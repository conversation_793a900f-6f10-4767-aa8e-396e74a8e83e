<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="36"
    height="20"
    fill="none"
    viewBox="0 0 36 20">
    <rect
      width="34.5"
      height="18.5"
      x=".75"
      y=".75"
      stroke="#9FA1A3"
      stroke-width="1.5"
      rx="5.25" />
    <path
      fill="#313438"
      d="M4 7a3 3 0 0 1 3-3h4a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H7a3 3 0 0 1-3-3V7Z"
      :opacity="(fill < 1 && 0.1) || 1" />
    <rect
      width="8"
      height="12"
      x="14"
      y="4"
      fill="#313438"
      :opacity="(fill < 2 && 0.1) || 1"
      rx="1" />
    <path
      fill="#313438"
      d="M24 5a1 1 0 0 1 1-1h4a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3h-4a1 1 0 0 1-1-1V5Z"
      :opacity="(fill < 3 && 0.1) || 1" />
  </svg>
</template>

<script lang="ts" setup>
interface Props {
  fill: 0 | 1 | 2 | 3;
}

defineProps<Props>();
</script>

<style lang="sass" module></style>
