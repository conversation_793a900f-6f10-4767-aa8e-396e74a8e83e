<script setup lang="ts">
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import VerificationSingleScale from "@/components/VerificationSingleScale.vue";
</script>

<template>
  <div>
    <UIFullScreenModal
      :title="$t('verification.verification')"
      :is-open="true"
      @close="$router.go(-1)">
      <template #content>
        <VerificationSingleScale />
      </template>
    </UIFullScreenModal>
  </div>
</template>
