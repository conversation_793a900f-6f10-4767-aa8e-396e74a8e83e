<script lang="ts" setup>
import CryptoScreen from "@/components/Transaction/Latest/CryptoScreen.vue";
import { computed, onBeforeUnmount, onMounted, ref, watchEffect } from "vue";
import { checkAutoBuyTransaction } from "@/composable/CardAutoBuy";
import type { TDepositByCryptoState } from "@/components/Transaction/Latest/types";
import TransactionMessage from "@/components/Transaction/Latest/Message.vue";
import { useUserStore } from "@/stores/user";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import type { TAccount } from "@/types/TAccount";
import { DEPOSIT_CRYPTO_MIN_FEE } from "../../../../constants/deposit_crypto_min_fee";
import { isNil } from "lodash";

const checkInterval = 10; // seconds

const props = defineProps<{
  account?: TAccount;
}>();

const emit = defineEmits(["on-submit"]);

const { userIsWarn, userFees, getUserFees } = useUserStore();
const defaultState: TDepositByCryptoState = {
  status: "pending",
  blockChainChange: "",
  balance: "",
  address: "",
};
const tState = ref<TDepositByCryptoState>(
  JSON.parse(JSON.stringify(defaultState))
);
const transactionCheckTimer = ref<any>(null);

const network = computed<string | undefined>(() => {
  return props.account?._currency?.iso_code === "USDT"
    ? "Tether USDT (TRC20)"
    : props.account?._currency?.name;
});

const checkBlockChainChange = async () => {
  clearInterval(transactionCheckTimer.value);

  transactionCheckTimer.value = setInterval(async () => {
    const res = await checkAutoBuyTransaction(tState.value.address);
    if (!res.status) {
      return;
    }
    if (tState.value.blockChainChange !== res.data) {
      tState.value.status = "progress";
    }
  }, checkInterval * 1000);
};

const checkBalanceChange = async (balance: number | string) => {
  clearInterval(transactionCheckTimer.value);

  transactionCheckTimer.value = setInterval(async () => {
    if (Number(balance) > Number(tState.value.balance)) {
      tState.value.status = "success";
    }
  }, checkInterval * 1000);
};

const setTransactionState = async () => {
  tState.value.address = String(props.account?.addresses[0]?.address) || "";
  tState.value.balance = String(props.account?.balance) || "";
  await checkAutoBuyTransaction(tState.value.address).then((res) => {
    tState.value.blockChainChange = res.data;
  });
};

getUserFees();

onMounted(() => {
  watchEffect(async () => {
    if (props.account && props.account?.id) {
      tState.value = JSON.parse(JSON.stringify(defaultState));
      await setTransactionState();

      if (tState.value.address) {
        await checkBlockChainChange();
      }
    }
    if (tState.value.status === "progress") {
      await checkBalanceChange(String(props.account?.balance));
    }
    if (tState.value.status === "success") {
      // emit("on-submit", true);
    }
  });
});

onBeforeUnmount(() => {
  clearInterval(transactionCheckTimer.value);
});
</script>

<template>
  <CryptoScreen
    :address="tState.address"
    :network="network"
    :status="tState.status" />
  <div
    v-if="['finished', 'success'].includes(`${tState.status}`)"
    class="mt-10">
    <UIButton
      class="w-full"
      color="green-solid"
      size="m"
      @click="emit('on-submit', true)">
      {{ $t("deposit.complete") }}
    </UIButton>
  </div>
  <div
    v-else
    class="flex flex-col gap-4 mt-10">
    <TransactionMessage
      :title="`${$t('label.attention')}!`"
      class="message"
      type="alert">
      <ul>
        <li>
          {{ $t("withdraw.toExternalMessage.p4") }}
        </li>
        <li>
          {{ $t("deposit.message-1") }}
        </li>
        <template v-if="!userIsWarn">
          <li
            v-if="
              props.account?._currency?.iso_code !== 'BTC' &&
              (isNil(userFees.deposit_fee_usdt) ||
                Number(userFees.deposit_fee_usdt) > 0)
            ">
            {{ $t("deposit.minFee", DEPOSIT_CRYPTO_MIN_FEE.initial) }}
          </li>
          <li
            v-if="props.account?._currency?.iso_code === 'BTC'"
            class="whitespace-pre-wrap">
            {{ $t("deposit.minFeePromo", DEPOSIT_CRYPTO_MIN_FEE.promo) }}
          </li>
        </template>
        <li v-else-if="props.account?._currency?.iso_code === 'BTC'">
          {{ $t("deposit.minFeeBtcPromo", DEPOSIT_CRYPTO_MIN_FEE.btcPromo) }}
        </li>
      </ul>
    </TransactionMessage>
    <TransactionMessage
      v-if="userIsWarn"
      :title="$t('transaction.depositWarning.attention')"
      class="message"
      type="alert">
      <p>
        {{ $t("transaction.depositWarning.attention_text_warn") }}
      </p>
    </TransactionMessage>
  </div>
</template>

<style lang="scss" scoped>
.message {
  & ul,
  p {
    @apply text-normal leading-[1.2];
  }

  & ul {
    @apply list-disc pl-4 flex flex-col gap-3;
  }
}
</style>
