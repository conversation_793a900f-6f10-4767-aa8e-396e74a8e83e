import { ref } from "vue";
import {
  defaultState,
  type TransferFormState,
  type TransferStrategy,
  type TransferStrategyInitParams,
} from "@/components/TransferModal/types";
import { useStrategyFactory } from "@/components/TransferModal/useTransferContext/factory/useStrategyFactory";

/*
 * Strategy for transfer from master accounts and cards to member card.
 */
export const masterAccountsAndCardsToMemberCardStrategy: TransferStrategy = (
  params: TransferStrategyInitParams
) => {
  const state = ref<TransferFormState>({
    ...defaultState,
  });

  const { initialize, searchFrom, searchTo, loadMoreFrom, loadMoreTo } =
    useStrategyFactory(
      {
        fromWhom: "user",
        fromWhat: "both",
        toWhom: "member",
        toWhat: "card",
      },
      state,
      params
    );

  initialize();

  return {
    state,
    searchFrom,
    searchTo,
    loadMoreFrom,
    loadMoreTo,
  };
};
