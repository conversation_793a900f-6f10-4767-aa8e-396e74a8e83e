<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { computed, ref } from "vue";
import Page from "@/components/ui/Page/Page.vue";

const icons = ref<string[]>([]);

icons.value = Object.keys(
  import.meta.glob("@/assets/svg/**/*.svg", { eager: true, as: "raw" })
);

const defaultPath = "/src/assets/svg/";
const parseFullPath = (path: string): string => {
  return path.replace(defaultPath, "");
};
const parseFilename = (path: string): string => {
  let normalPath = path.replace(defaultPath, "");
  normalPath = normalPath.replace(".svg", "");
  return normalPath.split("/")[1] || normalPath.split("/")[0];
};

const parsePath = (path: string): string | undefined => {
  const allowedList: string[] = [
    "icon",
    "menu",
    "cards",
    "cards-new",
    "cards-first",
    "alerts",
    "pattern",
    "gateway",
    "accounts",
  ];
  const normalPath = path.replace(defaultPath, "");
  if (normalPath.includes("/")) {
    const finalPath = normalPath.split("/")[0];
    if (allowedList.includes(finalPath)) {
      return finalPath;
    } else {
      return undefined;
    }
  } else {
    return "./";
  }
};

type TListIcon = {
  name: string;
  path?: string;
  fullPath: string;
  index: number;
};

const listIcons = computed(() => {
  if (!icons.value?.length) return [];
  const list: TListIcon[] = [];
  let index = 0;
  for (const path of icons.value) {
    const item = {
      name: parseFilename(path),
      path: parsePath(path),
      fullPath: parseFullPath(path),
      index: index,
    };
    if (item.path) {
      list.push(item);
      index++;
    }
  }
  return list;
});
</script>
<template>
  <Page>
    <div class="grid grid-cols-4 w-full">
      <div
        class="flex flex-none flex-row p-2"
        v-for="icon of listIcons"
        :key="icon.index">
        <div class="flex flex-none mr-2">
          <DynamicIcon
            v-if="icon.path"
            class="w-10 h-10"
            :name="icon.name"
            :path="icon.path === 'icon' ? '' : icon.path" />
        </div>
        <div class="flex flex-auto flex-col align-middle">
          <div class="flex flex-none">
            <span class="text-3 text-fg-primary">{{ icon.name }}</span>
          </div>
          <div class="flex flex-none">
            <span class="text-3 text-fg-secondary">{{ icon.path }}</span>
          </div>
        </div>
      </div>
    </div>
  </Page>
</template>
