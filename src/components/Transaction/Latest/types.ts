import type { TExchangeStatus } from "@/helpers/exchange";
import type BigNumber from "bignumber.js";
export type TTransactionStatus =
  | "pending"
  | "progress"
  | "success"
  | TExchangeStatus;

export type TTFromDirection = "card" | "account" | string;
export type TTToDirection = TTFromDirection | "external" | "user";
export type TTBaseType = "deposit" | "transfer" | "withdraw";
export type TTransactionBlockedState = {
  state: boolean;
  date: Date | null;
  duration: number;
};
type TTActive = {
  accountId?: number;
  cardId?: number;
};

export interface TTProps extends TTActive {
  type: TTransactionType;
  fromDirection?: TTFromDirection;
  fromId?: string | number;
  fromAmount?: string;
  toDirection?: TTToDirection | TDepositToDirection;
  toId?: string | number;
  toAmount?: string;
  isoCode?: "EUR" | "USD" | "USDT" | "BTC" | string;
  toEmail?: string;
  toAddress?: string;
}

export interface TDepositByCryptoState {
  status: TTransactionStatus;
  blockChainChange: string;
  balance: string;
  address: string;
}
export interface TDepositByAnyCryptoState {
  fromValue: BigNumber | number | string;
  toValue: BigNumber | number | string;
  status: TTransactionStatus | string;
  network: string;
  address: string;
  addressHelper?: {
    label?: string;
    value?: string;
  } | null;
  pending: boolean;
  rate: number;
  error: any;
}

// TODO: refactor types |>

// directions
export type TTransactionType =
  | "deposit-fiat"
  | "deposit-card"
  | "deposit"
  | "transfer"
  | "withdraw";
export type TTransactionFromDirection = "card" | "account";
export type TTransactionToDirection =
  | TTransactionFromDirection
  | "external"
  | "user";
export type TDepositToDirection = "account" | "external" | "crypto";

// default transaction config
interface TDefaultTransactionConfig {
  isSubmit?: boolean;
  isConfirm?: boolean;
  loading?: boolean;
  error: any; // TODO set error type
}
// default transaction props
export interface TDefaultTransactionProps {
  type: TTransactionType | string;
  toId?: string | number;
  toDirection?: TTransactionToDirection | TDepositToDirection | string;
  toAmount?: number | string;
  fromId?: number | string;
  fromDirection?: TTransactionFromDirection | string;
  fromAmount?: number | string;
  toEmail?: string;
  toAddress?: string;
}

export interface TTransactionQuery {
  type: TTransactionType | string;
  from_direction?: TTransactionFromDirection | string;
  to_direction?: TTransactionToDirection | TDepositToDirection | string;
  from_id?: string | number;
  to_id?: string | number;
  from_amount?: string;
  to_amount?: string;
  to_email?: string;
  to_address?: string;
}

// default transaction state
export interface TDefaultTransactionState
  extends TDefaultTransactionConfig,
    Omit<TDefaultTransactionProps, "type"> {
  step: number;
}

// transfer state
export interface TTransferState
  extends Omit<TDefaultTransactionState, "toEmail" | "toAddress"> {
  type: TTransactionType | string;
}

// transfer props
export interface TTransferProps
  extends Omit<
    TTransferState,
    "isSubmit" | "isConfirm" | "loading" | "error"
  > {}

// deposit state
export interface TDepositState
  extends Omit<
    TDefaultTransactionState,
    "toEmail" | "toAddress" | "fromDirection"
  > {}
// deposit props
export interface TDepositProps
  extends Omit<TDepositState, "isSubmit" | "isConfirm" | "loading" | "error"> {}

// withdraw state
export interface TWithdrawState
  extends Omit<TDefaultTransactionState, "toId"> {}

// withdraw props
export interface TWithdrawProps
  extends Omit<
    TWithdrawState,
    | "fromDirection"
    | "fromAmount"
    | "toId"
    | "isSubmit"
    | "isConfirm"
    | "loading"
    | "error"
  > {}

// withdraw config
export type TWithdrawConfig = {
  minFeeAmount: number; // in USD
};

export type TWithdrawToDirection = {
  id: string;
  title: string;
  icon: string;
  iconPath: string;
};

export type TDepositGateway = {
  id: number | string;
  type: string;
  title?: string;
  icon?: string;
  iso_code?: string;
};
