import type { <PERSON>a, StoryObj } from "@storybook/vue3";
import UIFormGroup from "./UIFormGroup.vue";
import { ref } from "vue";
import UITextInput from "../UITextInput/UITextInput.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

const meta = {
  title: "UI/UIFormGroup",
  component: UIFormGroup,
  tags: ["autodocs"],
  args: {
    label: "Label",
    required: true,
    actionLabel: "Action",
    helpText: "Helper Text",
    error: "",
  },
  render(args) {
    return {
      components: { UIFormGroup, UITextInput, DynamicIcon },
      setup() {
        const placeholder = ref("Here might be anything...");
        return { args, placeholder };
      },
      template: `
        <div class="max-w-64">
          <UIFormGroup v-bind="args">
            <template #label-icon>
              <DynamicIcon
                name="alert-circle"
                class="w-4 h-4" />
            </template>

            <UITextInput
              v-model="placeholder"
              class="w-full"
              size="m" />

            <template #helper-icon>
              <DynamicIcon
                name="alert-circle"
                class="w-4 h-4" />
            </template>
          </UIFormGroup>
        </div>
      `,
    };
  },
} satisfies Meta<typeof UIFormGroup>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {};
