<script setup lang="ts">
import cards, { getTypeTitleById } from "@/config/cards";

const { instantCardIssueSlug, instantCardIssueActivateBtnText } = cards;
import { useRouter } from "vue-router";
import { generateNumber } from "@/helpers/random";

import CardsIconIssue from "@/components/Cards/CardsIconIssue.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import { TrackerService } from "@/helpers/tracker/tracker.service";
import { inject } from "vue";
import { LocalStorageKey } from "@/constants/local_storage_key";

const trackingData: {} | undefined = inject("trackingData");
const router = useRouter();

const getNumberForIssueCard = (): string => {
  const storageKey = LocalStorageKey.LAST_DIGITS_ISSUE_CARD;
  if (typeof Storage !== "undefined") {
    const storageValue: string | null = localStorage.getItem(storageKey);
    if (storageValue) {
      return storageValue;
    } else {
      const digits: string = generateNumber(4);
      localStorage.setItem(storageKey, digits);
      return digits;
    }
  } else {
    return generateNumber(4);
  }
};

const onClick = async () => {
  if (trackingData) {
    await TrackerService.logEvent("Button", {
      ...trackingData,
      name: "Activate",
      location: "cards_block",
    });
  }
  await TrackerService.logEvent("card activate click", {
    slug: instantCardIssueSlug,
  }).catch((ex) =>
    console.warn("CreateCard/Form->TrackerService.logEvent", ex)
  );
  router.push(`/app/promo-cards/${instantCardIssueSlug}`);
};

const onClickIssueCard = async () => {
  if (trackingData) {
    await TrackerService.logEvent("Button", {
      page: "header",
      page_version: 0,
      name: "new_card",
      version: 0,
      location: "header",
    });
  }
  router.push({ path: "/app/promo-cards" });
};
</script>

<template>
  <div class="flex items-center justify-between gap-3 flex-wrap">
    <h6>{{ $t("Cards") }}</h6>
    <UiButton
      :title="$t('issue_a_card')"
      size="small"
      type="primary"
      icon="plus"
      class="w-full md:w-fit"
      :class="$style.root__issue"
      @click="onClickIssueCard()"
      data-cy="btn-issue-a-card" />
  </div>
  <div :class="$style.root">
    <div class="flex gap-2 justify-between items-start">
      <p class="text-lg font-semibold text-[#15191D] w-2/3 break-words">
        {{ getTypeTitleById(instantCardIssueSlug) }}
      </p>

      <CardsIconIssue
        :code="getNumberForIssueCard()"
        size="medium" />
    </div>

    <div class="flex items-center gap-1 mt-auto"></div>
    <p :class="[$style.balance]">0 $</p>
    <button
      :class="[$style.button]"
      @click.prevent="onClick">
      {{ $t(instantCardIssueActivateBtnText) }}
    </button>
  </div>
</template>

<style module lang="scss">
.root {
  @apply rounded-base bg-[#F1F2F4] p-5 cursor-pointer hover:bg-[#E8E9EB] relative;
  @apply flex flex-col min-h-[226px];
  max-width: 280px;

  &:hover .star {
    @apply flex;
  }
}

.root-first {
  @apply flex flex-col gap-4;
  max-width: 269px;
}

.star {
  @apply hidden z-20;
}

.clickOverlay {
  @apply absolute w-full h-full left-0 top-0 z-10;
}

.forceVisible {
  display: flex !important;
}

.card-hover-transition {
  transition: background-color cubic-bezier(0.4, 0, 0.2, 1) 0.15s;
}

.card-hover-transition:hover {
  --tw-border-opacity: 1;
  border-color: rgb(232 233 235 / var(--tw-border-opacity));
  transition-duration: 150ms;
}

.balance {
  @apply text-[22px] leading-7 text-[#15191D] mt-3;
}

.button {
  @apply mt-3 py-2 px-4 rounded text-white;
  background: #ff7d01;
}

.critical {
  @apply text-[#FF4747];
}
</style>
