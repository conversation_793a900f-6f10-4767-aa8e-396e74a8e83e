import { usePstFetch } from "@/composable/usePstFetch";
import type { UseFetchOptions } from "@vueuse/core";

type TUserCardCloseDeleteReq = {
  account_id: number;
};
export const useUserCardCloseDelete = (
  id: number,
  body: TUserCardCloseDeleteReq,
  useFetchOpts?: UseFetchOptions
) => {
  const url: string = `card/${id}`;
  return usePstFetch(url, useFetchOpts || {})
    .delete(body)
    .json<{ success: boolean }>();
};
