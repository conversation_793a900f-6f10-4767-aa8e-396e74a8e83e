<template>
  <UISideModal
    :title="$t('api-page.creating-api-key')"
    :is-open="props.isOpen"
    @close="close">
    <template #buttons>
      <div class="mr-3">
        <SupportTrigger />
      </div>

      <!-- <UIButton
        color="grey-free"
        size="m"
        icon-only
        @click="close">
        <DynamicIcon
          name="close"
          class="w-6 cursor-pointer" />
      </UIButton> -->
    </template>
    <template #content>
      <div class="flex w-full flex-col gap-10">
        <div class="flex flex-none flex-col">
          <div class="flex text-fg-secondary text-4 leading-5 mb-1">
            {{ $t("api-page.key-name") }}
          </div>
          <div class="flex">
            <UITextInput
              v-model="form.title"
              type="text"
              size="m"
              class="w-full"
              :max-length="20" />
          </div>
        </div>

        <div class="flex flex-none flex-col">
          <div class="flex text-fg-secondary text-4 leading-5 mb-1">
            {{ $t("api-page.access") }}
          </div>
          <div class="grid grid-cols-2 gap-2 w-full">
            <UIRadioButton
              v-model="form.abilities"
              :value="'read_only'">
              {{ $t("api-page.access-reading") }}
            </UIRadioButton>

            <UIRadioButton
              v-model="form.abilities"
              :value="'full_access'">
              {{ $t("api-page.access-reading-writing") }}
            </UIRadioButton>
          </div>
        </div>

        <div class="flex flex-col">
          <div class="flex text-4.5 leading-6 font-medium text-fg-primary">
            {{ $t("api-page.white-ip-addresses") }}
          </div>
          <div class="flex text-fg-secondary text-4 leading-5 mb-5">
            {{ $t("api-page.white-ip-addresses-text") }}
          </div>
          <div class="flex mb-2">
            <UITextInput
              v-model="formIpInput"
              type="text"
              size="m"
              class="w-full"
              :error="errorIpMsg"
              @key-up-enter="pushNewIp" />
          </div>
          <div class="flex flex-row flex-wrap gap-2">
            <div
              v-for="(item, index) of form.ipAdresses"
              :key="item"
              class="item-ip-address"
              @click="form.ipAdresses.splice(index, 1)">
              <div class="flex">{{ item }}</div>
              <div class="flex">
                <DynamicIcon
                  name="close"
                  class="w-4 h-4" />
              </div>
            </div>
          </div>
        </div>
        <div class="flex mt-10">
          <UIButton
            size="m"
            color="black"
            class="w-full"
            :is-loading="isLoadingSendForm"
            :disabled="!form.title"
            @click="submit">
            <span class="text-white">{{ $t("api-page.create-api-key") }}</span>
          </UIButton>
        </div>
      </div>
    </template>
  </UISideModal>
</template>

<script setup lang="ts">
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UITextInput from "@/components/ui/UITextInput/UITextInput.vue";
import { reactive, ref } from "vue";
import UIRadioButton from "@/components/ui/UIRadioButton/UIRadioButton.vue";
import {
  type TUseUserApiKeyPostReq,
  useUserApiKeyPost,
} from "@/composable/API/useUserApiKeyPost";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { useI18n } from "vue-i18n";
import SupportTrigger from "@/components/SupportTrigger.vue";

import { useValidator } from "@/composable/useValidator";

const { validateIPv4 } = useValidator();

interface Props {
  isOpen: boolean;
}

const formIpInput = ref<string>("");

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
});

type TAbilities = "full_access" | "read_only";

const form = reactive({
  title: "" as string,
  abilities: "read_only" as TAbilities,
  ipAdresses: [] as string[],
});

const errorIpMsg = ref<string>("");
const pushNewIp = () => {
  const ip: string = formIpInput.value;
  if (form.ipAdresses.includes(ip)) {
    formIpInput.value = "";
    return;
  }
  const resValidate = validateIPv4(ip);
  if (resValidate === null) {
    errorIpMsg.value = "";
    form.ipAdresses.push(ip);
    formIpInput.value = "";
  } else {
    errorIpMsg.value = resValidate;
  }
};

const { t } = useI18n();
const submit = async () => {
  isLoadingSendForm.value = true;

  const reqData: TUseUserApiKeyPostReq = {
    abilities: [form.abilities],
    ip_address: form.ipAdresses,
    title: form.title,
  };
  const { data } = await useUserApiKeyPost(reqData);
  const apiKeyText = data.value?.data?.api_key;
  if (apiKeyText) {
    close();
    resetForm();
    success(apiKeyText);
  } else {
    useCallToast({
      title: t("errors.universal-request-error"),
      options: {
        type: TOAST_TYPE.ERROR,
        id: "create-api-key-modal",
      },
    });
  }

  isLoadingSendForm.value = false;
};

const resetForm = () => {
  form.abilities = "read_only";
  form.ipAdresses = [];
  form.title = "";
};

const isLoadingSendForm = ref<boolean>(false);

const close = () => {
  emit("close");
};

const success = (apiKeyText: string) => {
  emit("success", apiKeyText);
};

const emit = defineEmits<{
  close: [];
  success: [apiKeyText: string];
}>();
</script>

<style scoped>
.item-ip-address {
  @apply flex flex-row bg-bg-level-2 pl-1.5 pt-1 pr-1 pb-1 text-fg-primary text-3.5 leading-4 gap-2 rounded
  items-center cursor-pointer hover:bg-bg-level-2-hover active:bg-bg-level-2-clicked transition-all;
}
</style>
