<script lang="ts" setup>
import GridItem from "@/components/Cards/StandardCreateCard/Stages/SelectBin/GridItems/GridItem/Index.vue";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import type { IBinInfo } from "@/components/Cards/StandardCreateCard/Stages/SelectBin/types";
import type { TCardTariff } from "@/types/user/user.types";

export interface Props {
  bins: IBinInfo[] | undefined;
  binsPrivate: IBinInfo[] | undefined;
  selectedTariff: TCardTariff;
  privateCashback: any;
  cashbackPercent: string;
  feeTopUpPrivateForCard: number;
  //subscriptionStatus: boolean;
  subscriptionLevel?: string;
}

defineProps<Props>();
const emit = defineEmits(["onSelectedBin"]);

const { subscriptionsStatus } = useSubscriptionsInfo();

const onSelectBin = (bin: string, slug: string) => {
  emit("onSelectedBin", bin, slug);
};
</script>

<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 flex-wrap">
    <template
      v-for="(bin, index) of bins"
      :key="index">
      <GridItem
        :bin="bin"
        :tariff="
          bin.private
            ? {
                ...selectedTariff,
                card_price: 0,
                fee_topup: feeTopUpPrivateForCard,
              }
            : selectedTariff
        "
        :subscription="bin.private ? true : subscriptionsStatus"
        :cashback="bin.private ? privateCashback : cashbackPercent"
        :subscription-level="subscriptionLevel"
        @on-selected-bin="onSelectBin(bin.bin, bin.slug)" />
    </template>

    <template v-if="!subscriptionsStatus">
      <GridItem
        v-for="(bin, index) of binsPrivate"
        :key="index"
        :bin="bin"
        :tariff="{
          ...selectedTariff,
          card_price: 0,
          fee_topup: feeTopUpPrivateForCard,
        }"
        :subscription="true"
        :cashback="privateCashback"
        :subscription-level="subscriptionLevel"
        @on-selected-bin="onSelectBin(bin.bin, bin.slug)" />
    </template>
  </div>
</template>
