<script setup lang="ts">
import { useI18n } from "vue-i18n";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

const { locale } = useI18n();
interface Props {
  //Fee
  feePercent?: number;
  feeValue?: number | string;
  feeCurrency?: string;
  feeFilled?: boolean;

  //Rate
  rate: number | string;
  fromCurrency: string;
  toCurrency: string;

  isTransferAccountToCard?: boolean;
}

const props = defineProps<Props>();
</script>
<template>
  <div
    :class="$style['root']"
    class="flex flex-col w-full">
    <!-- Commission -->
    <div class="flex flex-none flex-row w-full">
      <div
        class="flex flex-auto flex-row items-center"
        :class="$style['root__title']">
        <div class="flex flex-none">
          {{ $t("label.transactionFee") }}
        </div>
        <div
          v-if="
            isTransferAccountToCard && props?.feePercent && props.feePercent > 0
          "
          class="flex flex-none ml-1">
          <DynamicIcon
            v-tooltip="{
              placement: 'bottom',
              triggers: ['hover', 'focus', 'touch'],
              distance: 7,
              content: $t('cards.at-least-1-usdt'),
            }"
            name="info"
            class="text-fg-tertiary w-4 h-4" />
        </div>
      </div>
      <div
        class="flex flex-none"
        :class="$style['root__value']">
        <template
          v-if="
            props.feePercent &&
            props.feeValue !== undefined &&
            props.feePercent > 0
          ">
          {{ props.feePercent.toFixed(1) + "%" }}
        </template>
        <template v-else>
          {{ $t("label.feeNotCharged") }}
        </template>
      </div>
    </div>

    <!-- Rate -->
    <div class="flex flex-none flex-row w-full">
      <div
        class="flex flex-auto"
        :class="$style['root__title']">
        <span>{{ $t("label.exchangeRate") }}</span>
      </div>
      <div
        class="flex flex-none"
        :class="$style['root__value']">
        <span>1 {{ props.fromCurrency }}</span>
        <span>&nbsp;=&nbsp;</span>
        <span>{{ props.rate }} {{ props.toCurrency }}</span>
      </div>
    </div>
  </div>
</template>
<style lang="scss" module>
.root {
  grid-column: span 2 / span 2;
  display: flex;
  padding: var(--sys-spacing-calculate-inset, 16px);
  flex-direction: column;
  align-items: flex-start;
  gap: var(--sys-spacing-calculate-gap, 28px);
  align-self: stretch;
  border-radius: var(--sys-shape-rectagnle, 6px);
  background: var(--sys-color-calculate-bg, #f1f2f4);

  &__title {
    color: var(--sys-color-calculate-fg-secondary, #686a6e);

    /* sys/text/calculate */
    font-family: ALS Hauss VF;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 120% */
  }

  &__value {
    color: var(--sys-color-calculate-fg-primary, #313438);

    /* sys/text/calculate */
    font-family: ALS Hauss VF;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 120% */
  }
}
</style>
