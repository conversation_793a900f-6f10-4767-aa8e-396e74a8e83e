import type { IServiceResponse } from "@modules/services/base/types";
import type { TPaginationMeta } from "@/types/api/TPaginationMeta";
import type { TValueOf } from "@/types/TValueOf";
import type { TTeamMember, TTeamMemberDashboard } from "@/types/teamMember";
import type { TTeamMembersRequest } from "@/types/TTeamMembersRequest";

export const MEMBER_DASHBOARD_STATUS = {
  REATED: 5,
  APPROVED: 10,
  REJECTED: 20,
  DELETED: 30,
} as const;

export const MEMBER_DASHBOARD_SORT = {
  BY_MEMBER: "member",
  BY_CARDS_COUNT: "cards_count",
  BY_PAYMENTS_DAY: "payments_by_day",
  BY_PAYMENTS_MONTH: "payments_by_month",
  BY_BALANCE_CARDS: "balance_cards",
  BY_BALANCE_ACCOUNTS: "balance_accounts",
} as const;

export type TMemberDashboardSort = TValueOf<typeof MEMBER_DASHBOARD_SORT>;
export type TMemberDashboardStatus = TValueOf<typeof MEMBER_DASHBOARD_STATUS>;

export interface IUserBusinessRequestConfig {
  dashboard: {
    members?: string;
    direction?: "desc" | "asc";
    per_page?: number;
    sort?: TMemberDashboardSort;
    search?: string;
    status?: TMemberDashboardStatus;
    page?: number;
    for?: "master";
  };
  members: {
    page?: number;
    sort?: string;
  };
  membersApproveList: {
    page?: number;
    count?: number;
  };
  inviteMember: {
    member_email: string;
    member_name: string;
  };
  deleteInviteMember: number;
  deleteMember: number;
  updateMember: {
    member_name: string;
  };
}

export interface IUserBusinessResponse {
  dashboard: {
    data: TTeamMemberDashboard[];
    meta: TPaginationMeta;
  };
  members: {
    data: TTeamMember[];
    meta: TPaginationMeta;
  };
  membersApproveList: {
    data: TTeamMembersRequest[];
    meta: TPaginationMeta;
  };
  inviteMember: {
    success: boolean;
  };
  deleteInviteMember: {
    success: boolean;
  };
  deleteMember: {
    success: boolean;
  };
  updateMember: {
    success: boolean;
  };
}

export interface IUserBusinessApiService {
  dashBoard(
    config: IUserBusinessRequestConfig["dashboard"]
  ): Promise<IServiceResponse<IUserBusinessResponse["dashboard"], unknown>>;

  getMembers(
    config: IUserBusinessRequestConfig["members"]
  ): Promise<IServiceResponse<IUserBusinessResponse["members"], unknown>>;

  getMembersApproved(
    config: IUserBusinessRequestConfig["membersApproveList"]
  ): Promise<
    IServiceResponse<IUserBusinessResponse["membersApproveList"], unknown>
  >;

  postInviteMember(
    config: IUserBusinessRequestConfig["inviteMember"]
  ): Promise<IServiceResponse<IUserBusinessResponse["inviteMember"], unknown>>;

  postDeleteInviteMember(
    config: IUserBusinessRequestConfig["deleteInviteMember"]
  ): Promise<
    IServiceResponse<IUserBusinessResponse["deleteInviteMember"], unknown>
  >;

  deleteMember(
    config: IUserBusinessRequestConfig["deleteMember"]
  ): Promise<IServiceResponse<IUserBusinessResponse["deleteMember"], unknown>>;

  updateMember(
    memberId: number,
    config: IUserBusinessRequestConfig["updateMember"]
  ): Promise<IServiceResponse<IUserBusinessResponse["updateMember"], unknown>>;
}
