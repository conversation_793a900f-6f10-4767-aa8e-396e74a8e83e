<script lang="ts" setup>
/* eslint-disable max-len */
import BorderedTable from "@/components/ui/Table/BorderedTable.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import SelectMember from "@/components/Teams/Dashboard/Estimate/Filters/SelectMember.vue";
import TableHeader from "@/components/Teams/Dashboard/Estimate/Table/Header.vue";
import TableItem from "@/components/Teams/Dashboard/Estimate/Table/Item.vue";
import TableSkeleton from "@/components/Teams/Dashboard/Estimate/TableSkeleton.vue";
import { useTeamDashboard } from "@/composable/Team/Dashboard";
import { useMembers } from "@/composable/Team/TeamBuilding";
import PaymentPagination from "@/components/ui/Pagination/PaymentsPagination.vue";
import { usePagination } from "@/composable/Pagination";

import TopUpMemberWallet from "@/components/Teams/Modals/TopUpMemberWallet.vue";
import WithdrawFromTheUser from "@/components/Teams/Modals/WithdrawFromTheUser.vue";
import TeamMembersNotFoundAdd from "@/components/Teams/TeamMembersNotFoundAdd.vue";
import AddMember from "../Modals/AddMemberNew/AddMemberNew.vue";

import { useI18n } from "vue-i18n";
import { ref, computed } from "vue";
import { useQueryClient } from "vue-query";
import { teamDashboardKey } from "@/config/queryConfig";
import UiTransition from "@/components/ui/UITransition.vue";

const { t } = useI18n();
const queryClient = useQueryClient();
const sortField = ref<string>("member");
const sortDirection = ref<"asc" | "desc">("desc");
const withdrawId = ref<number>(-1);
const depositId = ref<number>(-1);
const addMember = ref<boolean>(false);
const filteredMembers = ref<Array<any>>([]);
const { page, setPage } = usePagination();
const enabled = ref<boolean>(false);
const membersData = useMembers({
  perPage: ref(100),
  withMaster: true,
  refetchOnWindowFocus: false,
  staleTime: 1000 * 5,
  enabled,
});

// computed

// get member for table
const filteredMembersToString = computed<string>(() => {
  return filteredMembers.value.toString();
});

const { data: dashboardMasterData, isFetching: dashboardMasterLoading } =
  useTeamDashboard({
    members: ref<string>(""),
    sort: sortField,
    direction: sortDirection,
    page: ref<number>(1),
    per_page: ref<number>(1),
    queryClient,
    for: ref<string>("master"),
    enabled: ref<boolean>(true),
  });

// Get members data for table
const { data: dashboardData, isFetching: dashboardLoading } = useTeamDashboard({
  members: filteredMembersToString,
  sort: sortField,
  direction: sortDirection,
  page,
  per_page: ref<number>(25),
  queryClient,
  enabled: ref<boolean>(true),
});

const members = computed(() => {
  const membersWithoutMaster = dashboardData.value.data.filter(
    (member: { isMaster: boolean }) => !member.isMaster
  );
  return filteredMembers.value.length
    ? [...dashboardData.value.data].sort((x) => (x.isMaster ? -1 : 1))
    : [...dashboardMasterData.value.data, ...membersWithoutMaster];
});

const selectedMember = computed(() =>
  dashboardData.value.data.find((item: any) =>
    [withdrawId.value, depositId.value].includes(item.id)
  )
);

const isFetching = computed(() => {
  return dashboardMasterLoading.value || dashboardLoading.value;
});

// methods
const onAddMemberToggle = () => (addMember.value = !addMember.value);
const onClose = (invalidate: boolean) => {
  withdrawId.value = -1;
  depositId.value = -1;
  if (invalidate) {
    queryClient.invalidateQueries([teamDashboardKey]);
  }
};
const onWithdrawSelect = (id: number) => (withdrawId.value = id);
const onDepositSelect = (id: number) => (depositId.value = id);
const onUpdateField = (fieldCode: string) => {
  // set sort direction to default
  sortDirection.value = "asc";
  sortField.value = fieldCode;
};
const onUpdateDirection = (direction: "asc" | "desc") =>
  (sortDirection.value = direction);
const template: string = `
  font-granate
  min-h-[48px]
  grid-cols-1
  md:grid-cols-[minmax(150px,_270px)_minmax(80px,_270px)_56px_minmax(100px,_250px)_minmax(100px,_250px)_minmax(100px,_104px)] items-center px-3.5 py-3.5 md:py-2.5 gap-3`;
// ui config
</script>

<template>
  <div :class="$style.root">
    <template v-if="!isFetching">
      <template v-if="membersData.data.value.data.length > 1">
        <!--       info adn add member -->
        <div :class="$style.head">
          <div :class="$style['head-title']">
            <h5>
              {{ $t("team.dashboard.estimate.title") }}
            </h5>
            <span v-if="membersData.data.value.data.length">
              {{
                t("team.membersCount", {
                  n: membersData.data.value.data.length - 1,
                })
              }}
            </span>
          </div>
          <UiButton
            :title="$t('btn.addTeamMember')"
            icon="add"
            type="light"
            :class="$style['head-button']"
            :disabled="!!dashboardLoading"
            @click="onAddMemberToggle" />
        </div>

        <!--    search-->
        <SelectMember
          :selected="filteredMembers"
          class="w-full" />

        <!--  table  -->
        <BorderedTable
          v-if="members.length"
          class="w-full">
          <TableHeader
            :template="template"
            :sort-field="sortField"
            :sort-direction="sortDirection"
            @update:field="onUpdateField"
            @update:direction="onUpdateDirection" />

          <TableItem
            v-for="item in members"
            :key="`team-member-${item.id}`"
            :data="item"
            :disabled="!!dashboardLoading"
            :template="template"
            @withdraw="onWithdrawSelect"
            @deposit="onDepositSelect" />
        </BorderedTable>

        <PaymentPagination
          v-if="
            Math.round(
              dashboardData?.meta?.total / dashboardData?.meta?.per_page
            ) > 1
          "
          :count="
            Math.round(
              dashboardData?.meta?.total / dashboardData?.meta?.per_page
            )
          "
          :page="page"
          class="w-fit"
          :loading="dashboardLoading"
          bordered
          @change="setPage" />
      </template>

      <!--  Nothing found  -->
      <TeamMembersNotFoundAdd
        v-else
        class="pt-8 mx-auto" />
    </template>
    <TableSkeleton v-else />
    <!--  Modals  -->
    <Teleport to="body">
      <UiTransition :name="'fade-slide-up'">
        <AddMember
          v-if="addMember"
          @close="onAddMemberToggle" />
      </UiTransition>
    </Teleport>
    <Portal to="modals">
      <TopUpMemberWallet
        v-if="depositId > -1"
        :data="selectedMember"
        @close="onClose" />

      <WithdrawFromTheUser
        v-if="withdrawId > -1"
        :data="selectedMember"
        @close="onClose" />
    </Portal>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply flex flex-col gap-5 font-granate;
}

.head {
  @apply w-full flex items-start justify-between;

  &-title {
    @apply flex flex-col;

    & h5 {
      @apply font-medium;
    }

    & span {
      @apply text-neutral-600 text-[15px];
    }
  }

  &-button {
    @apply pl-3 pr-4 py-[10px] text-[15px] font-normal;

    & svg {
      @apply w-6 h-6;
    }
  }
}
</style>
