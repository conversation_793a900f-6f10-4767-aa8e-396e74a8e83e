<template>
  <Button
    :type="props.type"
    :disabled="props.disabled"
    :title="btnTitle"
    :size="props.size"
    icon="copy"
    :padding="props.padding"
    :loading="props.loading"
    @click="onCopyHandler" />
</template>

<script lang="ts" setup>
import Button from "./Button.vue";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import type { IButtonCopyProps } from "./types";

const props = defineProps<IButtonCopyProps>();
const btnTitle = ref<string>("");
const { t } = useI18n();
const onCopyHandler = () => {
  navigator.clipboard
    .writeText(props.value ? String(props.value) : "")
    .catch((ex) =>
      console.warn(
        "ButtonCopy->onCopyHandler->navigator.clipboard error handled: ",
        ex
      )
    );

  if (props.title) {
    btnTitle.value = t("Copied");
    setTimeout(() => {
      btnTitle.value = props.title || "";
    }, 1000);
  }
};
onMounted(() => {
  btnTitle.value = props.title || "";
});
</script>
