/**
 * Calculates the top-up fee based on the provided amount, fee percentage, and minimum fee.
 *
 * @category Helpers
 * @param {number} [amount=0] - The amount of money to top up.
 * @param {number} [feeTopUpPercent=0] - The percentage of the top-up fee.
 * @param {number} [minFee=0] - The minimum top-up fee.
 * @returns {number} - The calculated top-up fee, ensuring it is at least the minimum fee.
 */
export function calcTopUpFee(
  amount: number,
  feeTopUpPercent: number,
  minFee: number = 0
): number {
  if (feeTopUpPercent === 0) return 0;
  if (amount === 0) return minFee;
  const fee = amount / (1 - feeTopUpPercent / 100) - amount;
  return fee < minFee ? minFee : fee;
}
