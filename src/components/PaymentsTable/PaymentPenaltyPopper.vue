<script setup lang="ts">
import { ref } from "vue";
import { usePenalty } from "@/composable/usePenalty";
import type { TPenaltyType } from "@/types/api/TTransactionResource";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UiCollapsible from "@/components/ui/UICollapsible/UICollapsible.vue";

const props = defineProps<{
  penaltyType: string | null;
  penaltyAmount: string | null;
}>();

const { currentPenaltyInfo } = usePenalty(props.penaltyType as TPenaltyType);

const isShowPenaltyDetails = ref(false);

const onPenaltyDetailsClick = () => {
  isShowPenaltyDetails.value = !isShowPenaltyDetails.value;
};
</script>

<template>
  <div
    v-if="currentPenaltyInfo"
    class="penalty"
    @click="onPenaltyDetailsClick">
    <div class="flex items-center gap-2">
      <DynamicIcon
        :class="[isShowPenaltyDetails ? 'rotate-0' : 'rotate-180']"
        class="w-5 h-5 transition-transform shrink-0"
        name="chevron" />
      <div class="grow font-medium text-4 leading-5">
        {{ currentPenaltyInfo.title }}
      </div>
      <div class="whitespace-nowrap font-medium text-4 leading-5">
        {{ props.penaltyAmount }} $
      </div>
    </div>
    <UiCollapsible
      open-class="mt-3"
      :is-open="isShowPenaltyDetails">
      <div class="flex flex-col gap-4">
        <div class="text-3.5 leading-4">
          {{ currentPenaltyInfo.text }}
        </div>
        <a
          class="text-3.5 leading-4 underline transition-colors"
          :href="currentPenaltyInfo.url"
          target="_blank"
          @click.stop>
          {{ $t("label.learnMore") }}
        </a>
      </div>
    </UiCollapsible>
  </div>
</template>

<style scoped lang="scss">
.penalty {
  @apply mt-2 px-4 py-3 rounded transition-colors cursor-pointer bg-bg-red-light;
}
</style>
