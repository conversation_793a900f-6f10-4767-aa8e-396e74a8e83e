<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import SelectWithSearch from "@/components/ui/Select/SelectWithSearch.deprecated.vue";
import Tag from "@/components/ui/Tags/Tag.vue";
import InputBase from "@/components/ui/Input/InputBase.vue";

import type { TSelectOption } from "@/components/PaymentsTable/types";

import { computed, ref } from "vue";
import { useMembers } from "@/composable/Team/TeamBuilding";
import type { TTeamMember } from "@/types/teamMember";

type TOption = TSelectOption;
interface Props {
  selected: Array<TOption["code"]>;
}

const enabled = ref<boolean>(false);
const members = useMembers({
  perPage: ref(100),
  withMaster: true,
  refetchOnWindowFocus: false,
  enabled,
});
const props = defineProps<Props>();
const emit = defineEmits(["change"]);
const search = ref<string>("");

const enableLoadData = () => (enabled.value = true);
const onChangeOption = (code: TOption["code"]) => {
  const list = props.selected;

  if (list.includes(code)) {
    const index = list.indexOf(code);
    if (index > -1) {
      list.splice(index, 1);
    }
  } else {
    list.push(code);
  }

  emit("change", list);
};
const onSearchSet = (event: Event & { target: HTMLInputElement }) =>
  (search.value = event.target.value?.trim());
const onClose = () => (search.value = "");

const cardOptions = computed<Array<TOption>>(() => {
  return (
    members.data.value?.data?.map((item: TTeamMember) => {
      return {
        code: (item?.id && String(item.id)) || "",
        title: item?.name || item?.email || "Unknown member",
      };
    }) || []
  );
});
const filteredOptions = computed<Array<TOption>>(() =>
  cardOptions.value.filter((option: TOption) => {
    try {
      const regExp = new RegExp(search.value.toLowerCase());

      return regExp.test(option.title.toLowerCase());
    } catch (ex) {
      return false;
    }
  })
);
</script>

<template>
  <SelectWithSearch
    :values="selected"
    :placeholder="$t('payments.filters.filter.member.placeholder')"
    :loading="members.isFetching.value"
    :with-search="true"
    :options="filteredOptions"
    @select="onChangeOption"
    @close="onClose"
    @click="enableLoadData">
    <template #search>
      <InputBase
        :value="search"
        type="text"
        :placeholder="$t('search')"
        :with-input-wrapper="false"
        @input="onSearchSet">
        <template #leftIcon>
          <DynamicIcon
            name="search"
            class="w-5 h-5 min-w-[20px]" />
        </template>
      </InputBase>
    </template>

    <Tag
      v-for="option in cardOptions.filter(
        (card: TSelectOption) => selected.includes(card.code)
      )"
      :key="`card-select-${option.code}`"
      :title="option.title"
      color="default"
      with-delete
      @click.stop="() => null"
      @delete="() => onChangeOption(option.code)" />
  </SelectWithSearch>
</template>

<style lang="sass" module></style>
