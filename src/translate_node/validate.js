const validators = [
  require("./validators/keys_not_used"),
  // require("./validators/compare_keys"),
  //require("./validators/how_to_use_keys"),
  //require("./validators/check_exists_keys")
];

const main = async () => {
  for (const validator of validators) {
    await validator.run();
    if (validator.error) {
      console.error(validator.getName(), "- error");
      return;
    } else {
      console.log(validator.getName(), "- success");
    }
  }
};

main();
