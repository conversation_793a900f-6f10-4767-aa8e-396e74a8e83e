<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import Modal from "@/components/ui/Modal/Modal.vue";
import ModalScreen from "@/components/ui/Modal/ModalScreen.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import Warning from "@/components/ui/Warning/Warning.vue";
import Filters from "@/components/Cards/CreateCardOldDesign/Filters.vue";
import Form from "@/components/Cards/CreateCardOldDesign/Form.vue";
import CreateCardItem from "@/components/Cards/CreateCardOldDesign/CreateCardItem.vue";
import InfoStep from "@/components/Settings/Verification/Steps/InfoStep.vue";
import Screen from "@/components/Settings/Verification/Steps/Screen.vue";
import ExperimentsService from "@/services/ExperimentsService";

import { useState } from "@/helpers/utilities";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import { getCardForIssue, getPremiumCard } from "@/config/cards";
import { useI18n } from "vue-i18n";
import { CardService } from "@modules/services/card";
import { useCardsStore } from "@/stores/cards";
import { loggerConsole } from "@/helpers/logger/Logger";
import { ExceptionCause, ExceptionReason } from "@/config/Exception";
import { UserService } from "@modules/services/user";
import { useAxios } from "@/helpers";
import { isNil } from "@/helpers/other";
import {
  dateIsInvalid,
  getCorrectDate,
  getMysqlDate,
  minBirthDay,
} from "@/helpers/time";
import { VerificationService } from "@modules/services/verification";
import Loader from "@/components/ui/Loader/Loader.vue";
import { useUserAccounts } from "@/composable/useUserAccounts";
import { RouteName } from "@/constants/route_name";
import { useSubscriptionsInfoGet } from "@/composable/API/useSubscriptionsInfoGet";
import type {
  TVerification,
  TVerificationErrors,
  TVerificationPayload,
  TVerificationStep,
} from "@/types/verification/verification";

onMounted(() => {
  if (ExperimentsService.getStatus("activateExperiment") === "2") {
    router.push({ name: RouteName.CREATE_CARD });
  }
});

// system
const { t } = useI18n();
const logger = loggerConsole();
const router = useRouter();
const userStore = useUserStore();
const { isTeamMember } = userStore;
const userIsWarn = computed<boolean>(() => !!userStore?.user?.show_warn);
const cardStore = useCardsStore();
const cardsStore = useCardsStore("cards-accounts");
const route = useRoute();
const { params } = route;
// data
const activeAutoBuy = ref<any | null>(null);
const activeFilters = ref<Array<number>>([200]);
const userTariff = ref<Array<any>>([]);
const userSpecial = ref<any>(null);
const bins = ref<{ [key: string]: any }>({});
const selectedCardForIssue = ref<any>(null);
const showModal = ref<boolean>(false);
const cardIssueFormRef = ref<any>(null);
const needCloseAutoBuy = ref<boolean>(true);
const hideTitle = ref<boolean>(false);
const step = ref<1 | 2>(1);
const loading = ref<boolean>(false);
const kycData = ref<Partial<TVerification>>({});
const errorTouchedFields = ref<Array<keyof TVerification>>([]);
const showKycLimit = ref<boolean>(false);
const [isLoading, setIsLoading] = useState<boolean>(false);

const { accounts } = useUserAccounts({
  selectUsdAccount: true,
});

const prepaymentAvailable = computed<boolean>(() => {
  if (userIsWarn.value) return false;
  const userHasCards = cardsStore.state.total !== 0;
  const userHasMoney = accounts.value?.find((item: any) => item.balance > 0);
  return !userHasCards && !userHasMoney && !isTeamMember;
});

if (prepaymentAvailable.value) {
  if (ExperimentsService.getStatus("newCreate") === "1") {
    router.push({ name: RouteName.CREATE_CARD });
  }
}

// computed
const kycErrors = computed<Partial<TVerificationErrors>>(() => {
  return {
    first_name: {
      error:
        isNil(kycData.value?.first_name) ||
        (kycData.value?.first_name
          ? kycData.value.first_name.length < 3
          : true),
      touched: errorTouchedFields.value.includes("first_name"),
    },
    last_name: {
      error:
        isNil(kycData.value?.last_name) ||
        (kycData.value?.last_name ? kycData.value.last_name.length < 3 : true),
      touched: errorTouchedFields.value.includes("last_name"),
    },
    birthday: {
      error:
        isNil(kycData.value?.birthday) ||
        kycData.value?.birthday?.length === 0 ||
        (!!kycData.value?.birthday &&
          (kycData.value.birthday.length !== 10 ||
            dateIsInvalid(
              new Date(getCorrectDate(kycData.value.birthday, "/"))
            ) ||
            minBirthDay(18) <=
              new Date(getCorrectDate(kycData.value.birthday, "/")) ||
            minBirthDay(150) >=
              new Date(getCorrectDate(kycData.value.birthday, "/")))),
      touched: errorTouchedFields.value.includes("birthday"),
    },
    country_id: {
      error: isNil(kycData.value?.country_id),
      touched: errorTouchedFields.value.includes("country_id"),
    },
  };
});
const isNeedWelcomeStep = computed<boolean>(() => userStore.actual === "");
const premiumCard = computed<any>(() => getPremiumCard(t));
const includePromo = computed<boolean>(() => {
  const promoFilters = [200, 206]; // Filters for show promo

  return (
    activeFilters.value.some((v) => promoFilters.includes(v)) ||
    activeFilters.value.length === 0
  );
});
const cardsForIssue = computed<Array<any>>(() => {
  const cards = getCardForIssue(t);

  return cards.map((card: any) => {
    // 1. Get user tariff and set allowed for user
    const cardUserTariff = userTariff.value?.find(
      (userTariff: any) => userTariff.slug === card.code
    );
    if (cardUserTariff) {
      card.is_allowed_for_user = cardUserTariff?.is_allowed_for_user;
    }

    // 2.
    if (card.not_issuable === true) {
      return card;
    }
    if (bins.value[card.code]?.length === 0) {
      card.available = false;
      return card;
    }
    card.available = true;

    return card;
  });
});

const cardsWithBinsBadges = computed<Array<any>>(() => {
  if (Object.keys(bins.value).length === 0) {
    return cardsForIssue.value;
  }

  return cardsForIssue.value.map((card: any) => {
    // get bins count
    const binsCount =
      card.code in bins.value ? bins.value[card.code].length : 0;

    // configure title
    const title = `${t("cards.create.badgeIn")} ${binsCount} ${t(
      "cards.create.badgeBins"
    )}`;

    // badge object
    const badge = {
      title,
      icon: "bolt",
      bg: "yellow",
    };

    // if binsCount greater than 2 and is not universal
    if (binsCount >= 2 && card.code !== "all") {
      card.badges.push(badge);
    }

    return card;
  });
});

const filteredCards = computed(() =>
  cardsWithBinsBadges.value.filter((card: any) => {
    if (activeFilters.value.length === 0) {
      return true;
    }
    /* eslint-disable */
    const method =
      activeFilters.value.length === 1
        ? (card: any) =>
          card.filters.some((v: any) => activeFilters.value.includes(v))
        : (card: any) =>
          activeFilters.value.every((v: any) => card.filters.includes(v));

    return method(card);
    /* eslint-enable */
  })
);

// methods
const getErrorExist = (step: TVerificationStep) => {
  let stepKeys: Array<keyof TVerification> = [];

  if (step === "info") {
    stepKeys = ["country_id", "first_name", "last_name", "birthday"];
  }

  const entries = Object.entries(kycErrors.value || {});
  const filteredEntries = entries.filter((entry: any) =>
    stepKeys.includes(entry[0])
  );

  return !!filteredEntries.find((entry) => entry[1]?.error);
};
const onAutoBuySent = (id: number) => (activeAutoBuy.value = { id });
const onSendWelcomeStep = async () => {
  loading.value = true;

  const data: TVerificationPayload = {
    step: "welcome",
    first_name: kycData.value?.first_name || "",
    last_name: kycData.value?.last_name || "",
    birthday: kycData.value?.birthday
      ? getMysqlDate(kycData.value.birthday, "-")
      : "",
    actual_country_id: kycData.value?.actual_country_id || undefined,
    country_id: kycData.value?.country_id || undefined,
    is_not_located_in_country:
      kycData.value?.is_not_located_in_country || false,
  };

  const result = await VerificationService.verification(data);

  if (!result.status) {
    useCallToast({
      title: t("toast.error"),
      body: result.message || t("toast.error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });
    loading.value = false;
    return;
  }

  step.value = 2;
  loading.value = false;
};
const touchField = (
  value: keyof TVerification | Array<keyof TVerification>
) => {
  if (Array.isArray(value)) {
    value.map((item) => errorTouchedFields.value.push(item));
  } else {
    errorTouchedFields.value.push(value);
  }
};
const onUpdateKycData = (field: keyof TVerification, value: any) => {
  // as watcher does not work correct, create some bugs with loading data
  if (field === "country_id") {
    kycData.value.actual_address = null;
    kycData.value.actual_country_id = value;
  }

  kycData.value[field] = value;
};
const onHideTitle = () => (hideTitle.value = true);
const closeRequest = () => {
  useAxios()
    .delete("/auto-buy/delete/" + activeAutoBuy.value?.id)
    .then(activeRequestClose)
    .catch(() => {
      selectedCardForIssue.value = null;
    });
};
const onChangeAutobuyClose = () => () => (needCloseAutoBuy.value = false);
const onCardSelect = (value: any) => {
  const cardCount = userStore.user.summary?.cards_count;
  const canIssueOneMoreCard = userStore.userActualLimit?.canIssueOneMoreCard(
    Number(cardCount)
  );

  if (canIssueOneMoreCard || userStore.userActualLimit === undefined) {
    userStore.updateActualKycLevel();
    selectedCardForIssue.value = value;
    step.value = 1;
    showModal.value = true;
  } else {
    showKycLimit.value = true;
  }
};
const onClose = () => {
  hideTitle.value = false;
  userStore.updateActualKycLevel();

  if (!isNil(activeAutoBuy.value?.id)) {
    closeRequest();

    if (cardIssueFormRef.value) {
      cardIssueFormRef.value?.clearAllIntervals();
    }
  }

  showModal.value = false;
  if (params.slug) {
    router.push(userStore.isTeamOwner ? "/team" : "/app");
  }
};
const onSetFilter = (filters: Array<number>) => {
  activeFilters.value = filters;
};
const close = () => {
  router.push(userStore.isTeamOwner ? "/team" : "/app");
};
// TODO: add bins badges addBinsBadges

// actions
const activeRequestClose = async () => {
  await cardStore.getCards();

  activeAutoBuy.value = null;
};
const loadExistingAutoBuy = async () => {
  const response = await CardService.autoBuyShow();

  if (!response.status) {
    logger.error(ExceptionCause.AUTO_BUY_SHOW, ExceptionReason.FAILED_LOAD);
  }

  if (response?.data && response.data.length > 0) {
    const autoBuy = response.data?.at(0);
    const card = filteredCards.value.find((card) => card.code === autoBuy.type);

    showModal.value = true;
    selectedCardForIssue.value = card;
    activeAutoBuy.value = autoBuy;
  }
};
const loadBin = async (type: string) => {
  const response = await CardService.bins({
    params: {
      type,
    },
  });

  if (!response.status) {
    logger.error(ExceptionCause.CARD_BIN, ExceptionReason.FAILED_LOAD, {
      bin: type,
    });
  }

  bins.value[type] = response?.data || {};
};
const loadBins = async () =>
  await Promise.all(
    [
      "adv",
      "all",
      "fb-prem",
      "platinum-credit",
      "3ds",
      "pst-black",
      "pst-black-prem",
      "pst-black-uniq-bin",
      "exclusive",
    ].map((type: string) => loadBin(type))
  ).catch((exception) =>
    console.error("CardIssue->loadBins error handled: ", exception)
  );

const loadUserTariff = async () => {
  const response = await UserService.tariff();
  const special = await UserService.special();

  if (!response.status) {
    useCallToast({
      title: t("toast.error"),
      body: response.message || t("toast.error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });
    logger.error(ExceptionCause.USER_TARIFF, ExceptionReason.FAILED_LOAD);
  }

  userSpecial.value = special?.data;
  userTariff.value = response?.data || [];
};
// setup
const setup = async () => {
  setIsLoading(true);
  // Subscriptions info
  const { data } = await useSubscriptionsInfoGet();
  // user have subscription
  if (data.value?.data?.status === 10) {
    await router.push({ name: RouteName.CREATE_CARD });
    return;
  }
  //user do not have special conditions
  // if (
  //   !userStore.user.has_special &&
  //   !userStore.user.is_old_user_for_subscription
  // ) {
  //   await router.push({ name: RouteName.CREATE_CARD });
  //   return;
  // }
  await router.push({ name: RouteName.CREATE_CARD });

  await Promise.all([
    loadUserTariff(),
    loadBins(),
    cardStore.getCards(),
    loadExistingAutoBuy(),
    userStore.updateActualKycLevel(),
  ]);

  setIsLoading(false);
  if (params?.slug) {
    const card = cardsWithBinsBadges.value.find((value) => {
      return value.code === params?.slug;
    });
    if (card) {
      onCardSelect(card);
    }
  }
};
setup();
</script>

<template>
  <ModalScreen
    data-cy="modal_create_card"
    @close="close">
    <!--  Title  -->
    <template #title>
      {{ $t(params.slug ? "Activate a card" : "Create new card") }}
    </template>

    <!--  Page  -->
    <div
      v-if="!isLoading"
      :class="$style.root">
      <!--   Side   -->
      <div :class="$style.aside">
        <Filters
          :active-filters="activeFilters"
          :class="$style.filters"
          @set-filter="onSetFilter" />
      </div>

      <!--   Body   -->
      <div :class="$style.body">
        <TransitionGroup
          appear
          name="fade-slide-up"
          tag="ul">
          <li
            v-if="
              includePromo &&
              userStore.user?.show_adv &&
              !userStore.user?.has_special
            "
            :key="`promo1-card-1`">
            <CreateCardItem
              :card="premiumCard"
              :user-tariff="userTariff" />
          </li>

          <li
            v-for="(card, index) in filteredCards"
            :key="`${card.code}-card-${index}`">
            <CreateCardItem
              :active-request="activeAutoBuy"
              :card="card"
              :cards-info="bins"
              :disabled="isLoading"
              :user-special="userSpecial"
              :user-tariff="userTariff"
              @select="onCardSelect"
              @active-request-close="activeRequestClose" />
          </li>
        </TransitionGroup>
      </div>

      <Portal
        multiple
        to="modals">
        <Screen
          v-if="showModal"
          :class="$style.test"
          :disable-back="true"
          :number-of-step="isNeedWelcomeStep ? 2 : 1"
          :step="step"
          :title="
            !hideTitle
              ? $t(params.slug ? 'Activate a card' : 'Create new card')
              : ''
          "
          @close="onClose">
          <transition
            mode="out-in"
            name="fade-slide-down">
            <InfoStep
              v-if="step === 1 && isNeedWelcomeStep"
              :data="kycData"
              :errors="kycErrors"
              :has-error="getErrorExist('info') || loading"
              :mode="'welcome'"
              @next="onSendWelcomeStep"
              @touch="touchField"
              @update="onUpdateKycData" />

            <Form
              v-else-if="step === (isNeedWelcomeStep ? 2 : 1)"
              ref="cardIssueFormRef"
              :active-request="activeAutoBuy"
              :card="selectedCardForIssue"
              :cards-info="bins"
              :user-tariff="userTariff"
              @hide="onHideTitle"
              @card-issue-autobuy="onAutoBuySent"
              @change-autobuy-close="onChangeAutobuyClose" />
          </transition>
        </Screen>
      </Portal>

      <!--      KYC Limit-->
      <Modal
        v-if="showKycLimit"
        :closable="false"
        :max-width-class="$style.limit"
        @close="showKycLimit = false">
        <Warning
          :class="$style['limit-message']"
          :data="{
            kyc_actual: userStore?.userActualLimit?.userLimit?.slug,
          }"
          background="transparent"
          error="kyc_limit_cards"
          icon="danger" />
        <div :class="$style['limit-actions']">
          <UiButton
            :title="$t('button.upgradeKyc')"
            size="medium"
            @click="$router.push('/app/settings/verification')" />
          <UiButton
            :title="$t('button.close')"
            size="medium"
            type="secondary"
            @click="showKycLimit = false" />
        </div>
      </Modal>
    </div>
    <div
      v-else
      class="absolute z-[1000] top-1/2 left-0 w-full h-full opacity-50">
      <Loader />
    </div>
  </ModalScreen>
</template>

<style lang="scss" module>
.test {
  @apply font-sans;
}

.root {
  @apply flex w-full h-full border-t border-greyscale-100 py-10;
  @apply grid lg:grid-cols-3 xl:grid-cols-4 gap-y-10 gap-x-4;
}

.aside {
  @apply overflow-hidden -ml-4 w-[calc(100%+2rem)] lg:ml-0 lg:w-full;
}

.filters {
  @apply sticky top-0 pt-8 -mt-8;
}

.body {
  @apply lg:col-span-2 xl:col-span-3;

  ul {
    @apply grid md:grid-cols-2 xl:grid-cols-3 grid-flow-row gap-5;
  }
}

.limit {
  @apply max-w-[440px] p-0;

  &-message {
    @apply pt-[30px];
  }

  &-actions {
    @apply px-5 pb-[30px] grid grid-flow-col auto-cols-max items-center gap-2;

    & button {
      @apply max-h-[48px];
    }
  }
}
</style>
