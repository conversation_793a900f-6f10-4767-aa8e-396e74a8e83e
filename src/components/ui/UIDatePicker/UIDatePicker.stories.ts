import type { <PERSON>a, StoryObj } from "@storybook/vue3";
import UIDatePicker from "@/components/ui/UIDatePicker/UIDatePicker.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import type { UIDatePickerProps } from "@/components/ui/UIDatePicker/types";
import { ref } from "vue";

const meta: Meta<UIDatePickerProps> = {
  title: "UI/UIDatePicker",
  tags: ["autodocs"],
  component: UIDatePicker,
  argTypes: {
    label: {
      control: { type: "text" },
    },
    placeholder: {
      control: { type: "text" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    error: {
      control: { type: "text" },
    },
    mode: {
      control: "inline-radio",
      options: ["date", "datetime", "time"],
    },
    minDate: {
      control: { type: "date" },
    },
    maxDate: {
      control: { type: "date" },
    },
    isRange: {
      control: { type: "boolean" },
    },
    masks: {
      control: { type: "object" },
    },
    attributes: {
      control: { type: "object" },
    },
  },
  args: {
    label: "Date",
    placeholder: "MM/DD/YYYY",
    disabled: false,
    error: "",
    mode: "date",
    isRange: false,
  },
};

export default meta;
type Story = StoryObj<UIDatePickerProps>;

export const Default: Story = {
  render: (args: any) => ({
    components: { UIDatePicker },
    setup() {
      const value = ref(null);
      return { args, value };
    },
    template: `
      <p class="mb-3">Value: {{value}}</p>
      <div class="w-[250px]">
      <UIDatePicker
        v-bind='args'
        v-model="value"
        class="w-[250px]"
      />
      </div>
    `,
  }),
};

export const DefaultIsRange: Story = {
  render: (args: any) => ({
    components: { UIDatePicker },
    setup() {
      const value = ref(null);
      return { args, value };
    },
    template: `
      <p class="mb-3">Value: {{value}}</p>
      <div class="w-[250px]">
      <UIDatePicker
        v-bind='args'
        v-model="value"
        is-range
        class="w-[250px]"
      />
      </div>
    `,
  }),
};

export const WithSlot: Story = {
  render: (args: any) => ({
    components: { UIDatePicker, UIButton },
    setup() {
      const value = ref(null);
      return { args, value };
    },
    template: `
      <p class="mb-3">Value: {{value}}</p>
      <div class="w-[250px]">
      <UIDatePicker
        v-bind='args'
        v-model="value"
        class="w-[250px]"
      >
        <UIButton>Select date</UIButton>
      </UIDatePicker>
      </div>
    `,
  }),
};
