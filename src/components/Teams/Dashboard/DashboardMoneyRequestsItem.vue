<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import Comment from "@/components/ui/Comment/Comment.vue";

import { getFormattedDate } from "@/helpers/time";
import { balanceWithSymbol } from "@/helpers/account";
import { getMoneyRequestStatusByValue } from "@/helpers/store";

interface Props {
  template: string;
  data: any;
  approveConfirmId: number;
  disabled: boolean;
  isHistory: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits([
  "on-select-approve",
  "on-show-details",
  "on-approve",
  "on-decline",
]);

const onSelectApprove = () => emit("on-select-approve", props.data.id);
const onBack = () => emit("on-select-approve", -1);
const onShowDetails = () => emit("on-show-details", props.data.id);
const onApprove = () => emit("on-approve", props.data);
const getRequestMoneyUiStatus = (status: number) =>
  getMoneyRequestStatusByValue(status);
</script>

<template>
  <div :class="[$style.root, template]">
    <!--  Date  -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.moneyRequests.header.date") }}
      </span>

      <span>{{ getFormattedDate(new Date(data.created_at)) }}</span>
    </div>

    <!--  Name  -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.moneyRequests.header.name") }}
      </span>

      <span class="overflow-hidden overflow-ellipsis">
        {{ data?.name || data?.email }}
      </span>
    </div>

    <!--  Amount  -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.moneyRequests.header.amount") }}
      </span>

      <span class="overflow-hidden overflow-ellipsis">{{
        balanceWithSymbol(data.amount, {
          iso_code: "USD",
          symbol: "$",
        })
      }}</span>
    </div>

    <!--  Comment  -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.moneyRequests.header.comment") }}
      </span>

      <div class="max-w-[100%]">
        <Comment v-if="data.description">
          {{ data.description }}
        </Comment>
      </div>
    </div>

    <template v-if="!isHistory">
      <!--  default state  -->
      <div
        v-if="data?.id !== approveConfirmId"
        class="grid grid-cols-2 gap-2">
        <UiButton
          :title="$t('team.dashboard.moneyRequests.resolve')"
          size="small"
          type="gray"
          class="col-span-1"
          :disabled="disabled"
          @click="onSelectApprove" />

        <UiButton
          :title="$t('team.dashboard.moneyRequests.details')"
          size="small"
          type="secondary"
          class="col-span-1"
          :disabled="disabled"
          @click="onShowDetails" />
      </div>

      <!--  confirm approve  -->
      <div
        v-else
        class="flex w-full items-center gap-3">
        <span class="text-base text-gray-900 font-medium">
          {{ $t("team.dashboard.moneyRequests.resolve") }}?
        </span>

        <div class="grid grid-cols-2 w-full gap-2">
          <UiButton
            :title="$t('team.dashboard.moneyRequests.confirmApprove.approve')"
            size="small"
            class="col-span-1"
            :disabled="disabled"
            @click="onApprove" />

          <UiButton
            :title="$t('team.dashboard.moneyRequests.confirmApprove.back')"
            size="small"
            type="secondary"
            class="col-span-1"
            :disabled="disabled"
            @click="onBack" />
        </div>
      </div>
    </template>

    <div
      v-else
      class="flex items-start justify-between gap-2 md:block">
      <p class="visible md:hidden text-base font-semibold text-gray-600">
        {{ $t("team.requests.status") }}
      </p>

      <span
        class="text-sm font-extrabold"
        :class="
          getRequestMoneyUiStatus(data.status) === 'approved'
            ? 'text-success-base'
            : 'text-error-base'
        ">
        {{ $t(`team.requestMoney.${getRequestMoneyUiStatus(data.status)}`) }}
      </span>
    </div>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply grid bg-white;
}
</style>
