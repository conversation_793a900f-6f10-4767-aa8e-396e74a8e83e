import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import UIRadioButton from "./UIRadioButton.vue";
import type { ComponentProps } from "vue-component-type-helpers";
import { ref } from "vue";
const meta: Meta<
  ComponentProps<typeof UIRadioButton> & {
    content?: string;
  }
> = {
  title: "UI/UIRadioButton",
  tags: ["autodocs"],
  component: UIRadioButton,
  argTypes: {
    size: {
      control: "inline-radio",
      options: ["m"],
    },
    disabled: {
      control: "boolean",
    },
    value: {
      control: false,
      description: "Value of input for multiple choose",
    },
  },
};

export default meta;

type Story = StoryObj<typeof UIRadioButton>;
/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/vue/api/csf
 * to learn how to use render functions.
 */
export const Default: Story = {
  render: (args: any) => ({
    components: { UIRadioButton },
    setup() {
      const checked = ref("");

      return { args, checked };
    },
    template: `
      <div class="flex flex-col gap-2 w-fit">
        <div class="w-full">Value: {{checked}}</div>
        <UIRadioButton v-bind="args" v-model="checked" value="0">
          UIRadioButton. value=0
        </UIRadioButton>
      </div>
    `,
  }),
  args: {
    size: "m",
    disabled: false,
  },
};

export const DefaultRow: Story = {
  render: (args: any) => ({
    components: { UIRadioButton },
    setup() {
      const checked = ref("");

      return { args, checked };
    },
    template: `
      <div class="flex flex-wrap gap-2 w-fit">
        <div class="w-full">Value: {{checked}}</div>
        <UIRadioButton v-for="(item, index) in 5" :key="item" :value="index" v-bind="args" v-model="checked">
          UIRadioButton. value={{index}}
        </UIRadioButton>
      </div>
    `,
  }),
  args: {
    size: "m",
    disabled: false,
  },
};

export const DefaultColumn: Story = {
  render: (args: any) => ({
    components: { UIRadioButton },
    setup() {
      const checked = ref("");

      return { args, checked };
    },
    template: `
      <div class="flex flex-col gap-2 w-fit">
        <div>Value: {{checked}}</div>
        <UIRadioButton v-for="(item, index) in 5" :key="item" :value="index" v-bind="args" v-model="checked">
          UIRadioButton. value={{index}}
        </UIRadioButton>
      </div>
    `,
  }),
  args: {
    size: "m",
    disabled: false,
  },
};
