import { ref, reactive, computed } from "vue";
import type { TCardResource } from "@/types/api/TCardResource";
import { useUserStore } from "@/stores/user";
import { RouteName } from "@/constants/route_name";
import { useRoute } from "vue-router";
import type { TUseCardsReq } from "@/composable/API/useCardsGet";
import type { TUserMembersBusinessCardsReq } from "@/composable/API/useBusinessMembersCardsGet";

export type CardsReq = TUseCardsReq & TUserMembersBusinessCardsReq;

export type TModals = keyof typeof modalState;

const modalState = reactive({
  cardDetailFull: false,
  cardRename: false,
  transferCardToMember: false,
  transferCardFromMember: false,
  transferCardFromMemberConfirmed: false,
  blockCard: false,
  cardAutoRefill: false,
  upgradeCardModal: false,
  upgradeCardSuccess: false,
  binDeprecatedAttention: false,
});

const selectedCard = ref<TCardResource | null>();

export const useCardsTableState = () => {
  const userStore = useUserStore();
  const route = useRoute();

  const setModalState = (state: boolean, modal: TModals) => {
    clearState();
    modalState[modal] = state;
  };

  const clearState = () => {
    for (const key in modalState) {
      modalState[key as TModals] = false;
    }
  };

  const isTeamTable = computed(() => {
    return (
      (route?.name === RouteName.TEAM_CARDS && userStore.isTeamOwner) ||
      isMemberTable.value
    );
  });

  const isMemberTable = computed(() => {
    return route?.name === RouteName.TEAM_MEMBER_BY_ID && userStore.isTeamOwner;
  });

  const isCardMaster = (email: string) => {
    return userStore.isTeamOwner && email === userStore?.user?.email;
  };

  const setSelectedCard = (card: TCardResource) => {
    selectedCard.value = card;
  };

  const memberId = computed(() => {
    return route.params.memberId as string;
  });

  return {
    modalState,
    selectedCard,
    isTeamTable,
    isMemberTable,
    isCardMaster,
    setModalState,
    setSelectedCard,
    memberId,
    clearState,
  };
};
