<script setup lang="ts">
import Groups from "@/components/ui/Groups/Groups.deprecated.vue";

import { useI18n } from "vue-i18n";
import { computed } from "vue";
import type { TFilterGroup } from "@/components/PaymentsTable/types";

const { t } = useI18n();
const groups = computed<Array<TFilterGroup>>(() => {
  return [
    {
      code: "promocode",
      title: t("affiliate.statistic.promoCode"),
    },
    {
      code: "referral",
      title: t("affiliate.statistic.referral"),
    },
    {
      code: "date",
      title: t("payments.filters.group.day"),
    },
    {
      code: "week",
      title: t("payments.filters.group.week"),
    },
    {
      code: "month",
      title: t("payments.filters.group.month"),
    },
  ];
});
</script>

<template>
  <Groups :groups="groups" />
</template>

<style scoped lang="scss"></style>
