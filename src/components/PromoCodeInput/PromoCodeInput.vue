<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { getFormattedDate, getUtcDate } from "@/helpers/time";
import { useAutoAnimate } from "@formkit/auto-animate/vue";
import {
  TPromoCodeMode,
  TUIPromoCode,
  usePromoCodeInput,
} from "@/components/PromoCodeInput/usePromoCodeInput";
import { useI18n } from "vue-i18n";
import UITextInput from "@/components/ui/UITextInput/UITextInput.vue";
import UIToggle from "@/components/ui/UIToggle/UIToggle.vue";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";

const props = withDefaults(
  defineProps<{
    mode?: TPromoCodeMode;
  }>(),
  {
    mode: "card",
  }
);

const emit = defineEmits<{
  setPromoCode: [data: TPromoCodeResource | undefined];
}>();

const [wrapper] = useAutoAnimate({ duration: 150 });
const [promoCodeList] = useAutoAnimate({ duration: 150 });

const { t } = useI18n();
const {
  activePromoCode,
  isFetching,
  promoCodeLocalList,
  hasError,
  errorMsg,
  activePromoCodeData,
  checkPromoCode,
  clearErrors,
} = usePromoCodeInput(props.mode);

const inputValue = ref("");
const isShowInput = ref(false);

const validationRules = {
  validate: (value: string) =>
    /^(?=[a-zA-Z0-9,!@#$%^&*()\-_=+{};:<.>]*$)/.test(value),
  message: t("promoCode.error"),
};

const validationError = computed(() => {
  const valid = validationRules.validate(inputValue.value);

  if (!valid && inputValue.value.length) {
    return validationRules.message;
  }
  return "";
});

const displayError = computed(() => {
  return hasError.value ? t(`errors.${errorMsg.value}`) : "";
});

const checkPromoCodeHandle = async (code: string) => {
  await checkPromoCode(code);

  if (activePromoCodeData.value && !hasError.value) {
    inputValue.value = "";
  }
};

const switcherHandle = (checked: boolean, code: string) => {
  for (let item of promoCodeLocalList.value) {
    if (item.code === code) {
      item.enabled = checked;
    } else {
      if (checked) {
        item.enabled = false;
      }
    }
  }
  activePromoCode.value = checked ? code : "";
};

const reset = () => {
  activePromoCode.value = "";
  promoCodeLocalList.value = [];
};

watch(activePromoCode, () => {
  emit("setPromoCode", activePromoCodeData.value?.resource ?? undefined);
});

defineExpose({ reset });

const benefitText = (promoCodeData: TUIPromoCode) => {
  switch (promoCodeData.promocode_type) {
    case "card_discount":
    case "subscription_discount":
      return (
        t("operations.promoCode.discountTitle") +
        " " +
        promoCodeData.promocode_value +
        "%"
      );
    case "card_bonus":
      return (
        t("operations.promoCode.bonusTitle") +
        " " +
        promoCodeData.promocode_value +
        "$"
      );
    case "extension":
      return (
        t("operations.promoCode.extensionTitle") +
        " " +
        `(+${promoCodeData.promocode_value})`
      );
    default:
      return null;
  }
};
</script>

<template>
  <div ref="wrapper">
    <UIButton
      v-if="!isShowInput"
      class="w-full"
      color="green-free"
      size="s"
      @click="isShowInput = true">
      {{ $t("promoCode.btn") }}
    </UIButton>
    <div
      v-else
      class="bg-bg-level-1 p-4 rounded w-full">
      <div class="w-full flex items-start justify-between space-x-3">
        <UITextInput
          v-model="inputValue"
          :error="validationError || displayError"
          :placeholder="$t('promoCode.inputPlaceHolder')"
          autofocus
          class="flex-grow"
          required
          size="m"
          type="text"
          @focus="clearErrors"
          @key-down.enter="
            !validationError && checkPromoCodeHandle(inputValue)
          " />
        <UIButton
          v-if="inputValue && !validationError"
          :disabled="isFetching"
          :is-loading="isFetching"
          color="black"
          @click="checkPromoCodeHandle(inputValue)">
          {{ $t("promoCode.apply") }}
        </UIButton>
      </div>
      <!--  PromoCode list -->
      <div
        v-if="promoCodeLocalList"
        ref="promoCodeList">
        <div
          v-for="(item, i) in promoCodeLocalList"
          :key="i"
          class="bg-bg-level-1 rounded border border-bg-level-2 px-2.5 py-2 mt-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <h4
                v-if="item.code"
                class="font-semibold uppercase text-xl">
                {{ item?.code }}
              </h4>
              <div
                v-if="item.stop_at"
                class="bg-bg-level-2 rounded-full px-2 py-1">
                <p class="text-sm leading-none text-nowrap">
                  {{ $t("promoCode.atDate") }}
                  {{
                    getFormattedDate(getUtcDate(item.stop_at), {
                      withYear: true,
                    })
                  }}
                </p>
              </div>
            </div>
            <UIToggle
              :model-value="item.enabled ?? false"
              :size="'medium'"
              @update:model-value="
                (checked) => switcherHandle(checked, item.code)
              " />
          </div>
          <p
            v-if="item.promocode_value"
            class="font-medium text-fg-secondary">
            {{ benefitText(item) }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
