<template>
  <InputBase
    v-bind="$attrs"
    :value="value"
    :label-type="labelType"
    @input="onInput"
    @change="onChange">
    <template
      v-if="$slots.leftIcon"
      #leftIcon>
      <slot name="leftIcon" />
    </template>

    <template
      v-if="props.copy"
      #rightIcon>
      <DynamicIcon
        v-tooltip="{
          content: value === copiedValue ? $t('Copied') : $t('Copy'),
        }"
        v-ripple
        class="cursor-pointer w-6 h-6 text-fg-primary"
        name="copy"
        @click="copyValue" />
    </template>
  </InputBase>
</template>

<script lang="ts" setup>
import InputBase from "./InputBase.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  copy?: boolean;
  value?: string | number;
  copiedValue?: string | number;
  labelType?: "inside" | "outside";
}

const emit = defineEmits(["change", "input", "copied"]);
const props = withDefaults(defineProps<Props>(), {
  labelType: "inside",
});
const copyValue = () => {
  navigator.clipboard
    .writeText(props.value ? String(props.value) : "")
    .catch((ex) =>
      console.warn(
        "InputCopy->copyValue->navigator.clipboard error handled: ",
        ex
      )
    );

  emit("copied", props.value);
};

const onChange = (event: Event & { target: HTMLInputElement }) =>
  emit("change", event.target.value);
const onInput = (event: Event & { target: HTMLInputElement }) =>
  emit("input", event.target.value);
</script>

<style lang="sass" module></style>
