<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import Backdrop from "@/components/ui/Backdrop/Backdrop.vue";
import Auth from "@/helpers/auth";
import { useUserStore } from "@/stores/user";
import { RouteName } from "@/constants/route_name";
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

type ToolbarLink = {
  to: RouteName;
  dataCy: string;
  onClick(): void;
  iconName: string;
  text: string;
};

const props = withDefaults(
  defineProps<{
    hiddenLinks?: ("finance" | "settings")[];
    hiddenBackdrop?: boolean;
    size?: "s" | "m" | "xs";
  }>(),
  {
    hiddenLinks: () => [],
    size: "m",
  }
);

const userStore = useUserStore();
const { t } = useI18n();

const router = useRouter();
const emit = defineEmits(["close"]);

const showDropdown = ref<boolean>(false);
const dropdownEl = ref<HTMLElement | null>(null);

const toolbarLinks = computed<ToolbarLink[]>(() => {
  const links: ToolbarLink[] = [];

  if (!props.hiddenLinks.includes("finance")) {
    links.push({
      to: userStore.isTeamOwner ? RouteName.TEAM_FINANCE : RouteName.FINANCE,
      dataCy: "topbar_finance",
      onClick: onClose,
      iconName: "receipt-tax",
      text: t("toolbar.menu.finance"),
    });
  }

  if (!props.hiddenLinks.includes("settings")) {
    links.push({
      to: RouteName.SETTINGS,
      dataCy: "topbar_settings",
      onClick: dropdownHandler,
      iconName: "settings",
      text: t("settings"),
    });
  }

  return links;
});

const onClose = () => {
  emit("close");

  dropdownHandler();
};

const dropdownHandler = () => {
  showDropdown.value = !showDropdown.value;
  const sidebar: HTMLElement | null = document.querySelector(".sidebar");
  if (showDropdown.value) {
    document.documentElement.classList.add("active-toolbar");
    if (sidebar) sidebar.style.zIndex = "0";
  } else {
    document.documentElement.classList.remove("active-toolbar");
    if (sidebar) sidebar.style.zIndex = "1000";
  }
};

const logoutHandler = async () => {
  try {
    const result = await Auth.logout();

    if (result) {
      await router.push({ name: RouteName.LOGIN });
    }
  } catch (ex) {
    console.error("ToolbarUser->logoutHandler error handled: ", ex);
  }
};
</script>
<template>
  <div :class="$style.root">
    <div
      :class="{
        [$style.root__wrapper]: true,
        [$style[`root__wrapper-active`]]: showDropdown,
      }">
      <UIButton
        type="button"
        :size="size"
        shape="pill"
        icon-only
        data-cy="toolbar_user_button"
        :class="{ 'pointer-events-none': showDropdown }"
        @click="dropdownHandler">
        <DynamicIcon
          name="user"
          class="w-6 h-6" />
      </UIButton>
      <transition name="fade-slide-down">
        <div
          v-if="showDropdown"
          ref="dropdownEl"
          v-click-outside="dropdownHandler"
          :class="[$style.dropdown]"
          data-cy="topbar_account_container">
          <ul>
            <li
              v-for="link in toolbarLinks"
              :key="link.dataCy">
              <router-link
                :to="{ name: link.to }"
                :data-cy="link.dataCy"
                @click="link.onClick">
                <DynamicIcon :name="link.iconName" />
                {{ link.text }}
              </router-link>
            </li>

            <li>
              <a
                data-cy="topbar_exit"
                @click="logoutHandler">
                <DynamicIcon name="logout" />
                {{ $t("exit") }}
              </a>
            </li>
          </ul>
        </div>
      </transition>
    </div>
    <Backdrop
      v-if="!hiddenBackdrop && showDropdown"
      :class="$style.root__backdrop" />
  </div>
</template>

<style lang="scss" module>
.root {
  &__wrapper {
    @apply relative;

    &-active {
      @apply z-4;
    }
  }

  &__backdrop {
    @apply z-3;
  }
}

.dropdown {
  @apply bg-bg-level-0 z-50 min-w-[210px] w-auto whitespace-nowrap mt-2 rounded;
  @apply absolute overflow-hidden left-auto right-0;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 8px 16px rgba(0, 0, 0, 0.08);

  & ul {
    @apply flex flex-col;

    & li a {
      @apply flex items-center gap-4 cursor-pointer text-fg-secondary text-4 p-4 transition-all;
      @apply hover:text-fg-primary hover:bg-bg-level-0-hover active:bg-bg-level-0-clicked;

      & svg {
        @apply w-5 h-5 min-w-5;
      }
    }
  }
}
</style>
