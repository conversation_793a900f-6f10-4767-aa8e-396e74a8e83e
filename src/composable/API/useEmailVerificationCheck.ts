import { usePstFetch } from "@/composable/usePstFetch";
import type { UseFetchOptions } from "@vueuse/core";
import type { MaybeRef } from "vue";

export type EmailVerificationCheckRequest = {
  code: string;
};

export type EmailVerificationCheckPostResponse = {
  success: boolean;
  message?: string;
};

export const useEmailVerificationCheckPost = (
  body: MaybeRef<EmailVerificationCheckRequest>,
  options: UseFetchOptions = {}
) => {
  return usePstFetch("email/verify/check", options)
    .post(body)
    .json<EmailVerificationCheckPostResponse>();
};
