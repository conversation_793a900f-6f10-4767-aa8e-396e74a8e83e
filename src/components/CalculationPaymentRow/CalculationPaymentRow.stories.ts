import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/vue3";
import type { ComponentProps } from "vue-component-type-helpers";
import CalculationPaymentRow from "@/components/CalculationPaymentRow/CalculationPaymentRow.vue";
import Calculation from "@/components/Calculation/Calculation.vue";
import type { TTransactionResource } from "@/types/api/TTransactionResource";
import type { MemberTransactionsResource } from "@/types/api/MemberTransactionsResource";

const meta: Meta<
  ComponentProps<
    | typeof CalculationPaymentRow
    | TTransactionResource
    | MemberTransactionsResource
  >
> = {
  title: "CalculationPaymentRow",
  component: CalculationPaymentRow,
  tags: ["autodocs"],
  render(args: any) {
    return {
      components: {
        CalculationPaymentRow,
        Calculation,
      },
      setup() {
        return { args };
      },
      template: `<div class="flex w-full">
        <Calculation>
          <CalculationPaymentRow
              :iso-code="args.isoCode"
              :payment="args"/>
          </Calculation>
      </div>`,
    };
  },
  argTypes: {
    isoCode: { control: "text" },
    description: { control: "text" },
    amount_total: { control: "text" },
    processed_at: { control: "text" },
    deleted_at: { control: "text" },
    status: { control: "number" },
    type_enum: { control: "text" },
    status_text: { control: "text" },
    type: { control: "text" },
    cashback_status: { control: "text" },
    cashback_amount: { control: "text" },
  },
  args: {
    isoCode: "USDT",
    description: "Transaction from USDT account",
    amount_total: "1880.78",
    processed_at: "2024-05-04 15:00:00",
    deleted_at: "2024-05-04 17:00:00",
    status: 1,
    type_enum: "Authorization",
    status_text: "status_text",
    type: "type",
    cashback_status: "approved",
    cashback_amount: "188.07",
  },
};

export default meta;

type Story = StoryObj<typeof CalculationPaymentRow>;

export const Default: Story = {};
