import { computed, unref, type MaybeRef } from "vue";
import queryString from "query-string";

/**
 * Converts an object to a query string computed ref
 *
 * @param {MaybeRef<T>} obj - object to convert to query string
 * @returns {ComputedRef<string>} - computed ref of the query string
 *
 * @category Composables
 */
export function useQueryString<T = unknown>(obj: MaybeRef<T>) {
  return computed(() => {
    return queryString.stringify(unref(obj as Record<string, unknown>), {
      arrayFormat: "comma",
    });
  });
}
