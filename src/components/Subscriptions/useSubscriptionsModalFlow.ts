import { reactive, ref } from "vue";
import { balanceWithSymbol } from "@/helpers/account";
import type { TIsoSymbol } from "@/composable";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";

const modalState = reactive({
  subscriptionUpLimitModal: false,
  subscriptionSuccessModal: false,
  subscriptionWarnLimitModal: false,
  subscriptionSuccessRaiseModal: false,
});

const meta = ref<{
  kind: "extension";
  payload: TSubscriptionTariffResource;
} | null>(null);

export const useSubscriptionsModalFlow = () => {
  const setModalState = (state: boolean, modal: keyof typeof modalState) => {
    modalState[modal] = state;
  };

  const usdBalanceValue = (v: number) => {
    return balanceWithSymbol(
      v,
      {
        symbol: "$",
        iso_code: "USD",
      },
      6,
      "right"
    );
  };

  const getCurrencySymbolById = (
    currency_id: number
  ): TIsoSymbol | undefined => {
    switch (currency_id) {
      case 2:
        return "$";
      case 3:
        return "€";
      case 15:
        return "₮";
      case 14:
        return "₿";
    }
  };

  return {
    modalState,
    meta,
    setModalState,
    usdBalanceValue,
    getCurrencySymbolById,
  };
};
