<template>
  <div :class="[$style.root, props?.blur ? $style.blur : $style.default]"></div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";

interface Props {
  blur?: boolean;
}

const props = defineProps<Props>();

onMounted(() => {
  document.documentElement.classList.add("no-scrolled");
});

onUnmounted(() => {
  document.documentElement.classList.remove("no-scrolled");
});
</script>

<style lang="scss" module>
.root {
  @apply fixed top-0 bottom-0 left-0 right-0;
}

.blur {
  @apply bg-white bg-opacity-80 backdrop-blur-sm;
}

.default {
  @apply bg-black bg-opacity-50;
}
</style>
