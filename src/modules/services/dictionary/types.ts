import type {
  TAccountTypes,
  TCardStatuses,
  TCardSystem,
  TContentTypes,
  TCountry,
  TFees,
  TLanguage,
  TMoneyRequestStatuses,
  TNotificationService,
  TNotificationType,
  TOauth,
  TPaymentSystem,
  TTariffTypes,
  TTransactionStatuses,
  TVerificationStatuses,
  TApiKeyAbility,
} from "@/types/dictionary/dictionary.types";
import type { TMerchant } from "@/types/user/user.types";
import type { TypeExchangeRates } from "@/composable/useAccounts";
import type { TCurrency } from "@/types/dictionary/currencies";

export enum ServiceBase {
  value = "dictionary",
}

export enum ServiceEndpoint {
  CURRENCIES = "currencies",
  ACCOUNT_TYPES = "account-types",
  ACCOUNT_TYPES_KEY = "accountTypes",
  VERIFICATION_STATUSES = "verification-statuses",
  VERIFICATION_STATUSES_KEY = "verificationStatuses",
  VERIFICATION_STEPS = "verification-steps",
  VERIFICATION_STEPS_KEY = "verificationSteps",
  TARIFF_TYPES = "tariff-types",
  TARIFF_TYPES_KEY = "tariffTypes",
  CARD_STATUSES = "card-statuses",
  CARD_STATUSES_KEY = "cardStatuses",
  CARD_SYSTEM = "card-systems",
  CARD_SYSTEM_KEY = "cardSystem",
  CONTENT_TYPES = "content-types",
  CONTENT_TYPES_KEY = "contentTypes",
  EXCHANGE_RATES = "exchange-rates",
  EXCHANGE_RATES_KEY = "exchangeRates",
  PAYMENT_SYSTEMS = "payment-systems",
  PAYMENT_SYSTEMS_KEY = "paymentSystems",
  DEFAULT_CURRENCY = "default-currency",
  DEFAULT_CURRENCY_KEY = "defaultCurrency",
  TRANSACTION_STATUSES = "transaction-statuses",
  TRANSACTION_STATUSES_KEY = "transactionStatuses",
  LANGUAGE = "languages",
  COUNTRIES = "countries",
  FEES = "fees",
  NOTIFICATION_SERVICES = "notification-services",
  NOTIFICATION_SERVICES_KEY = "notificationServices",
  NOTIFICATION_TYPES = "notification-types",
  NOTIFICATION_TYPES_KEY = "notificationTypes",
  OAUTH = "oauth",
  MONEY_REQUEST_STATUSES = "money-request-statuses",
  MONEY_REQUEST_STATUSES_KEY = "moneyRequestStatuses",
  MERCHANTS = "merchants",
  API_KEY_ABILITIES = "api-key-abilities",
}

export type TResponses = {
  [ServiceEndpoint.CURRENCIES]: Array<TCurrency>;
  [ServiceEndpoint.ACCOUNT_TYPES_KEY]: TAccountTypes;
  [ServiceEndpoint.VERIFICATION_STATUSES_KEY]: TVerificationStatuses;
  // TODO: FIx any
  [ServiceEndpoint.VERIFICATION_STEPS_KEY]: any;
  [ServiceEndpoint.TARIFF_TYPES_KEY]: TTariffTypes;
  [ServiceEndpoint.CARD_STATUSES_KEY]: TCardStatuses;
  [ServiceEndpoint.CARD_SYSTEM_KEY]: TCardSystem;
  [ServiceEndpoint.CONTENT_TYPES_KEY]: TContentTypes;
  [ServiceEndpoint.EXCHANGE_RATES_KEY]: TypeExchangeRates;
  [ServiceEndpoint.PAYMENT_SYSTEMS_KEY]: Array<TPaymentSystem>;
  [ServiceEndpoint.DEFAULT_CURRENCY_KEY]: TCurrency;
  [ServiceEndpoint.TRANSACTION_STATUSES_KEY]: TTransactionStatuses;
  [ServiceEndpoint.LANGUAGE]: Array<TLanguage>;
  [ServiceEndpoint.COUNTRIES]: Array<TCountry>;
  [ServiceEndpoint.FEES]: TFees;
  [ServiceEndpoint.NOTIFICATION_SERVICES_KEY]: Array<TNotificationService>;
  [ServiceEndpoint.NOTIFICATION_TYPES_KEY]: Array<TNotificationType>;
  [ServiceEndpoint.OAUTH]: TOauth;
  [ServiceEndpoint.MONEY_REQUEST_STATUSES_KEY]: TMoneyRequestStatuses;
  [ServiceEndpoint.MERCHANTS]: Array<TMerchant>;
  [ServiceEndpoint.API_KEY_ABILITIES]: Array<TApiKeyAbility>;
};

export type TPayloads = {
  [ServiceEndpoint.CURRENCIES]: undefined;
  [ServiceEndpoint.ACCOUNT_TYPES_KEY]: undefined;
  [ServiceEndpoint.VERIFICATION_STATUSES_KEY]: undefined;
  [ServiceEndpoint.VERIFICATION_STEPS_KEY]: undefined;
  [ServiceEndpoint.TARIFF_TYPES_KEY]: undefined;
  [ServiceEndpoint.CARD_STATUSES_KEY]: undefined;
  [ServiceEndpoint.CARD_SYSTEM_KEY]: undefined;
  [ServiceEndpoint.CONTENT_TYPES_KEY]: undefined;
  [ServiceEndpoint.EXCHANGE_RATES_KEY]: undefined;
  [ServiceEndpoint.PAYMENT_SYSTEMS_KEY]: undefined;
  [ServiceEndpoint.DEFAULT_CURRENCY_KEY]: undefined;
  [ServiceEndpoint.TRANSACTION_STATUSES_KEY]: undefined;
  [ServiceEndpoint.LANGUAGE]: undefined;
  [ServiceEndpoint.COUNTRIES]: undefined;
  [ServiceEndpoint.FEES]: undefined;
  [ServiceEndpoint.NOTIFICATION_SERVICES_KEY]: undefined;
  [ServiceEndpoint.NOTIFICATION_TYPES_KEY]: undefined;
  [ServiceEndpoint.OAUTH]: undefined;
  [ServiceEndpoint.MONEY_REQUEST_STATUSES_KEY]: undefined;
  [ServiceEndpoint.MERCHANTS]: undefined;
  [ServiceEndpoint.API_KEY_ABILITIES]: undefined;
};
