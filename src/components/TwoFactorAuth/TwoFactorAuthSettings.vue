<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";

import ConfirmDisconnectDialog from "@/components/TwoFactorAuth/dialogs/ConfirmDisconnectDialog.vue";
import DisconnectedDialog from "@/components/TwoFactorAuth/dialogs/DisconnectedDialog.vue";
import TwoFactorCodeDialog from "@/components/TwoFactorAuth/dialogs/TwoFactorCodeDialog.vue";
import UnableToDisconnectDialog from "@/components/TwoFactorAuth/dialogs/UnableToDisconnectDialog.vue";
import StepsContainer from "@/components/TwoFactorAuth/StepsContainer.vue";
import { useResendTimeout } from "@/components/TwoFactorAuth/useResendTimeout";
import { useUserTfaData } from "@/components/TwoFactorAuth/useUserTfaData";
import SupportTrigger from "@/components/SupportTrigger.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import UITextInput from "@/components/ui/UITextInput/UITextInput.vue";
import type {
  DisconnectDialogsState,
  TwoFactorAuthProvider,
  TwoFactorAuthStep,
} from "@/components/TwoFactorAuth/types";

import { useUser2FaPost } from "@/composable/API/useUser2FaPost";
import { useUserRemoveTgPost } from "@/composable/API/useUserRemoveTgPost";
import { useUserRemoveWaPost } from "@/composable/API/useUserRemoveWaPost";
import { TOAST_TYPE, useCallToast } from "@/composable";

const { t } = useI18n();

const { refetch, twoFactorAuthState } = useUserTfaData();

const { start: startResendTimeout, counter } = useResendTimeout();

const showTwoFactorAuthSidebar = ref<boolean>(false);
const step = ref<TwoFactorAuthStep>("list");

const tfaState = reactive<DisconnectDialogsState>({
  provider: null,
  dialogState: "none",
  code: "",
  incorrectCodeError: false,
});

const sideBarTitle = computed(() => {
  switch (step.value) {
    case "list":
      return "2FA";
    case "connect-tg":
      return t("2fa-settings.telegram-connection");
    case "connect-wa":
      return t("2fa-settings.whatsapp-connection");
    default:
      return "";
  }
});

const showBackButton = computed(() => {
  return step.value !== "list";
});

const tfaValue = computed(
  () =>
    twoFactorAuthState.value.user_telegram_2fa ??
    twoFactorAuthState.value.user_whatsapp_2fa
);

const tfaPrefix = computed(() =>
  twoFactorAuthState.value.enabledProvider
    ? twoFactorAuthState.value.user_telegram_2fa
      ? "t.me/@"
      : "+"
    : ""
);

const openSidebar = () => {
  showTwoFactorAuthSidebar.value = true;
};

const onCloseSidebarHandle = () => {
  showTwoFactorAuthSidebar.value = false;
};

const onChangeStepHandle = (newStep: TwoFactorAuthStep) => {
  step.value = newStep;
  refetchData();
};

const onEnableProviderHandle = async (provider: TwoFactorAuthProvider) => {
  const response = await useUser2FaPost({
    provider: provider,
  });

  if (response.statusCode.value === 200) {
    refetchData();
  }
};

const onDisableProviderHandle = async () => {
  const response = await useUser2FaPost({
    provider: null,
  });

  if (response.statusCode.value === 200) {
    refetchData();
  } else {
    onRequestError();
  }
};

const onCloseDisconnectedDialog = () => {
  tfaState.dialogState = "none";
  tfaState.provider = null;
  tfaState.incorrectCodeError = false;
  tfaState.code = "";
};

const onDisconnectProviderHandle = async (provider: TwoFactorAuthProvider) => {
  tfaState.provider = provider;
  if (
    provider === twoFactorAuthState.value.enabledProvider ||
    provider === twoFactorAuthState.value.registration_method
  ) {
    tfaState.dialogState = "show-unable-to-disconnect-dialog";
  } else {
    tfaState.dialogState = "show-confirm-dialog";
  }
};

const onConfirmDisconnectTfa = async () => {
  await requestCode();
  tfaState.dialogState = "show-code-dialog";
};

const onEnteredCodeHandle = async (code: string) => {
  tfaState.code = code;
  tfaState.incorrectCodeError = false;
  if (tfaState.provider === "telegram") {
    const response = await useUserRemoveTgPost({
      step: "approve_delete",
      code: code,
    });

    if (
      response.statusCode.value === 422 ||
      response.statusCode.value === 400
    ) {
      tfaState.incorrectCodeError = true;
      tfaState.code = "";
      return;
    } else if (response.statusCode.value === 200) {
      refetchData();
      tfaState.dialogState = "show-disconnected-dialog";
      tfaState.code = "";
      tfaState.incorrectCodeError = false;
    }
  } else if (tfaState.provider === "whatsapp") {
    const response = await useUserRemoveWaPost({
      step: "approve_delete",
      code: code,
    });

    if (
      response.statusCode.value === 422 ||
      response.statusCode.value === 400
    ) {
      tfaState.incorrectCodeError = true;
      tfaState.code = "";
      return;
    } else if (response.statusCode.value === 200) {
      refetchData();
      tfaState.dialogState = "show-disconnected-dialog";
      tfaState.code = "";
      tfaState.incorrectCodeError = false;
    }
  }
};

const onResendCodeHandle = async () => {
  await requestCode(true);
  tfaState.incorrectCodeError = false;
  startResendTimeout();
};

const requestCode = async (resend: boolean = false) => {
  if (tfaState.provider === "telegram") {
    await useUserRemoveTgPost({
      step: resend ? "refresh" : "delete_account",
    });
  } else if (tfaState.provider === "whatsapp") {
    await useUserRemoveWaPost({
      step: resend ? "refresh" : "delete_account",
    });
  }
};

const onRequestError = () => {
  refetchData();
  resetTfaState();
  useCallToast({
    title: t("errors.universal-request-error"),
    options: {
      type: TOAST_TYPE.ERROR,
    },
  });
};

const onCloseUnableToDisconnectHandle = () => {
  tfaState.dialogState = "none";
};

const refetchData = async () => {
  // if we have connected provider and opened connection step for it - we close it
  await refetch();
  if (twoFactorAuthState.value.user_telegram && step.value === "connect-tg") {
    step.value = "list";
  } else if (
    twoFactorAuthState.value.user_whatsapp &&
    step.value === "connect-wa"
  ) {
    step.value = "list";
  }
};

const resetTfaState = () => {
  tfaState.provider = null;
  tfaState.dialogState = "none";
  tfaState.code = "";
  tfaState.incorrectCodeError = false;
};
</script>

<template>
  <div>
    <div class="flex flex-row">
      <UIButton
        v-if="!twoFactorAuthState.enabledProvider"
        color="grey-solid"
        size="m"
        class="w-full md:w-[180px]"
        @click="openSidebar">
        {{ $t("2fa-settings.status-connect") }}
      </UIButton>
      <div
        v-else
        class="flex gap-4 min-w-[26.25rem]">
        <UITextInput
          v-model="tfaValue"
          disabled
          :prefix="tfaPrefix"
          type="text"
          size="m"
          class="flex-grow">
        </UITextInput>
        <UIButton
          color="grey-solid"
          size="m"
          @click="openSidebar">
          <DynamicIcon
            name="settings"
            class="w-6 h-6" />
        </UIButton>
      </div>
    </div>
    <UISideModal
      :title="sideBarTitle"
      :is-open="showTwoFactorAuthSidebar"
      @close="onCloseSidebarHandle">
      <template #title>
        <div class="flex flex-row flex-auto items-center">
          <div class="flex flex-row">
            <div
              v-if="showBackButton"
              class="flex flex-none mr-3">
              <UIButton
                icon-only
                color="grey-free"
                size="s"
                @click="onChangeStepHandle('list')">
                <DynamicIcon
                  name="arrow-left-thin"
                  class="w-6 h-6 text-fg-secondary" />
              </UIButton>
            </div>
          </div>
          <div
            class="flex flex-none text-fg-primary font-medium text-5 leading-7">
            {{ sideBarTitle }}
          </div>
        </div>
      </template>
      <template #buttons>
        <div class="mr-3">
          <SupportTrigger />
        </div>
      </template>
      <template #content>
        <StepsContainer
          :two-factor-auth-state="twoFactorAuthState"
          :step="step"
          @change-step="onChangeStepHandle"
          @enable-provider="onEnableProviderHandle"
          @disable-provider="onDisableProviderHandle"
          @disconnect="onDisconnectProviderHandle"
          @refetch="refetchData" />
      </template>
    </UISideModal>
    <ConfirmDisconnectDialog
      v-if="tfaState.provider"
      :is-open="tfaState.dialogState === 'show-confirm-dialog'"
      :provider="tfaState.provider"
      :two-factor-auth-state="twoFactorAuthState"
      @close="tfaState.dialogState = 'none'"
      @confirm="onConfirmDisconnectTfa" />
    <TwoFactorCodeDialog
      :timeout="counter"
      :is-open="tfaState.dialogState === 'show-code-dialog'"
      :code-error="tfaState.incorrectCodeError"
      @close="tfaState.dialogState = 'none'"
      @resend="onResendCodeHandle"
      @confirm="onEnteredCodeHandle" />
    <DisconnectedDialog
      v-if="tfaState.provider"
      :is-open="tfaState.dialogState === 'show-disconnected-dialog'"
      :provider="tfaState.provider"
      @close="onCloseDisconnectedDialog" />
    <UnableToDisconnectDialog
      v-if="tfaState.provider"
      :is-open="tfaState.dialogState === 'show-unable-to-disconnect-dialog'"
      :provider="tfaState.provider"
      @close="onCloseUnableToDisconnectHandle" />
  </div>
</template>
