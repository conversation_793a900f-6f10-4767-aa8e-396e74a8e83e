import type { TAccountsCurrencyId } from "@/types/api/TUserAccountResource";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import { CARDS_STATUS } from "@/constants/cards_status";
import type { TTariffResource } from "@/types/api/TTariffResource";

export const cardBalanceWithSymbol = (
  balance: string,
  currency_id: TAccountsCurrencyId
) => {
  return `${Intl.NumberFormat().format(Number(balance))} ${
    getAccountCurrencyByCurrencyId(currency_id).symbol
  }`;
};

export const getCardSimpleStatus = (simpleStatus: "active" | "inactive") => {
  switch (simpleStatus) {
    case "active":
      return "Active";
    case "inactive":
      return "Inactive";
    default:
      return null;
  }
};

export const getCardStatus = (status_id: CARDS_STATUS) => {
  switch (status_id) {
    case CARDS_STATUS.Active:
      return "Active";
    case CARDS_STATUS.Expired:
      return "Expired";
    case CARDS_STATUS.Blocked:
      return "Blocked";
    default:
      return null;
  }
};

export const getTariffName = (id: number, tariffs: TTariffResource[]) => {
  const tariff = tariffs.find((t) => t.id === id);
  return tariff?.name.replace(/ card|s$/g, "");
};
