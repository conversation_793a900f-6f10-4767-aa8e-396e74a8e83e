<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { computed } from "vue";
import type { TCardPst } from "@/types/card";

export interface Props {
  card?: TCardPst;
  size?: "small" | "medium";
}

const props = defineProps<Props>();

const cardType = computed<string>(() => {
  return props.card?.mask.startsWith("4") ? "visa" : "mastercard";
});
const getTypeColor = (tariff: string) => {
  const lightBgCards: string | string[] = ["3ds", "all"];
  if (lightBgCards.includes(tariff)) {
    return "dark";
  }
  return "light";
};
</script>

<template>
  <div :class="[cardIcon.root, cardIcon[`size-${props.size || 'medium'}`]]">
    <!--    {{ props.card?._tariff_slug }}-->
    <DynamicIcon
      path="cards"
      :class="cardIcon.bg"
      :name="`${props.card?._tariff_slug}-small`" />
    <span
      :class="[
        cardIcon.num,
        cardIcon[
          `num-color-${getTypeColor(props.card?._tariff_slug || 'all')}`
        ],
        cardIcon[`num-${props.size || 'medium'}`],
      ]">
      {{ card?.mask.slice(-4) }}
    </span>

    <DynamicIcon
      :class="[
        cardIcon.type,
        cardIcon[`type-${getTypeColor(props.card?._tariff_slug || 'all')}`],
        cardIcon[`type-${props.size || 'medium'}-${cardType}`],
      ]"
      :name="cardType"
      path="cards" />
  </div>
</template>
<style lang="scss" module="cardIcon">
.root * {
  @apply font-granate;
}

.root {
  @apply relative z-20;
}

.size {
  &-small {
    @apply w-[45px] h-[32px];
  }

  &-medium {
    @apply w-[67px] h-[44px];
  }
}

.bg {
  @apply w-full h-full;
}

.num {
  @apply absolute top-0 left-0 leading-4;

  &-small {
    @apply mt-[2px] ml-[3px] text-[10px] font-medium;
  }

  &-medium {
    @apply mt-[3px] ml-[6px] text-sm font-semibold;
  }

  &-color {
    &-light {
      @apply text-white;
    }

    &-dark {
      @apply text-neutral-900;
    }
  }
}

.type {
  @apply absolute right-0 bottom-0 mb-[5px];

  &-light {
    @apply text-white;
  }

  &-dark {
    @apply text-neutral-900;
  }

  &-small-mastercard {
    @apply w-[22px] h-[7px] mr-[2px];
  }

  &-small-visa {
    @apply w-[22px] h-[6px] mr-[2px];
  }

  &-medium-mastercard {
    @apply w-[28px] h-[10px] mr-[5px];
  }

  &-medium-visa {
    @apply w-[24px] h-[8px] mr-[5px];
  }
}
</style>
