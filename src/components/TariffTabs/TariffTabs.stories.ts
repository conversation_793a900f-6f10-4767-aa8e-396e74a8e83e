import type { <PERSON>a, StoryObj } from "@storybook/vue3";
import TariffTabs from "@/components/TariffTabs/TariffTabs.vue";
import type {
  TariffTabsOption,
  TariffTabsProps,
} from "@/components/TariffTabs/types";
import { ref } from "vue";

const meta: Meta<TariffTabsProps> = {
  title: "TariffTabs",
  component: TariffTabs,
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: "inline-radio",
      options: ["s", "l"],
      description: "Sets the size",
    },
    options: {
      control: false,
    },
  },
};
export default meta;
type Story = StoryObj<TariffTabsProps>;

const options: TariffTabsOption[] = [
  {
    title: "Weekly",
    price: "$7",
    period: "per week",
    value: 1,
    badge: {
      name: "some",
      color: "green",
    },
  },
  {
    title: "Monthly",
    price: "$15",
    period: "per month",
    value: 2,
  },
  {
    title: "Annually",
    price: "$99",
    period: "per year",
    value: 3,
    oldPrice: "$180",
    badge: {
      name: "any",
      color: "purple",
    },
  },
];

export const Default: Story = {
  args: {
    size: "s",
    options: options,
  },
  render: (args: TariffTabsProps) => ({
    components: { TariffTabs },
    setup() {
      const activeTab = ref(1);

      return { args, activeTab };
    },
    template: `
      <TariffTabs v-model="activeTab" v-bind="args"></TariffTabs>
    `,
  }),
};
