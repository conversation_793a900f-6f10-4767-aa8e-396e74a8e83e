import { useOneSignal } from "@onesignal/onesignal-vue3";

const isDev = import.meta.env.DEV;

const OneSignal = useOneSignal();
const { showSlidedownPrompt, setExternalUserId, isPushNotificationsEnabled } =
  OneSignal;
import { useUserStore } from "@/stores/user";

export default {
  async subscribe(oneSignalHash: string | undefined) {
    if (isDev) return;
    await showSlidedownPrompt();

    const { user, getUser } = useUserStore();
    await getUser();
    if (oneSignalHash && user.uuid) {
      await setExternalUserId(user.uuid, oneSignalHash);
    }
  },
  initEvent(event: string, callback: Function) {
    if (isDev) return;
    OneSignal.on(event, (eventData: any) => {
      callback(eventData);
    });
  },
  async isSubscribed() {
    if (isDev) return false;
    return await isPushNotificationsEnabled();
  },
};
