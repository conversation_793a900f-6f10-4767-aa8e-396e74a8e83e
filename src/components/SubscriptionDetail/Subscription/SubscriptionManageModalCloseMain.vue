<script setup lang="ts">
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import type { TSubscriptionInfoResource } from "@/types/api/TSubscriptionInfoResource";
import { reactive, ref, watch, computed } from "vue";
import { useDateFormattedWithLocale } from "@/composable/useDateFormattedWithLocale";
import UICheckbox from "@/components/ui/UICheckbox/UICheckbox.vue";
import { currencyFormatter } from "@/helpers/currencyFormatter";
import { useSubscriptionsClosePost } from "@/composable/API/useSubscriptionsClosePost";
import { useI18n } from "vue-i18n";
import type { TSubscriptionResource } from "@/types/api/TSubscriptionResource";
import Divider from "../../ui/Divider/Divider.vue";
import UIRates from "@/components/ui/UIRates/UIRates.vue";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { useFeedbacksSubscriptionClosedPost } from "@/composable/API/useFeedbacksSubscriptionClosedPost";
import UITextarea from "@/components/ui/UITextarea/UITextarea.vue";

const { t } = useI18n();

const props = defineProps<{
  infoData: TSubscriptionInfoResource;
  subscriptionId: number;
  mainTariff?: TSubscriptionResource;
  extensions?: TSubscriptionResource[];
}>();

const emit = defineEmits<{
  close: [];
}>();

type CloseMainSubscriptionStages =
  | "start"
  | "activeCards"
  | "cashback"
  | "commission"
  | "rating"
  | "success";

const modalStages: Record<CloseMainSubscriptionStages, boolean> = {
  start: true,
  activeCards: props.infoData.cards_active_count > 0,
  cashback: Number(props.infoData.approved_cashback) > 0,
  commission: true,
  success: true,
  rating: true,
};

const modalTitles = reactive<Record<CloseMainSubscriptionStages, string>>({
  start: t("pst-private.modal.unsubscribe.title"),
  activeCards: t("pst-private.modal.block-cards.title"),
  cashback: t("pst-private.modal.lose-cashbak.title"),
  commission: t("pst-private.modal.upscale-fee.title"),
  success: t("pst-private.modal.unsubscribe-done.title"),
  rating: t("pst-private.modal.unsubscribe.button.please-give-me-rating"),
});

const currentModalTitle = ref<string>(modalTitles["start"]);

const checkBoxState = reactive({
  activeCards: false,
  cashback: false,
  commission: false,
});

const orderedNextDate = useDateFormattedWithLocale(
  props.infoData?.ordered_next!,
  "DD MMMM YYYY"
);

const currentCloseMainSubscriptionStage =
  ref<CloseMainSubscriptionStages>("start");

const disableSubscriptionNextHandle = (next: CloseMainSubscriptionStages) => {
  const nextIdx = Object.keys(modalStages).findIndex((elem) => elem === next);

  const nextStage = Object.entries(modalStages).find(([k, v], idx) => {
    if (idx >= nextIdx && v) {
      return [k, v];
    }
  });

  if (nextStage) {
    currentCloseMainSubscriptionStage.value =
      nextStage[0] as CloseMainSubscriptionStages;
  } else {
    currentCloseMainSubscriptionStage.value = next;
  }
};

const close = () => {
  emit("close");
};

const closeHandle = () => {
  currentCloseMainSubscriptionStage.value = "start";
  emit("close");
};

watch(currentCloseMainSubscriptionStage, (newVal) => {
  currentModalTitle.value = modalTitles[newVal];
});

const { error, isFetching, execute, data } = useSubscriptionsClosePost(
  {
    subscription_id: props.subscriptionId,
  },
  { immediate: false }
);

const btnSendRatingDisabled = computed(() => {
  return (
    !formRating.comment ||
    !formRating.rates.service_quality ||
    !formRating.rates.subscriptions_conditions ||
    !formRating.rates.support_operations
  );
});

const formRating = reactive({
  comment: "",
  rates: {
    service_quality: null,
    subscriptions_conditions: null,
    support_operations: null,
  },
});
const isSendRatingReqProcess = ref<boolean>(false);
const sendRating = async () => {
  isSendRatingReqProcess.value = true;
  const { data } = await useFeedbacksSubscriptionClosedPost(formRating);
  if (data.value?.success) {
    disableSubscriptionNextHandle("success");
  } else {
    isSendRatingReqProcess.value = false;
    useCallToast({
      title: t("errors.universal-request-error"),
      options: {
        type: TOAST_TYPE.ERROR,
        id: "unsubscribe-send-rating-error",
      },
    });
  }
};

const closeMainSubscriptionHandle = async () => {
  await execute();

  if (error.value) {
    return;
  }

  disableSubscriptionNextHandle("rating");
};

defineExpose({
  isFetching,
  currentCloseMainSubscriptionStage,
  currentModalTitle,
});
</script>

<template>
  <div>
    <!-- start  -->
    <div v-if="currentCloseMainSubscriptionStage === 'start'">
      <div class="my-5">
        <p>
          {{ $t("pst-private.subscriptionManageModalCloseMain.title") }}
          <span class="text-fg-red font-medium">{{
            $t("pst-private.subscriptionManageModalCloseMain.title2")
          }}</span>
        </p>

        <p>
          <span class="font-medium"
            >{{ $t("pst-private.SubscriptionManageModalCloseMain.title3") }}
            {{ orderedNextDate }}</span
          >
        </p>
      </div>

      <UIButton
        color="black"
        class="w-full"
        @click="closeHandle">
        {{ $t("pst-private.modal.unsubscribe.button.cancel") }}
      </UIButton>

      <UIButton
        color="red-light"
        class="w-full mt-2"
        @click="disableSubscriptionNextHandle('activeCards')">
        {{ $t("pst-private.modal.unsubscribe.button.uncibscribe-right-now") }}
      </UIButton>
    </div>

    <!-- active cards  -->
    <div v-if="currentCloseMainSubscriptionStage === 'activeCards'">
      <div class="mt-5">
        <p>
          {{ $t("pst-private.SubscriptionManageModalCloseMain.title4") }}
          <span class="font-medium">{{
            $t("pst-private.subscriptionManageModalCloseMain.title5")
          }}</span>
          {{ $t("pst-private.subscriptionManageModalCloseMain.title6") }}:
          <span class="font-medium">{{
            props.infoData.cards_active_count
          }}</span>
        </p>

        <p>
          {{ $t("pst-private.subscriptionManageModalCloseMain.allCards") }},
          <span class="text-fg-red font-medium">{{
            $t("pst-private.subscriptionManageModalCloseMain.title7")
          }}</span>
        </p>
      </div>

      <UICheckbox
        v-model="checkBoxState.activeCards"
        class="my-4">
        <template #label>
          <span class="ml-2"
            >{{
              $t(
                "pst-private.subscriptionManageModalCloseMain.activeCardsStage.checkbox"
              )
            }}
            {{ props.infoData.cards_active_count }}</span
          >
        </template>
      </UICheckbox>

      <UIButton
        color="black"
        class="w-full"
        @click="closeHandle">
        {{ $t("pst-private.modal.unsubscribe.button.cancel") }}
      </UIButton>

      <UIButton
        color="red-light"
        :disabled="!checkBoxState.activeCards"
        class="w-full mt-2"
        @click="disableSubscriptionNextHandle('cashback')">
        {{ $t("pst-private.modal.unsubscribe.button.uncibscribe-right-now") }}
      </UIButton>
    </div>

    <!-- cashback  -->
    <div v-if="currentCloseMainSubscriptionStage === 'cashback'">
      <div class="mt-5">
        <p>
          {{
            $t(
              "pst-private.subscriptionManageModalCloseMain.cashbackStage.title"
            )
          }}
          <span class="font-medium">{{
            currencyFormatter(props.infoData.approved_cashback, "USD", true)
          }}</span>
          {{
            $t(
              "pst-private.subscriptionManageModalCloseMain.cashbackStage.title2"
            )
          }}.
        </p>
        <p class="text-fg-red font-medium">
          {{
            $t("pst-private.subscriptionManageModalCloseMain.cashback.title3")
          }}
        </p>
      </div>

      <UICheckbox
        v-model="checkBoxState.cashback"
        class="my-4">
        <template #label>
          <span class="ml-2"
            >{{
              $t(
                "pst-private.subscriptionManageModalCloseMain.cashbackStage.title3"
              )
            }}
            {{
              currencyFormatter(props.infoData.approved_cashback, "USD", true)
            }}
            {{
              $t(
                "pst-private.subscriptionManageModalCloseMain.cashbackStage.title4"
              )
            }}</span
          >
        </template>
      </UICheckbox>

      <UIButton
        color="black"
        class="w-full"
        @click="closeHandle">
        {{ $t("pst-private.modal.unsubscribe.button.cancel") }}
      </UIButton>

      <UIButton
        color="red-light"
        class="w-full mt-2"
        :disabled="!checkBoxState.cashback"
        @click="disableSubscriptionNextHandle('commission')">
        {{ $t("pst-private.modal.unsubscribe.button.continue") }}
      </UIButton>
    </div>

    <!-- commission  -->
    <div v-if="currentCloseMainSubscriptionStage === 'commission'">
      <div class="my-4">
        <p
          v-if="error"
          class="text-fg-red text-3.5 my-2">
          {{ data?.message ?? "Server error" }}
        </p>

        <p>
          {{
            $t(
              "pst-private.subscriptionManageModalCloseMain.commissionStage.title"
            )
          }}
          <span class="font-medium"
            >{{
              $t(
                "pst-private.subscriptionManageModalCloseMain.commissionStage.title2"
              )
            }}
            {{ Number(props.infoData.fee_topup).toFixed() }}%</span
          >.
        </p>
        <p>
          {{
            $t(
              "pst-private.subscriptionManageModalCloseMain.commissionStage.title3"
            )
          }}
          <span class="text-fg-red font-medium">{{
            $t(
              "pst-private.subscriptionManageModalCloseMain.commissionStage.title4"
            )
          }}</span
          >.
        </p>
      </div>

      <UICheckbox
        v-model="checkBoxState.commission"
        class="my-4">
        <template #label>
          <span class="ml-2">
            {{
              $t(
                "pst-private.subscriptionManageModalCloseMain.commissionStage.title5"
              )
            }}</span
          >
        </template>
      </UICheckbox>

      <UIButton
        color="black"
        class="w-full"
        :disabled="isFetching"
        @click="closeHandle">
        {{ $t("pst-private.modal.unsubscribe.button.cancel") }}
      </UIButton>

      <UIButton
        color="red-light"
        :disabled="!checkBoxState.commission"
        :is-loading="isFetching"
        class="w-full mt-2"
        @click="closeMainSubscriptionHandle">
        {{ $t("pst-private.modal.unsubscribe.button.continue") }}
      </UIButton>
    </div>

    <!-- rating -->
    <div
      v-if="currentCloseMainSubscriptionStage === 'rating'"
      class="flex flex-col gap-5">
      <div class="flex flex-none flex-row justify-between items-center mt-5">
        <div class="text-4 text-primary leading-5 flex-wrap">
          {{ $t("pst-private.modal.unsubscribe.service-operation") }}
        </div>
        <UIRates v-model="formRating.rates.service_quality" />
      </div>
      <div class="flex flex-none flex-row justify-between items-center">
        <div class="text-4 text-primary leading-5 flex-wrap">
          {{
            $t(
              "pst-private.modal.unsubscribe.private-subscription-terms-conditions"
            )
          }}
        </div>
        <UIRates v-model="formRating.rates.subscriptions_conditions" />
      </div>
      <div class="flex flex-none flex-row justify-between items-center">
        <div class="text-4 text-primary leading-5">
          {{ $t("pst-private.modal.unsubscribe.support-work") }}
        </div>
        <div>
          <UIRates v-model="formRating.rates.support_operations" />
        </div>
      </div>

      <div>
        <UITextarea
          v-model="formRating.comment"
          :placeholder="
            $t(
              'pst-private.modal.unsubscribe.why-did-you-decide-unsubscribe-private'
            )
          "
          :native-max-length="255"
          label=""
          :total-characters-in-counter="255" />
      </div>
      <UIButton
        color="black"
        class="w-full"
        :disabled="btnSendRatingDisabled"
        :is-loading="isSendRatingReqProcess"
        @click="sendRating">
        {{ $t("pst-private.modal.unsubscribe.button.continue") }}
      </UIButton>
    </div>

    <!-- success  -->
    <div v-if="currentCloseMainSubscriptionStage === 'success'">
      <div class="p-4 bg-bg-level-1 mt-5">
        <ul class="flex flex-col gap-4">
          <li class="flex items-start justify-between">
            <p class="text-fg-secondary">
              {{
                $t("pst-private.subscriptionManageModalCloseMain.subscription")
              }}

              {{
                props.mainTariff?.subscription_tariff?.name?.replace("_A", "")
              }}
            </p>

            <p>
              {{ props.mainTariff?.subscription_tariff?.cards_limit }}

              {{
                props.mainTariff &&
                props.mainTariff?.subscription_tariff?.cards_limit > 1
                  ? $t("pst-private.subscriptionManageModalClose.cards")
                  : $t("pst-private.subscriptionManageModalClose.card")
              }}
            </p>
          </li>

          <li
            v-for="(item, idx) in props.extensions"
            :key="idx"
            class="flex items-start justify-between">
            <p class="text-fg-secondary">
              {{ item.subscription_tariff.name?.replace("_A", "") }}
            </p>

            <p>
              {{ item.subscription_tariff.cards_limit }}
              {{
                item.subscription_tariff &&
                item.subscription_tariff.cards_limit > 1
                  ? $t("pst-private.subscriptionManageModalClose.cards")
                  : $t("pst-private.subscriptionManageModalClose.card")
              }}
            </p>
          </li>
        </ul>

        <Divider
          type="dashed"
          class="my-7" />

        <div class="flex items-center justify-between">
          <p class="text-4.5 font-medium">
            {{ $t("pst-private.subscriptionManageModalCloseMain.totalCards") }}
          </p>

          <p class="text-4.5 font-medium">
            {{ props.infoData.cards_limit }}
          </p>
        </div>
      </div>

      <UIButton
        color="black"
        class="w-full mt-2"
        @click="close">
        {{ $t("pst-private.modal.unsubscribe-done.button.done") }}
      </UIButton>
    </div>
  </div>
</template>
