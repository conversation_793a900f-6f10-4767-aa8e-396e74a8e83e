export const TariffSlugArr = [
  "ultima",
  "ultima-annually-3ds",
  "ultima-weekly-3ds",
  "ultima-3ds",
  "ultima-annually",
  "ultima-semiannually",
  "ultima-quarterly",
  "ultima-weekly",

  "advertisement-cards",
  "tik-tok-cards",
  "google-cards",
  "facebook-cards",
  "fb-prem",
  "sigma_no_limits",
  "exclusive",
  "platinum-credit",
  "adv",
  "all",

  "pst-black-uniq-bin",
  "pst-black-prem",
  "pst-black",
  "3ds",
  // "3ds-pst-private",
  // "private",
] as const;

type TariffSlugTuple = typeof TariffSlugArr;

export type TCardTariffSlug = TariffSlugTuple[number];

export interface ICardTariff {
  id: number;
  name: string;
  slug: TCardTariffSlug;
  card_count_min?: number;
  card_count_max?: number;
  card_price?: string;
  replace_price?: string;
  start_balance?: string;
  duration?: number;
  type?: number;
  fee_topup?: string;
  fee_transaction_amount?: string;
  is_visible?: boolean;
  is_allowed_for_user?: boolean;
}

export interface ICardTariffTag {
  title: string;
  icon?:
    | "basket"
    | "stone"
    | "star"
    | "flash"
    | "speaker"
    | "fb"
    | "clock"
    | "network"
    | "private-manager"
    | "subscription"
    | string;
  style?: "default" | "orange" | "default-dark";
  color?: "default" | "orange" | "default-dark";
}

export interface ICardTariffE extends ICardTariff {
  tags?: ICardTariffTag[];
  url?: string;
  description?: string;
  triggers?: { title: string; value: string }[];
  buttons?: {
    title: string;
    url: string;
  }[];
  ui?: "dark" | "light";
}
