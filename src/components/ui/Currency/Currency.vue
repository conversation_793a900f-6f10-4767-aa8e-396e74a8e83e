<template>
  <div :class="[$style.UiCurrency, getCurrencyClass(props.currency)]">
    <component
      :is="currenciesIcons[props.currency].icon"
      class="w-5 h-auto flex items-center justify-center" />
  </div>
</template>

<script lang="ts" setup>
// @ts-ignore
import Btc from "@/assets/svg/icon/bitcoin-rounded.svg";
// @ts-ignore
import Usd from "@/assets/svg/icon/usd-circle.svg";
// @ts-ignore
import Tether from "@/assets/svg/icon/usdt-circle.svg";
// @ts-ignore
import Eur from "@/assets/svg/icon/eur-circle.svg";
import { useCssModule } from "vue";

type TCurrencyIcon = {
  icon: any;
  class: string;
};

interface Props {
  currency: string;
}

const props = withDefaults(defineProps<Props>(), {
  currency: "BTC",
});

const $style = useCssModule();
const currenciesIcons: { [key: string]: TCurrencyIcon } = {
  BTC: {
    icon: Btc,
    class: "UiCurrency__BTC",
  },
  USD: {
    icon: Usd,
    class: "UiCurrency__USD",
  },
  USDT: {
    icon: Tether,
    class: "UiCurrency__USDT",
  },
  EUR: {
    icon: Eur,
    class: "UiCurrency__EUR",
  },
};

// methods
const getCurrencyClass = (currency: string) =>
  $style[currenciesIcons[currency]?.class] || "";
</script>

<style module lang="scss">
.UiCurrency {
  @apply flex items-center justify-center;
  border-radius: 50%;

  &__BTC {
    background-color: #fefae6;
  }

  &__USDT {
    background-color: #f7fafc;
  }

  &__USD {
    background-color: #f0f3ff;
  }

  &__EUR {
    background-color: #fafafa;
  }
}
</style>
