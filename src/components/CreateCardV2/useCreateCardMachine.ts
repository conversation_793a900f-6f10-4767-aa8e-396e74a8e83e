import { computed } from "vue";
import { useMachine } from "@xstate/vue";
import { assign, fromPromise, setup } from "xstate";
import type {
  TCardForIssue,
  TCardType,
  TSelectedBin,
} from "@/components/CreateCardV2/types";
import type { TCardTariffSlug } from "@/composable/Tariff/types";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import { useAccountGet } from "@/composable/API/useAccountGet";
import { useSubscriptionsInfoGet } from "@/composable/API/useSubscriptionsInfoGet";
import { useUserStore } from "@/stores/user";
import type { TCardResource } from "@/types/api/TCardResource";
import { useBrowserLocation } from "@vueuse/core";
import { useUserSpecialCondition } from "@/composable/useUserSpecialCondition";
import {
  useCountrySets,
  useSubscriptionPlusCardAutobuy1Experiment,
  useVerificationActualGet,
  useVerificationState,
} from "@/composable";
import { useVerificationAvailableGet } from "@/composable/API/useVerificationAvailableGet";

const { getIsActiveAsync } = useSubscriptionPlusCardAutobuy1Experiment();

export enum CardCreateMachineState {
  INITIALIZATION = "initialization",
  SELECT_CARD_TYPE_STEP = "selectCardTypeStep",
  VERIFICATION_STEP = "verifyStep",
  SELECT_CARD_TARIFF_STEP = "selectCardTariffStep",
  SELECT_BIN_STEP = "selectBinStep",
  DEFAULT_CARD_ISSUE_STEP = "defaultCardIssueStep",
  ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP = "ultimaWithoutSubscriptionCardIssueStep",
  APPROVE_CARD_STEP = "approveCardStep",
  AUTO_BUY_PAYMENT_STEP = "autoBuyPaymentStep",
  SUCCESS_STEP = "successStep",
}

type CardCreateMachineEvents =
  | { type: "SELECT_CARD_TYPE"; value: TCardType }
  | { type: "VERIFICATION_COMPLETED" }
  | { type: "SELECT_CARD_TARIFF"; value: TCardTariffSlug }
  | { type: "SELECT_CARD_BIN"; value: TSelectedBin }
  | { type: "ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE"; value: TCardForIssue }
  | { type: "DEFAULT_CARD_ISSUE"; value: TCardForIssue }
  | { type: "SET_PROMO_CODE"; value: TPromoCodeResource | null }
  | { type: "SINGLE_BUY_SUCCESS"; value: TCardResource | null }
  | { type: "MULTI_BUY_SUCCESS" }
  | { type: "AUTO_BUY_SUCCESS" }
  | { type: "BACK" }
  | { type: "RESET" };

type CardCreateMachineContext = {
  isAutoBuy: boolean;
  isVerified: boolean;
  subscriptionsStatus: boolean;
  cardType: TCardType | null;
  cardTariff: TCardTariffSlug | null;
  selectedBin: string | null;
  cardForIssue: TCardForIssue | null;
  promoCodeData: TPromoCodeResource | null;
  resultCard: TCardResource | null;
  numSteps: number;
  step: number;
  userSpecialCondition: boolean;
  isAdvWithSubActive: boolean;
  lateVerification: boolean;
};

const initialContext: CardCreateMachineContext = {
  isAutoBuy: false,
  isVerified: false,
  subscriptionsStatus: false,
  cardType: null,
  cardTariff: null,
  selectedBin: null,
  cardForIssue: null,
  promoCodeData: null,
  resultCard: null,
  numSteps: 0,
  step: 1,
  userSpecialCondition: false,
  isAdvWithSubActive: false,
  lateVerification: false,
};

const createCardMachine = setup({
  types: {} as {
    context: CardCreateMachineContext;
    events: CardCreateMachineEvents;
  },
  actors: {
    initialize: fromPromise(
      async (): Promise<
        Pick<
          CardCreateMachineContext,
          | "isAutoBuy"
          | "isVerified"
          | "subscriptionsStatus"
          | "cardType"
          | "userSpecialCondition"
          | "numSteps"
          | "isAdvWithSubActive"
          | "lateVerification"
        >
      > => {
        const userStore = useUserStore();
        const [{ data: accountsData }, { data: subscriptionsData }] =
          await Promise.all([
            useAccountGet(),
            useSubscriptionsInfoGet(),
            useVerificationActualGet(),
            useVerificationAvailableGet(),
          ]);
        const subscriptionsStatus = !!subscriptionsData.value?.data?.status;
        const userHasMoney = accountsData.value?.data?.find(
          (item) => parseFloat(item.balance) > 0
        );
        const cardsCount = userStore.summary?.cards_count ?? 0;
        const userIsWarn = userStore.user.show_warn;
        const isTeamMember = userStore.isTeamMember;
        const userHasCards = cardsCount !== 0;
        const isAutoBuy =
          !userHasCards && !userHasMoney && !isTeamMember && !userIsWarn;
        const isVerified = userStore.user.is_verified ?? false;

        // user special condition
        const userSpecialCondition =
          await useUserSpecialCondition().getSpecialCondition();

        // adv with sub experiment
        const isAdvWithSubActive = await getIsActiveAsync();

        // needVerificationAction
        const { isActive: haveNeedVerificationAction } = useCountrySets();
        const { isOnlyUnlimited, refetchData, isVerificationAboveWelcome } =
          useVerificationState();
        await refetchData();

        const lateVerification =
          (haveNeedVerificationAction.value ||
            isOnlyUnlimited.value ||
            isVerificationAboveWelcome.value) &&
          !isTeamMember;

        // type from query params
        const location = useBrowserLocation();
        const searchParams = new URLSearchParams(location.value.search);
        const params = Object.fromEntries(searchParams.entries());
        const type = params.type;

        const numSteps = getTotalSteps(
          subscriptionsStatus,
          userSpecialCondition,
          (type as TCardType) || null,
          isVerified
        );

        return {
          isAutoBuy,
          isVerified,
          subscriptionsStatus,
          cardType: (type as TCardType) || null,
          userSpecialCondition: userSpecialCondition,
          numSteps,
          isAdvWithSubActive,
          lateVerification,
        };
      }
    ),
  },
  actions: {
    RESET: assign(initialContext),
    UPDATE_STEP_ON_EXIT: assign({
      step: ({ context, event }) => {
        if (event.type === "BACK") {
          return context.step - 1;
        } else {
          return context.step + 1;
        }
      },
    }),
    UPDATE_NEED_VERIFICATION_ACTION: assign({
      lateVerification: () => {
        const { isActive: haveNeedVerificationAction } = useCountrySets();
        const { isOnlyUnlimited } = useVerificationState();
        const { isTeamMember } = useUserStore();
        return (
          (haveNeedVerificationAction.value || isOnlyUnlimited.value) &&
          !isTeamMember
        );
      },
    }),
  },
}).createMachine({
  id: "create-card-machine",
  initial: CardCreateMachineState.INITIALIZATION,
  context: initialContext,
  states: {
    [CardCreateMachineState.INITIALIZATION]: {
      invoke: {
        id: "init",
        src: "initialize",
        onDone: [
          {
            target: CardCreateMachineState.VERIFICATION_STEP,
            guard: ({ event }) => {
              return !event.output.isVerified && !event.output.lateVerification;
            },
            actions: assign({
              isAutoBuy: ({ event }) => event.output.isAutoBuy,
              isVerified: ({ event }) => event.output.isVerified,
              subscriptionsStatus: ({ event }) =>
                event.output.subscriptionsStatus,
              userSpecialCondition: ({ event }) =>
                event.output.userSpecialCondition,
              cardType: ({ event }) => event.output.cardType,
              numSteps: ({ event }) => event.output.numSteps,
              isAdvWithSubActive: ({ event }) =>
                event.output.isAdvWithSubActive,
              lateVerification: ({ event }) => event.output.lateVerification,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
            guard: ({ event }) => {
              return (
                event.output.cardType === null &&
                !event.output.userSpecialCondition &&
                !event.output.subscriptionsStatus
              );
            },
            actions: assign({
              isAutoBuy: ({ event }) => event.output.isAutoBuy,
              isVerified: ({ event }) => event.output.isVerified,
              subscriptionsStatus: ({ event }) =>
                event.output.subscriptionsStatus,
              userSpecialCondition: ({ event }) =>
                event.output.userSpecialCondition,
              numSteps: ({ event }) => event.output.numSteps,
              isAdvWithSubActive: ({ event }) =>
                event.output.isAdvWithSubActive,
              lateVerification: ({ event }) => event.output.lateVerification,
            }),
          },

          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: ({ event }) => {
              return (
                ([null, "forAdv"].includes(event.output.cardType) &&
                  event.output.subscriptionsStatus) ||
                (!event.output.subscriptionsStatus &&
                  event.output.isAdvWithSubActive &&
                  [null, "forAdv"].includes(event.output.cardType)) ||
                (event.output.userSpecialCondition &&
                  !event.output.subscriptionsStatus)
              );
            },
            actions: assign({
              isAutoBuy: ({ event }) => event.output.isAutoBuy,
              isVerified: ({ event }) => event.output.isVerified,
              subscriptionsStatus: ({ event }) =>
                event.output.subscriptionsStatus,
              userSpecialCondition: ({ event }) =>
                event.output.userSpecialCondition,
              cardType: ({ event }) => event.output.cardType,
              numSteps: ({ event }) => event.output.numSteps,
              isAdvWithSubActive: ({ event }) =>
                event.output.isAdvWithSubActive,
              lateVerification: ({ event }) => event.output.lateVerification,
            }),
          },
          {
            target:
              CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP,
            guard: ({ event }) => {
              return event.output.cardType === "ultima";
            },
            actions: assign({
              isAutoBuy: ({ event }) => event.output.isAutoBuy,
              isVerified: ({ event }) => event.output.isVerified,
              subscriptionsStatus: ({ event }) =>
                event.output.subscriptionsStatus,
              userSpecialCondition: ({ event }) =>
                event.output.userSpecialCondition,
              cardType: ({ event }) => event.output.cardType,
              cardTariff: "ultima-3ds",
              numSteps: ({ event }) => event.output.numSteps,
              isAdvWithSubActive: ({ event }) =>
                event.output.isAdvWithSubActive,
              lateVerification: ({ event }) => event.output.lateVerification,
            }),
          },
          // {
          //   target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
          //   guard: ({ event }) => {
          //     return (
          //       event.output.cardType === null &&
          //       !event.output.userSpecialCondition &&
          //       !event.output.subscriptionsStatus
          //     );
          //   },
          //   actions: assign({
          //     isAutoBuy: ({ event }) => event.output.isAutoBuy,
          //     isVerified: ({ event }) => event.output.isVerified,
          //     showEmailVerificationStep: ({ event }) =>
          //       event.output.showEmailVerificationStep,
          //     subscriptionsStatus: ({ event }) =>
          //       event.output.subscriptionsStatus,
          //     userSpecialCondition: ({ event }) =>
          //       event.output.userSpecialCondition,
          //     numSteps: ({ event }) => event.output.numSteps,
          //     isAdvWithSubActive: ({ event }) =>
          //       event.output.isAdvWithSubActive,
          //   }),
          // },
        ],
      },
    },
    [CardCreateMachineState.VERIFICATION_STEP]: {
      exit: ["UPDATE_STEP_ON_EXIT", "UPDATE_NEED_VERIFICATION_ACTION"],
      on: {
        VERIFICATION_COMPLETED: [
          {
            target:
              CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP,
            guard: ({ context }) => {
              return context.cardType === "ultima" && !context.lateVerification;
            },
            actions: assign({
              isVerified: true,
              cardTariff: "ultima-3ds",
            }),
          },
          {
            target: CardCreateMachineState.SELECT_BIN_STEP,
            guard: ({ context }) =>
              context.cardType === "ultima" &&
              context.subscriptionsStatus &&
              context.cardForIssue === null,
            actions: assign({
              isVerified: true,
              cardTariff: "ultima-3ds",
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: ({ context }) => {
              return (
                (context.cardType === "forAdv" ||
                  context.userSpecialCondition) &&
                !context.lateVerification
              );
            },
            actions: assign({
              isVerified: true,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
            guard: ({ context }) => {
              return (
                context.cardType === null &&
                !context.userSpecialCondition &&
                !context.subscriptionsStatus &&
                !context.lateVerification
              );
            },
            actions: assign({
              isVerified: true,
            }),
          },
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP,
            guard: ({ context }) =>
              !context.isAutoBuy && context.lateVerification,
            actions: assign({
              isVerified: true,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP,
            guard: ({ context }) =>
              context.isAutoBuy && context.lateVerification,
            actions: assign({
              isVerified: true,
            }),
          },
        ],
        BACK: [
          {
            target:
              CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP,
            guard: ({ context }) =>
              context.cardType === "ultima" && context.lateVerification,
          },
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: ({ context }) =>
              context.cardType === "forAdv" && context.lateVerification,
          },
        ],
      },
    },

    [CardCreateMachineState.SELECT_CARD_TYPE_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SELECT_CARD_TYPE: [
          {
            target:
              CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP,
            guard: ({ event, context }) => {
              return !context.subscriptionsStatus && event.value === "ultima";
            },
            actions: assign({
              cardType: ({ event }) => event.value,
              cardTariff: "ultima-3ds",
              numSteps: ({ context }) => (context.subscriptionsStatus ? 5 : 4),
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: ({ event, context }) => {
              return event.value === "forAdv" && context.isAdvWithSubActive;
            },
            actions: assign({
              cardType: ({ event }) => event.value,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.SELECT_CARD_TARIFF_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SELECT_CARD_TARIFF: [
          {
            target:
              CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP,
            guard: ({ event }) => event.value === "ultima",
            actions: assign({
              cardTariff: ({ event }) => event.value,
              cardType: "ultima",
              numSteps: 3,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_BIN_STEP,
            actions: assign({
              cardTariff: ({ event }) => event.value,
              cardType: ({ event }) =>
                event.value === "ultima" ? "ultima" : "forAdv",
              numSteps: ({ context }) => {
                if (!context.subscriptionsStatus) {
                  return 4;
                } else {
                  return context.numSteps;
                }
              },
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.SELECT_BIN_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SELECT_CARD_BIN: [
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: ({ context }) => context.cardType === "ultima",
            actions: assign({
              selectedBin: ({ event }) => event.value.bin,
              cardTariff: ({ event }) => event.value.slug,
            }),
          },
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: ({ context }) => context.cardType === "forAdv",
            actions: assign({
              selectedBin: ({ event }) => event.value.bin,
              cardTariff: ({ event }) => event.value.slug,
            }),
          },
        ],
        BACK: [
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            actions: assign({
              cardTariff: null,
              cardType: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE: [
          {
            target: CardCreateMachineState.VERIFICATION_STEP,
            guard: ({ context }) => {
              return context.lateVerification;
            },
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP,
            guard: ({ context }) => !context.isAutoBuy,
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP,
            guard: ({ context }) => context.isAutoBuy,
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
        ],
        SELECT_CARD_TARIFF: {
          actions: assign({
            cardTariff: ({ event }) => event.value,
          }),
        },
        SET_PROMO_CODE: {
          actions: assign({
            promoCodeData: ({ event }) => event.value,
          }),
        },
        BACK: [
          {
            target: CardCreateMachineState.SELECT_CARD_TARIFF_STEP,
            guard: ({ context }) => {
              return (
                (context.userSpecialCondition &&
                  context.cardType === "ultima") ||
                (!context.subscriptionsStatus && context.isAdvWithSubActive)
              );
            },
            actions: assign({
              cardTariff: null,
              promoCodeData: null,
              cardType: null,
            }),
          },
          {
            target: CardCreateMachineState.SELECT_CARD_TYPE_STEP,
            guard: ({ context }) =>
              !context.subscriptionsStatus && !context.userSpecialCondition,
            actions: assign({
              cardTariff: null,
              promoCodeData: null,
              cardType: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        DEFAULT_CARD_ISSUE: [
          {
            target: CardCreateMachineState.VERIFICATION_STEP,
            guard: ({ context }) => context.lateVerification,
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.APPROVE_CARD_STEP,
            guard: ({ context }) => !context.isAutoBuy && context.isVerified,
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
          {
            target: CardCreateMachineState.AUTO_BUY_PAYMENT_STEP,
            guard: ({ context }) => context.isAutoBuy && context.isVerified,
            actions: assign({
              cardForIssue: ({ event }) => event.value,
            }),
          },
        ],
        SET_PROMO_CODE: {
          actions: assign({
            promoCodeData: ({ event }) => event.value,
          }),
        },
        BACK: {
          target: CardCreateMachineState.SELECT_BIN_STEP,
          actions: assign({
            selectedBin: null,
            promoCodeData: null,
          }),
        },
      },
    },

    [CardCreateMachineState.APPROVE_CARD_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        SINGLE_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP,
          actions: assign({
            resultCard: ({ event }) => event.value,
          }),
        },
        MULTI_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP,
        },
        BACK: [
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: ({ context }) => {
              return !(context.cardType === "ultima");
            },
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
          {
            target:
              CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP,
            guard: ({ context }) => context.cardType === "ultima",
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.AUTO_BUY_PAYMENT_STEP]: {
      exit: "UPDATE_STEP_ON_EXIT",
      on: {
        AUTO_BUY_SUCCESS: {
          target: CardCreateMachineState.SUCCESS_STEP,
        },
        BACK: [
          {
            target: CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP,
            guard: ({ context }) => {
              return !(context.cardType === "ultima");
            },
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
          {
            target:
              CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP,
            guard: ({ context }) => context.cardType === "ultima",
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
            }),
          },
        ],
      },
    },

    [CardCreateMachineState.SUCCESS_STEP]: {
      entry: assign({ numSteps: 0, step: 1 }),
    },
  },
});

export const useCreateCardMachine = () => {
  const { send, snapshot } = useMachine(createCardMachine);
  return {
    snapshot,
    send,
    step: computed(() => snapshot.value.value),
    context: computed(() => snapshot.value.context),
  };
};

function getTotalSteps(
  subscriptionsStatus: boolean,
  userSpecialCondition: boolean,
  cardType: TCardType | null,
  isVerified: boolean
): number {
  const paths = [
    {
      stepsCount: 2,
      conditions: [
        {
          subscriptionsStatus: false,
          userSpecialCondition: false,
          cardType: "ultima",
        },
      ],
    },
    {
      stepsCount: 3,
      conditions: [
        {
          subscriptionsStatus: false,
          userSpecialCondition: true,
          cardType: null,
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: false,
          cardType: "ultima",
        },
        {
          subscriptionsStatus: false,
          userSpecialCondition: false,
          cardType: null,
        },
      ],
    },
    {
      stepsCount: 4,
      conditions: [
        {
          subscriptionsStatus: false,
          userSpecialCondition: true,
          cardType: "forAdv",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: true,
          cardType: "ultima",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: true,
          cardType: "forAdv",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: false,
          cardType: "forAdv",
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: false,
          cardType: null,
        },
        {
          subscriptionsStatus: true,
          userSpecialCondition: true,
          cardType: null,
        },
      ],
    },
  ];
  let totalSteps = 0;

  for (const path of paths) {
    const match = path.conditions.some((condition) => {
      return (
        condition.subscriptionsStatus === subscriptionsStatus &&
        condition.userSpecialCondition === userSpecialCondition &&
        condition.cardType === cardType
      );
    });
    if (match) {
      totalSteps = path.stepsCount;
    }
  }
  if (!isVerified) totalSteps += 1;

  return totalSteps;
}
