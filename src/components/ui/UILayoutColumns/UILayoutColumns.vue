<template>
  <div :class="[`grid gap-2`, props.gridColsClass]">
    <div
      class="bg-bg-level-1 rounded p-4 flex flex-col items-start"
      v-for="(item, index) in props.items"
      :key="index">
      <slot
        name="col"
        :item="item">
        {{ item }}
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
type Props = {
  gridColsClass?: string;
  items: T[];
};

defineSlots<{
  col?: (props: { item: T }) => any;
}>();
const props = defineProps<Props>();
</script>
