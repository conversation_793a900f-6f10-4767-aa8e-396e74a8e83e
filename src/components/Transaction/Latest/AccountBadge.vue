<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { getAccountName, prepareAccountBalance } from "@/helpers";
import { computed } from "vue";
import { IsoCodeNames } from "@/constants/iso_code_names";

interface Props {
  account: any;
  rates: any;
}

const props = defineProps<Props>();
const getAccountUsdBalance = (account: {
  balance: string;
  _currency: { iso_code: string };
}) => {
  return (
    parseFloat(account.balance) *
    (props.rates[account._currency.iso_code]?.USD || 1)
  ).toFixed(2);
};

const accountUsdBalance = computed(() => {
  if (props.account?._currency?.iso_code === IsoCodeNames.USDT) {
    return prepareAccountBalance(parseFloat(props.account?.balance), "USDT");
  } else {
    return getAccountUsdBalance(props.account);
  }
});
</script>

<template>
  <div :class="accountBadge.root">
    <span
      :class="[
        accountBadge.icon,
        accountBadge[`bg-${props.account._currency.iso_code}`],
      ]">
      <DynamicIcon
        v-if="props.account?._currency"
        path="accounts"
        :name="`square-${props.account._currency.iso_code.toLowerCase()}`" />
    </span>
    <div :class="accountBadge.title">
      {{ getAccountName(props.account?._currency?.iso_code) }}
    </div>
    <div :class="accountBadge.amount">
      <span :class="accountBadge.value">
        {{
          prepareAccountBalance(
            props.account?.balance,
            props.account?._currency?.iso_code
          )
        }}
        {{ props.account?._currency?.iso_code }}
      </span>
      <span
        v-if="props.account?._currency?.iso_code !== IsoCodeNames.USD"
        :class="accountBadge.usdValue">
        {{ accountUsdBalance }} {{ IsoCodeNames.USD }}
      </span>
    </div>
  </div>
</template>

<style lang="scss" module="accountBadge">
.root {
  @apply flex items-center gap-3 w-full;
}

.icon {
  @apply flex items-center justify-center w-8 h-8;

  & svg {
    @apply w-full h-full;
  }
}

.title {
  @apply text-base font-medium;
}

.amount {
  @apply flex flex-col ml-auto text-right;
}

.value {
  @apply text-base font-medium leading-none;
}

.usdValue {
  @apply text-sm text-neutral-600 leading-none pt-1;
}

.bgUSDT {
  @apply bg-[#CCEEE4];
}

.bgUSD {
  @apply bg-[#E3F3FE];
}

.bgEUR {
  @apply bg-[#EFE9FB];
}

.bgBTC {
  @apply bg-[#FBEEDD];
}
</style>
