<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";

defineProps<{
  verified: boolean;
  cardsLimit: number;
}>();
defineEmits(["upgrade"]);
</script>

<template>
  <div class="unlimited-verification">
    <div class="block">
      <div class="header">
        <div class="title">
          {{ $t("verification.unlimited-verification-panel.deposit-title") }}
        </div>
        <div class="circle">
          <DynamicIcon
            name="blank-note-01"
            class="w-5 h-5 text-fg-primary" />
        </div>
      </div>
      <div class="subtitle">
        {{ $t("verification.unlimited-verification-panel.deposit-subtitle") }}
      </div>
    </div>
    <div
      v-if="cardsLimit > 0"
      class="block">
      <div class="header">
        <div class="title">
          {{
            $t("verification.unlimited-verification-panel.cards-title", {
              c: cardsLimit,
            })
          }}
        </div>
        <div class="circle">
          <DynamicIcon
            name="credit-card-02"
            class="w-5 h-5 text-fg-primary" />
        </div>
      </div>
      <div class="subtitle">
        {{
          $t("verification.unlimited-verification-panel.cards-subtitle", {
            c: cardsLimit,
          })
        }}
      </div>
    </div>

    <div
      v-if="verified"
      class="inline-flex items-center justify-center gap-1.5 mt-7 text-fg-green">
      <DynamicIcon
        name="check-verified-02"
        class="w-5 h-5" />
      <span class="text-4.5 leading-6 font-normal">
        {{ $t("verification.unlimited-verification-panel.verified-text") }}
      </span>
    </div>

    <UIButton
      v-if="!verified"
      class="w-full mt-7"
      color="black"
      @click="$emit('upgrade')">
      {{ $t("verification.unlimited-verification-panel.upgrade-button") }}
    </UIButton>
  </div>
</template>

<style scoped lang="scss">
.unlimited-verification {
  @apply flex flex-col gap-4;
  .block {
    @apply flex flex-col p-5 rounded-2xl w-full bg-bg-level-1 gap-4;
  }
  .title {
    @apply font-medium text-6 leading-7;
  }
  .circle {
    @apply flex w-9 h-9 min-w-9 items-center justify-center rounded-full bg-white;
  }
  .header {
    @apply flex justify-between items-center;
  }
  .subtitle {
    @apply text-4 leading-5;
  }
}
</style>
