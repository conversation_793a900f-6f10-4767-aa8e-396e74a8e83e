<!-- CriticalNotification.depricated -->
<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import SquareCheckbox from "@/components/ui/Checkbox/SquareCheckbox.vue";
import UiButton from "@/components/ui/Button/Button.vue";

import { useAxios } from "@/helpers";
import { useState } from "@/helpers/utilities";

interface Props {
  data: {
    notification: {
      id: number;
      notify_type_id: number;
      text: string;
      priority: string;
      read_at: null | string;
      created_at: string;
    };
    user: any;
  };
}

const [loading, setLoading] = useState<boolean>(false);
const [checked, setChecked] = useState<boolean>(false);
const props = defineProps<Props>();
const emit = defineEmits(["read"]);

const onReadNotification = async () => {
  try {
    setLoading(true);

    const result = await useAxios().post(
      `/user/notification/${props.data.notification.id}/read`
    );

    if (result.status === 200) {
      emit("read", props.data.notification.id);
    }
  } catch (ex) {
    console.warn(
      "CriticalNotification->onReadNotification error handled: ",
      ex
    );
  } finally {
    setLoading(false);
  }
};
</script>

<template>
  <div :class="$style.root">
    <div class="flex gap-5">
      <div
        class="flex items-center justify-center p-3 rounded-full relative w-13 h-13 bg-bg-level-1">
        <DynamicIcon
          class="w-6 h-6"
          name="card-v2" />

        <div
          class="flex items-center justify-center w-6 h-6 bg-bg-yellow-light absolute -right-1 -bottom-1 rounded-full">
          <DynamicIcon
            name="alert-circle"
            class="w-4 h-4" />
        </div>
      </div>

      <div class="flex flex-col gap-3">
        <span class="text-fg-primary text-xl font-semibold">
          {{ $t("criticalNotification.card") }}
        </span>

        <span class="text-fg-primary text-4">
          {{ data.notification.text }}
        </span>

        <SquareCheckbox
          :checked="checked"
          :disabled="checked"
          @change="setChecked">
          <template #label>
            <span class="text-4 text-fg-secondary font-normal">
              {{ $t("criticalNotification.agree") }}
            </span>
          </template>
        </SquareCheckbox>

        <UiButton
          :disabled="!checked || loading"
          :class="$style.button"
          :title="$t('criticalNotification.buttonAgree')"
          @click="onReadNotification" />
      </div>
    </div>
  </div>
</template>

<style module lang="scss">
.root {
  @apply p-5 rounded-2xl bg-bg-level-0;
  box-shadow: 32px 33px 19px rgba(0, 0, 0, 0.01),
    18px 19px 16px rgba(0, 0, 0, 0.02), 8px 8px 12px rgba(0, 0, 0, 0.03),
    2px 2px 6px rgba(0, 0, 0, 0.04), 0px 0px 0px rgba(0, 0, 0, 0.04);
}
.button {
  @apply font-normal;
}
</style>
