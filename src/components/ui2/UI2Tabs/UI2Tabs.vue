<script generic="T extends UI2TabsOption" setup lang="ts">
import { computed, onUpdated, reactive, ref } from "vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { vElementSize } from "@vueuse/components";
import type {
  UI2TabsOption,
  UI2TabsProps,
} from "@/components/ui2/UI2Tabs/types";

const props = withDefaults(defineProps<UI2TabsProps<T>>(), {
  size: "m",
});

const activeTab = defineModel<string | number>({ required: true });

defineSlots<{
  tab(slotProps: { tab: T }): any;
}>();

const tabsRef = ref<HTMLElement[]>([]);

const markerPosition = reactive({
  width: 0,
  offsetLeft: 0,
  offsetTop: 0,
  height: 0,
});

const activeTabIndex = computed(() => {
  return props.options.findIndex((item) => item.value === activeTab.value);
});

const activeTabElement = computed<HTMLElement | undefined>(
  () => tabsRef.value[activeTabIndex.value]
);

const updateMarkerPosition = () => {
  markerPosition.width = activeTabElement.value?.offsetWidth ?? 0;
  markerPosition.height = activeTabElement.value?.offsetHeight ?? 0;
  markerPosition.offsetLeft = activeTabElement.value?.offsetLeft ?? 0;
  markerPosition.offsetTop = activeTabElement.value?.offsetTop ?? 0;
};

onUpdated(() => updateMarkerPosition());

const onResize = () => updateMarkerPosition();
</script>

<template>
  <div
    v-element-size="onResize"
    class="ui-tabs p-0.5 gap-0.5 w-fit">
    <label
      v-for="(item, i) in props.options"
      :key="`${item.value}${i}`"
      ref="tabsRef"
      :class="{
        active: i === activeTabIndex,
        'min-h-10': props.size === 'l',
        'min-h-8': props.size === 'm',
        'ui-tabs__item': !Boolean($slots.tab),
      }"
      class="z-2 cursor-pointer"
      :for="`${item.value}${i}`">
      <input
        :id="`${item.value}${i}`"
        type="radio"
        :checked="activeTab === item.value"
        class="absolute hidden"
        :value="item?.value"
        @change="activeTab = item.value" />
      <slot
        name="tab"
        :tab="item">
        <p
          v-if="item.leftIcon"
          class="min-w-5">
          <DynamicIcon :name="item.leftIcon" />
        </p>
        <p
          v-if="item.label"
          :class="{
            'text-3.5': props.size === 'm',
            'text-4': props.size === 'l',
          }">
          {{ item.label }}
        </p>
        <p
          v-if="item.rightIcon"
          class="min-w-5">
          <DynamicIcon :name="item.rightIcon" />
        </p>
      </slot>
    </label>
    <div
      class="marker"
      :style="{
        width: `${markerPosition.width}px`,
        height: `${markerPosition.height}px`,
        transform: `translate(${markerPosition.offsetLeft}px, ${markerPosition.offsetTop}px)`,
      }" />
  </div>
</template>

<style lang="scss" scoped>
.ui-tabs {
  @apply rounded-lg bg-bg-overlay-custom1 flex relative;

  box-shadow: 0 0 4px 0 rgba(21, 24, 30, 0.08) inset;

  &__item {
    @apply flex gap-1.5 flex-1 justify-center items-center px-3
    cursor-pointer
    text-fg-secondary hover:text-fg-primary transition-colors;
  }

  &__item.active {
    @apply text-fg-primary;
  }

  .marker {
    @apply absolute left-0 top-0 bg-bg-button-default-second rounded;
    transition: transform 0.5s cubic-bezier(0.11, 0.11, 0.19, 1.13),
      width 0.3s cubic-bezier(0.42, 0, 0.24, 1.01);
    box-shadow: 0 2px 2px 0 rgba(21, 24, 30, 0.06);
  }
}
</style>
