<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

import Env from "@/config/env";
import { getFormattedDate } from "@/helpers/time";
import { computed, watch } from "vue";
import { useState } from "@/helpers/utilities";
import type { TPromoCode } from "@/types/promoCode";

interface Props {
  template: string;
  data: TPromoCode;
}

const [copied, setCopied] = useState<string>("");
const props = defineProps<Props>();
const emit = defineEmits(["edit"]);

const refUrl = computed<string>(
  () => `${Env.landingUrl}?f=${props.data?.code}`
);
const copyValue = (value: string) =>
  navigator.clipboard.writeText(value).then(() => setCopied(value));

watch(
  () => copied.value,
  () => setTimeout(() => setCopied(""), 3000)
);
</script>

<template>
  <div :class="[$style.root, template]">
    <div class="flex items-center gap-1.5">
      <span :class="$style.text">{{ data.code }}</span>

      <div
        v-tooltip="{
          content: copied === data.code ? $t('Copied') : $t('Copy'),
        }"
        class="w-6 h-6 flex items-center justify-center"
        @click="() => copyValue(data.code)">
        <DynamicIcon
          name="copy"
          class="w-3 h-3 fill-neutral-600 cursor-pointer" />
      </div>
    </div>

    <div>
      <span
        v-tooltip="{
          content: copied === refUrl ? $t('Copied') : $t('Copy'),
        }"
        :class="[$style.text, 'cursor-pointer']"
        @click="() => copyValue(refUrl)">
        {{ refUrl }}
      </span>
    </div>

    <span :class="$style.text">
      {{ data.created_at ? getFormattedDate(new Date(data.created_at)) : "" }}
    </span>

    <div :class="$style.button">
      <DynamicIcon
        v-if="!!data.created_at"
        name="edit"
        class="w-5 h-5 min-h-[20px]"
        @click="emit('edit')" />
    </div>
  </div>
</template>

<style module lang="scss">
.root {
  @apply items-center;
}

.text {
  @apply text-base text-[#313438];
}

.button {
  @apply bg-[#D6D7D9] rounded-xl p-1.5 cursor-pointer h-fit min-h-[32ox] w-fit;
}
</style>
