import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import type { ComponentProps } from "vue-component-type-helpers";

import SupportContact from "./SupportContact.vue";

const meta: Meta<ComponentProps<typeof SupportContact>> = {
  component: SupportContact,
  tags: ["autodocs"],
  title: "DASHBOARD/SupportContact",
  argTypes: {
    onClickTelegram: { action: "Click on the telegram link" },
    onClickWhatsApp: { action: "Click on WhatsApp link" },
    mode: {
      options: ["light", "dark"],
    },
  },
};

export default meta;
type Story = StoryObj<ComponentProps<typeof SupportContact>>;

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Primary: Story = {
  render: (args) => ({
    components: { SupportWidget: SupportContact },
    setup() {
      return { args };
    },
    template: `
      <SupportWidget v-bind="args">
        <template #description>
          We will help you collect the necessary
          documents and are guaranteed to pass verification in PSTNET
        </template>
      </SupportWidget>`,
  }),
  args: {
    isAssignedManager: false,
    titleColor: "black",
    title: "Sales Manager",
    telegramLink: "https://t.me/?test",
    whatsAppLink: "https://wa.me/?test",
  },
};
