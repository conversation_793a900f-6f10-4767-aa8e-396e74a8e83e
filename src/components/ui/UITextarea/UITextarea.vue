<script setup lang="ts">
import { computed, watch } from "vue";
import { useTextareaAutosize } from "@vueuse/core";
import { useAutoAnimate } from "@formkit/auto-animate/vue";

type TextareaProps = {
  error?: string | null;
  helperText?: string;
  label?: string;
  nativeMaxLength?: number;
  totalCharactersInCounter?: number;
  placeholder?: string;
  rows?: number;
};

const props = withDefaults(defineProps<TextareaProps>(), {
  error: null,
  helperText: undefined,
  label: undefined,
  nativeMaxLength: undefined,
  totalCharactersInCounter: undefined,
  placeholder: undefined,
  rows: 1,
});

const modelValue = defineModel<string | number | null>({
  default: null,
});

const { textarea, input: textareaInput } = useTextareaAutosize({
  styleProp: "minHeight",
});
const [helperTextWrap] = useAutoAnimate({ duration: 100 });

const emit = defineEmits<{
  characterCounting: [count: number];
  blur: [];
  input: [value: string];
}>();

const charactersCount = computed(() => {
  if (modelValue.value) {
    return String(modelValue.value).length;
  } else {
    return 0;
  }
});

const onInput = (event: Event) => {
  emit("input", (event.target as HTMLInputElement).value);
};

watch(charactersCount, (newVal) => {
  emit("characterCounting", newVal);
});

watch(modelValue, (newVal) => {
  textareaInput.value = String(newVal);
});
</script>

<template>
  <div
    class="wrap"
    :class="{
      error: props.error,
    }">
    <span
      v-if="props.label"
      class="label">
      {{ props.label }}
    </span>
    <textarea
      ref="textarea"
      v-model="modelValue"
      class="textarea"
      :maxlength="props.nativeMaxLength"
      :placeholder="props.placeholder"
      :rows="props.rows"
      @blur="emit('blur')"
      @input="onInput"></textarea>

    <div
      ref="helperTextWrap"
      class="helper-text-wrap">
      <span
        v-if="props.error"
        class="error">
        {{ props.error }}
      </span>

      <span
        v-else-if="props.helperText"
        class="helper-text">
        {{ props.helperText }}
      </span>

      <span
        v-if="props.totalCharactersInCounter"
        class="count">
        {{ charactersCount }} / {{ props.totalCharactersInCounter }}
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.textarea {
  @apply appearance-none px-3 py-2 border border-bg-level-2 rounded resize-none 
  w-full focus:border-fg-primary active:border-fg-primary focus-visible:border-fg-primary focus-visible:outline-0;
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.label {
  @apply text-fg-secondary leading-4 transition-colors;
}

.wrap {
  @apply flex flex-col items-stretch gap-1;
}

.wrap.error {
  .label {
    @apply text-fg-red;
  }

  .textarea {
    @apply border-fg-red transition-colors;
  }

  .error {
    @apply text-fg-red;
  }
}

.helper-text-wrap {
  @apply flex items-center justify-between text-3.5;
}

.count {
  @apply text-3.5 leading-4 text-fg-secondary ml-auto;
}

.helper-text {
  @apply text-3.5 text-fg-secondary;
}
</style>
