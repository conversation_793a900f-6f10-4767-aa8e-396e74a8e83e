<script setup lang="ts">
interface Props {
  stages?: number;
  activeIndex?: number;
  type?: "progress" | "tabs";
}
const props = withDefaults(defineProps<Props>(), {
  stages: 1,
  activeIndex: 0,
  type: "progress",
});
</script>
<template>
  <!-- progress type  -->
  <div
    :class="[$style.steps_progress, `grid-cols-${props.stages}`]"
    v-if="props.type === 'progress'">
    <div
      v-for="(item, index) in props.stages"
      :key="index"
      :class="{
        [$style.active]: index < props.stages && index <= activeIndex,
      }"></div>
  </div>
  <!-- tab type  -->
  <div
    :class="[$style.steps_progress, `grid-cols-${props.stages}`]"
    v-if="props.type === 'tabs'">
    <div
      v-for="(item, index) in props.stages"
      :key="index"
      :class="{
        [$style.active]: index === activeIndex,
      }"></div>
  </div>
</template>
<style module lang="scss">
.steps_progress {
  @apply grid gap-3 w-full;
  > div {
    @apply h-[3px] bg-bg-level-2 transition-colors;
  }
  .active {
    @apply bg-bg-contrast;
  }
}
</style>
