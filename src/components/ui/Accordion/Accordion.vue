<template>
  <div>
    <div>
      <slot
        :is-open="isActive"
        name="header">
        Please provide accordion header
      </slot>
    </div>

    <transition
      name="accordion"
      @before-enter="setHeightToZero"
      @enter="setHeightToScrollHeight"
      @before-leave="setHeightToScrollHeight"
      @leave="setHeightToZero">
      <div
        v-show="isActive"
        :class="$style.body">
        <slot name="body"> Please provide accordion body </slot>
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  isActive?: boolean;
}

const props = defineProps<Props>();

// methods for transition
const setHeightToScrollHeight = (el: any) =>
  (el.style.height = el.scrollHeight + "px");
const setHeightToZero = (el: any) => (el.style.height = "0");
</script>

<style module lang="scss">
.body {
  overflow: hidden;
  transition: all 0.3s ease-out;
}
</style>
