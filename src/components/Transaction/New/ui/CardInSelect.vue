<script setup lang="ts">
import CardsIcon from "@/components/Cards/CardsIcon.vue";

interface Props {
  card?: any;
}

const props = withDefaults(defineProps<Props>(), {
  card: undefined,
});
</script>

<template>
  <div
    class="cursor-pointer flex items-center gap-3 w-full"
    data-cy="card-item">
    <CardsIcon
      :card="props.card"
      size="small" />

    <div class="flex flex-col text-base max-w-[80px] md:max-w-[172px]">
      <span
        class="font-extrabold truncate"
        :class="{ 'text-greyscale-300': props.card.status === 10 }"
        data-cy="card-tariff"
        >{{ props.card._tariff }}</span
      >
      <span
        class="font-medium text-greyscale-600 truncate"
        data-cy="card-description">
        <template v-if="props.card.description">
          {{ props.card.description }}
        </template>
        <template v-else> N/A</template>
      </span>
    </div>

    <div class="flex flex-col text-base ml-auto text-right">
      <span
        class="font-medium text-greyscale-600"
        data-cy="card-mask"
        >{{ props.card.mask }}</span
      >
      <span
        class="font-medium"
        data-cy="card-balance">
        {{ props.card.account.balance }}
        {{ props.card.account._currency.iso_code }}
      </span>
    </div>
  </div>
</template>
