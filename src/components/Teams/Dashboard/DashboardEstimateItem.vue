<template>
  <div :class="[$style.root, template]">
    <!--  Name  -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.estimate.header.name") }}
      </span>

      <span
        class="overflow-ellipsis overflow-hidden cursor-pointer underline"
        @click="onMemberClick">
        {{ data.name }}
      </span>
    </div>

    <!--  Amount   -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.estimate.header.amountPeriod") }}
      </span>

      <span class="overflow-ellipsis overflow-hidden">
        {{
          balanceWithSymbol(Number(data.amount), {
            symbol: "$",
            iso_code: "USD",
          })
        }}
      </span>
    </div>

    <!--  Account balance  -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.estimate.header.accountsBalance") }}
      </span>

      <div
        class="flex items-center gap-2"
        :class="isDatesChanged && 'opacity-50'">
        <DynamicIcon
          name="wallet"
          class="min-h-6 min-w-6 w-6 h-6 text-neutral-200" />

        <span class="whitespace-nowrap">
          {{
            balanceWithSymbol(Number(data.accountBalance), {
              symbol: "$",
              iso_code: "USD",
            })
          }}
        </span>
      </div>
    </div>

    <!--  Card balance  -->
    <div class="flex justify-between items-start gap-2">
      <span class="visible md:hidden text-gray-600 font-semibold">
        {{ $t("team.dashboard.estimate.header.cardsBalance") }}
      </span>

      <div
        class="flex items-center gap-2"
        :class="isDatesChanged && 'opacity-50'">
        <DynamicIcon
          name="card"
          class="min-h-6 min-w-6 w-6 h-6 text-neutral-200" />

        <span class="whitespace-nowrap">
          {{
            balanceWithSymbol(Number(data.cardBalance), {
              symbol: "$",
              iso_code: "USD",
            })
          }}
        </span>

        <template v-if="data.activeCards">
          <span class="w-1 h-1 rounded-full bg-neutral-300" />

          <span
            v-if="data.activeCards"
            class="rounded-[6px] px-[6px] bg-neutral-100 text-base text-neutral-600">
            {{ data.activeCards }}
          </span>
        </template>
      </div>
    </div>

    <div
      v-if="!data?.isMaster"
      class="grid grid-cols-2 gap-2">
      <UiButton
        v-if="!data?.isDeleted"
        :title="$t('buttons.deposit')"
        size="small"
        class="col-span-2 lg:col-span-1"
        type="gray"
        :disabled="disabled"
        @click="onDeposit" />

      <UiButton
        :title="$t('buttons.withdraw')"
        size="small"
        type="gray-outline"
        class="col-span-2 lg:col-span-1"
        :disabled="disabled"
        @click="onWithdraw" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import { balanceWithSymbol } from "@/helpers/account";
import { useRouter } from "vue-router";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import type { TEstimateItem } from "@/components/Teams/Dashboard/types";

import { computed } from "vue";
import { getFormatDate } from "@/helpers/time";
import type { TDatePickerDates } from "@/types/date";

interface Props {
  template: string;
  data: TEstimateItem;
  disabled: boolean;
  dates?: TDatePickerDates<Date>;
}

const router = useRouter();
const emit = defineEmits(["withdraw", "deposit"]);
const props = defineProps<Props>();

// computed
const isDatesChanged = computed<boolean>(() => {
  const yesterdayDate = new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    new Date().getDate() - 1
  ).getDate();

  return (
    !(
      props.dates?.end && new Date(props.dates.end).getDate() === yesterdayDate
    ) ||
    !(
      props.dates?.start &&
      new Date(props.dates.start).getDate() === yesterdayDate
    )
  );
});

// methods
const onDeposit = () => emit("deposit", props.data.id);
const onWithdraw = () => emit("withdraw", props.data.id);
const onMemberClick = () => {
  const pushArg: { path: string; query?: { u?: any; ds?: any; de?: any } } = {
    path: `/team/payments`,
  };

  if (!props.data.isMaster) {
    pushArg.query = { u: props.data.id };
  }

  if (props.dates?.start) {
    pushArg.query = {
      ...pushArg.query,
      ds: getFormatDate(props.dates.start),
    };
  }

  if (props.dates?.end) {
    pushArg.query = {
      ...pushArg.query,
      de: getFormatDate(props.dates.end),
    };
  }

  router.push(pushArg);
};
</script>

<style lang="scss" module>
.root {
  @apply grid bg-white;
}
</style>
