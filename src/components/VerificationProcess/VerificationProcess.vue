<script setup lang="ts">
import { useVerificationProcess } from "@/components/VerificationProcess/useVerificationProcess";
import WelcomeVerification from "@/components/WelcomeVerification/WelcomeVerification.vue";
import ScaleVerification from "@/components/VerificationProcess/process/ScaleVerification.vue";
import UnlimitedVerification from "@/components/VerificationProcess/process/UnlimitedVerification.vue";
import { computed } from "vue";
import Loader from "@/components/ui/Loader/Loader.vue";

const { nextVerificationTier, isLoading } = useVerificationProcess();

const emit = defineEmits<{
  success: [value: boolean];
}>();

const onSuccessHandle = () => {
  emit("success", true);
};

const isWelcomeVerificationVisible = computed(() => {
  return nextVerificationTier.value === "welcome";
});
</script>

<template>
  <div class="verification-process">
    <div
      v-if="isLoading"
      class="fixed inset-0 flex items-center justify-center">
      <Loader />
    </div>
    <div v-if="!isLoading">
      <div
        v-if="isWelcomeVerificationVisible"
        class="max-w-[27.5rem] md:min-w-[440px]">
        <WelcomeVerification @success="onSuccessHandle" />
      </div>
      <div v-if="nextVerificationTier === 'scale'">
        <ScaleVerification @success="onSuccessHandle" />
      </div>
      <div v-if="nextVerificationTier === 'unlimited'">
        <UnlimitedVerification @close="onSuccessHandle" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.verification-process {
  @apply flex flex-col items-center justify-around w-full h-full;
}
</style>
