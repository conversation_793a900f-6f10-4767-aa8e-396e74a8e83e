<template>
  <div
    class="flex w-full items-start gap-5 self-stretch bg-bg-level-1 p-4 sm:p-10 rounded-md flex-col">
    <div class="flex flex-none w-full items-center align-center">
      <h4
        class="text-neutral-800 text-[2.563rem] sm:text-5xl font-bold leading-[52px] mx-auto">
        {{ $t("faq.have-questions") }}
      </h4>
    </div>
    <ul
      class="flex flex-none flex-col gap-5 w-full mt-10 md:px-[4.375rem] lg:px-[8.75rem]">
      <template
        v-for="item of questions"
        :key="item.id">
        <li
          class="flex border-b-neutral-150 border-b border-solid flex-col pb-5 w-full">
          <div
            class="flex cursor-pointer text-fg-primary text-5 font-medium leading-6 flex-none flex-row w-full"
            @click="item.isOpen = !item.isOpen">
            <div class="flex flex-auto mr-2">
              {{ $t(`faq.item.40${item.id}.title`) }}
            </div>
            <div class="flex flex-none mt-2">
              <svg
                :class="[
                  'transition-transform duration-150',
                  item.isOpen ? 'rotate-180' : '',
                ]"
                class="cursor-pointer hover:opacity-80"
                fill="none"
                height="7"
                viewBox="0 0 12 7"
                width="12"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  clip-rule="evenodd"
                  d="M0.898953 0.398862C1.2309 0.0669159 1.76909 0.0669159 2.10103 0.398862L5.99999
                      4.29782L9.89895 0.398862C10.2309 0.0669159 10.7691
                      0.0669159 11.101 0.398862C11.433 0.730807 11.433 1.269 11.101 1.60094L6.60103
                       6.10094C6.26909 6.43289 5.7309 6.43289 5.39895
                        6.10094L0.898953 1.60094C0.567007 1.269 0.567007 0.730807 0.898953 0.398862Z"
                  fill="#15191D"
                  fill-rule="evenodd" />
              </svg>
            </div>
          </div>

          <UiCollapsible
            open-class="mt-3"
            :is-open="item.isOpen">
            <div
              class="question-content"
              v-html="$t(`faq.item.40${item.id}.content`)" />
          </UiCollapsible>
        </li>
      </template>
    </ul>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import UiCollapsible from "@/components/ui/UICollapsible/UICollapsible.vue";

const questions = ref([
  { id: 1, isOpen: false },
  { id: 2, isOpen: false },
  { id: 3, isOpen: false },
  { id: 4, isOpen: false },
  { id: 5, isOpen: false },
  { id: 6, isOpen: false },
]);
</script>

<style lang="scss" scoped>
.question-content {
  @apply text-fg-primary text-4.5 leading-6;

  & > ul {
    @apply list-disc pl-4 py-4;
    & > li {
      @apply pb-1;
    }
  }

  & > a {
    @apply underline text-greyscale-900;
  }
}

.questions__ul__li__description__link {
  @apply no-underline border-b border-solid;
}
</style>
