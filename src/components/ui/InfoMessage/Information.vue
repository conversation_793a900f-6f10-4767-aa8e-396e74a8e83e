<template>
  <div
    class="bg-greyscale-50 rounded-base px-4 py-3 text-base text-greyscale-600 font-extrabold flex items-center gap-4">
    <DynamicIcon
      :name="iconName"
      class="w-8 h-8 min-w-[2rem]" />

    <span>
      <slot />
    </span>
  </div>
</template>

<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  iconName?: string;
}

const props = withDefaults(defineProps<Props>(), {
  iconName: "alert-circle",
});
</script>

<style lang="sass" module></style>
