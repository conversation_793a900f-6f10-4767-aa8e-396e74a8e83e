<script setup lang="ts">
import type { TUserSettings } from "@/types/user/user.types";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  settings?: TUserSettings;
}

const props = defineProps<Props>();

// methods
const openTelegramBot = () => {
  window.open(
    // eslint-disable-next-line max-len
    `https://t.me/${props.settings?.telegram?.bot}?${props.settings?.telegram?.command}=${props.settings?.telegram?.code}`,
    "_blank"
  );
};
function openWhatsAppHandler() {
  window.open(
    // eslint-disable-next-line max-len
    `https://wa.me/${props.settings?.whatsapp?.bot}?text=/${props.settings?.whatsapp?.command} ${props.settings?.whatsapp?.code}`,
    "_blank"
  );
}
</script>

<template>
  <div :class="$style.root">
    <h4>
      {{ $t("notification.telegram.titleDesktop") }}
    </h4>
    <p>
      {{ $t("notification.telegram.text") }}
    </p>
    <div class="flex items-center justify-between flex-row md:flex-col gap-2">
      <button
        class="text-sm font-semibold rounded-xl bg-bg-level-2 w-full py-4 text-fg-primary"
        @click="openTelegramBot">
        <DynamicIcon
          name="telegram-color-circle"
          class="w-5 h-5" />
        <span class="hidden md:block ml-1.5">{{ $t("btn.gotoTelegram") }}</span>
      </button>
      <button
        v-if="props.settings?.whatsapp"
        class="text-sm font-semibold rounded-xl bg-bg-level-2 text-fg-primary w-full py-4"
        @click="openWhatsAppHandler">
        <DynamicIcon
          name="whatsapp_colored"
          class="w-5 h-5" />
        <span class="hidden md:block ml-1.5">{{ $t("btn.gotoWhatsApp") }}</span>
      </button>
    </div>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply bg-bg-level-1 px-5 py-4.5 rounded flex flex-col;

  & h4 {
    @apply mb-1 text-fg-primary text-4.5 font-normal leading-6;
  }

  & p {
    @apply text-fg-secondary text-3 not-italic font-normal leading-4 mb-5;
  }

  & button {
    @apply font-semibold h-8 text-sm rounded-full bg-bg-level-2 w-full py-4 text-fg-primary hover:bg-bg-level-2-hover
    active:bg-bg-level-2-clicked transition-colors;
    & span {
      @apply text-3 font-semibold leading-4;
    }
  }
}
</style>
