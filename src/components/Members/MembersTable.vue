<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { Skeletor } from "vue-skeletor";

import { useBusinessDashboardGet, useEventHooks } from "@/composable";
import { RouteName } from "@/constants/route_name";
import MembersTableFilter from "@/components/Members/MembersTableFilter.vue";
import {
  MEMBER_DASHBOARD_SORT,
  type TMemberDashboardSort,
} from "@modules/services/userBusiness/model";
import { useMembersState } from "@/components/Members/useMembersState";
import MembersTableItem from "@/components/Members/MembersTableItem.vue";
import MembersModalRename from "@/components/Members/MembersModalRename.vue";
import MembersDeleteModal from "@/components/Members/MembersDeleteModal.vue";
import UIPagination from "@/components/ui/UIPagination/UIPagination.vue";
import UISelect from "@/components/ui/UISelect/UISelect.vue";
import type {
  IUITableSortModel,
  TUITableColumn,
} from "@/components/ui/UITableV2/types";
import UiNotFound from "@/components/ui/UiNotFound.vue";
import UITableV2 from "@/components/ui/UITableV2/UITableV2.vue";
import type { TTeamMemberDashboard } from "@/types/teamMember";
import type { TDashboardService } from "@/types/api/TDashboardService";

const {
  membersDashboardConfig,
  currentMember,
  setModalsState,
  modalsState,
  setConfig,
  setCurrentMember,
} = useMembersState();

const { t } = useI18n();
const router = useRouter();

const { changeAccountHook } = useEventHooks();

const sort = ref<IUITableSortModel>({
  key: "id",
  value: "asc",
});

const PER_PAGE_DEFAULT = 12;

const paginationPerPage = ref<number>(PER_PAGE_DEFAULT);

const {
  data: members,
  isFetching,
  error: isError,
  execute: updateMembers,
} = await useBusinessDashboardGet(membersDashboardConfig, { refetch: true });

const hiddenLoading = ref<boolean>(false);
const completedLoading = computed<boolean>(() => {
  return !hiddenLoading.value && isFetching.value;
});

const perPageOptions = computed(() => {
  return [
    {
      label: `${t("cards.pagination.perPage.by")} ${PER_PAGE_DEFAULT}`,
      value: PER_PAGE_DEFAULT,
    },
    {
      label: `${t("cards.pagination.perPage.by")} 24`,
      value: 24,
    },
    {
      label: `${t("cards.pagination.perPage.by")} 48`,
      value: 48,
    },
  ];
});

const setPageHandler = (page: number) => {
  setConfig({
    page: page,
    per_page: paginationPerPage.value,
  });
};

const paginationFrom = computed<number>(() => {
  const currentPage = Number(members.value?.meta?.current_page || "0");
  return 1 + (currentPage - 1) * paginationPerPage.value;
});

const paginationTo = computed<number>(() => {
  const currentPage = Number(members.value?.meta?.current_page || "0");
  const countItems = Number(members.value?.data?.length || "0");
  return (currentPage - 1) * paginationPerPage.value + countItems;
});

const paginationTotal = computed<number>(() => {
  const total = Number(members.value?.meta?.total || "0");
  return total;
});

const columns = computed<TUITableColumn[]>(() => {
  return [
    {
      label: t("membersV2.table.header.user"),
      align: "left",
    },
    {
      label: t("membersV2.table.header.status"),
      align: "left",
    },
    {
      label: t("membersV2.table.header.accounts"),
      align: "left",
      sortKey: MEMBER_DASHBOARD_SORT.BY_BALANCE_ACCOUNTS,
    },
    {
      label: t("membersV2.table.header.cards"),
      align: "left",
      sortKey: MEMBER_DASHBOARD_SORT.BY_BALANCE_CARDS,
    },
    {
      label: t("membersV2.table.header.cardsCount"),
      align: "left",
    },
    {
      label: t("membersV2.table.header.paymentsByDay"),
      align: "left",
      sortKey: MEMBER_DASHBOARD_SORT.BY_PAYMENTS_DAY,
    },
    {
      label: "",
      align: "right",
    },
  ];
});

watch(paginationPerPage, () => {
  setPageHandler(1);
});

watch(
  sort,
  (newVal) => {
    const { key, value } = newVal;
    if (!value) {
      setConfig({
        sort: "id" as TMemberDashboardSort,
        direction: "asc",
      });
    } else {
      setConfig({
        sort: key as TMemberDashboardSort,
        direction: value as "desc" | "asc" | undefined,
      });
    }
  },
  { deep: true }
);

changeAccountHook.on(async () => {
  hiddenLoading.value = true;
  await updateMembers();
  hiddenLoading.value = false;
});

const onMemberClick = (member: TDashboardService) => {
  if (!member.isMaster) {
    setCurrentMember(member);
    router.push({
      name: RouteName.TEAM_MEMBER_BY_ID,
      params: { memberId: member.id },
    });
  }
};
</script>

<template>
  <div>
    <div v-if="!isError">
      <MembersTableFilter />

      <UITableV2
        v-model:sort="sort"
        grid-class="grid-cols-[3fr_80px_2fr_2fr_80px_150px_120px]"
        :is-loading="completedLoading"
        :columns="columns"
        :items="members?.data ?? []"
        class="members-table"
        @click-table-item="onMemberClick">
        <template #item="{ item }">
          <MembersTableItem :member="item as TTeamMemberDashboard" />
        </template>
        <template
          v-if="Number(members?.meta?.total || '0') > 0"
          #footer>
          <div
            class="flex items-center justify-between md:flex-row flex-col gap-2">
            <div class="flex gap-2">
              <div
                v-if="completedLoading"
                class="w-[7rem] flex">
                <Skeletor class="w-full rounded" />
              </div>
              <div
                v-else
                class="text-4 flex items-center">
                {{ paginationFrom }} - {{ paginationTo }}
                {{ $t("from") }}
                {{ paginationTotal }}
              </div>

              <UISelect
                v-model="paginationPerPage"
                :cleared="false"
                placeholder=""
                class="w-[5.5rem]"
                :options="perPageOptions" />
            </div>

            <UIPagination
              v-if="
                Number(members?.meta?.total || '0') >
                Number(members?.meta?.per_page || '0')
              "
              :loading="completedLoading"
              :count="members?.meta?.last_page!"
              :page="members?.meta?.current_page!"
              @change="setPageHandler" />
          </div>
        </template>
      </UITableV2>

      <UiNotFound
        v-show="members?.meta?.total === 0"
        :text="$t('membersTableNotFound')" />
    </div>

    <UiNotFound
      v-else
      :text="$t(`Server error`)"
      type="critical" />

    <!--  Modals -->
    <Teleport to="#modals">
      <MembersModalRename
        v-if="modalsState.renameMemberModal"
        :member="currentMember"
        @close="setModalsState('renameMemberModal', false)" />
      <MembersDeleteModal />
    </Teleport>
  </div>
</template>

<style lang="scss">
.members-table {
  .ui-table__item:hover {
    @apply bg-bg-none-hover;
  }
}
</style>
