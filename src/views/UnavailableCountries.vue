<script setup lang="ts">
import UiTransition from "@/components/ui/UITransition.vue";
import { ref } from "vue";
import { useUserStore } from "@/stores/user";
import BlockedCountriesModal from "@/components/Auth/BlockedCountriesModal.vue";
import { useRouter } from "vue-router";
import { useCountries } from "@/composable/useCountries";
import { TrackerService } from "@/helpers/tracker/tracker.service";
import { ERemoteStorageKeys } from "@modules/services/remoteStorage";
import { RouteName } from "@/constants/route_name";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useSessionStorage } from "@vueuse/core";
import { SessionStorageKey } from "@/constants/session_storage_key";
import SupportTrigger from "@/components/SupportTrigger.vue";

const { countryIsAvailable } = useCountries();
const { getUserCountryCode } = useUserStore();
const router = useRouter();
const wasGeoBlockShown = useSessionStorage(
  SessionStorageKey.WAS_GEO_BLOCK_SHOWN,
  false
);

const isShowModal = ref<boolean>(false);
const isLoaded = ref<boolean>(false);
const openFullListUnavailableCountriesModal = () => {
  isShowModal.value = true;
};
const countryCode = ref("");
const checkCountryCode = async () => {
  if (wasGeoBlockShown.value) {
    await router.replace({ name: RouteName.DASHBOARD });
    return;
  }
  countryCode.value = await getUserCountryCode();
  if (!countryCode.value) return;
  if (countryIsAvailable(countryCode.value)) {
    await router.replace({ name: RouteName.DASHBOARD });
  } else {
    await TrackerService.logEvent(ERemoteStorageKeys.SHOULD_GEO_BLOCK, {});
    isLoaded.value = true;
  }
};

const clickContinue = async () => {
  wasGeoBlockShown.value = true;
  await router.push({ name: RouteName.DASHBOARD });
};

checkCountryCode();
</script>
<template>
  <template v-if="isShowModal">
    <UiTransition :name="'fade'">
      <BlockedCountriesModal
        @close="
          () => {
            isShowModal = false;
          }
        " />
    </UiTransition>
  </template>

  <template v-if="isLoaded">
    <UiTransition
      name="fade-slide-up"
      appear>
      <div class="wrapper flex flex-col flex-auto m-auto w-full">
        <div class="flex flex-auto"></div>
        <div
          class="flex flex-none flex-col items-center text-center font-hauss w-full max-w-[27.5rem] m-auto gap-7 p-2">
          <div class="flex flex-none">
            <img
              :src="'/img/geoblock/map.png'"
              alt="map" />
          </div>
          <div class="flex flex-none w-full px-10">
            <span
              class="text-fg-primary text-center text-6 not-italic font-semibold leading-7">
              {{
                $t("unavailable-country.title", {
                  c: $t(`unavailable-country-list.${countryCode}`),
                })
              }}
            </span>
          </div>
          <div class="flex flex-none w-full">
            {{
              $t("unavailable-country.text", {
                c: $t(`unavailable-country-list.${countryCode}`),
              })
            }}
          </div>
          <div class="flex flex-none w-full">
            <UIButton
              color="black"
              class="w-full"
              @click="clickContinue()">
              <span
                class="text-fg-contrast text-center text-base not-italic font-medium leading-5">
                {{ $t("unavailable-country.button.submit") }}
              </span>
            </UIButton>
          </div>
          <div class="flex flex-none">
            <span
              class="text-4 text-fg-secondary leading-5 text-center cursor-pointer"
              @click="openFullListUnavailableCountriesModal()">
              {{ $t("unavailable-country.button.list") }}
            </span>
          </div>
        </div>
        <div class="flex flex-auto"></div>
      </div>
    </UiTransition>

    <div class="support-wrapper">
      <SupportTrigger color="blue-light" />
    </div>
  </template>
</template>
<style lang="scss" scoped>
.support-wrapper {
  @apply fixed right-4 top-5 z-2;
}

.wrapper {
  @apply h-screen;
}
</style>
