<script lang="ts" setup>
import { IsoCodeNames } from "@/constants/iso_code_names";
// components
import CardIssueSummary from "@/components/CardIssueSummary/CardIssueSummary.vue";
import UICurrencyInput from "@/components/ui/UICurrencyInput/UICurrencyInput.vue";
import SelectAccount from "@/components/ui/Select/SelectAccount.deprecated.vue";
import { computed, onMounted, ref, toRef } from "vue";
import { useUserAccounts } from "@/composable/useUserAccounts";
import { useI18n } from "vue-i18n";
import { UserService } from "@modules/services/user";
import type {
  TTransactionFromDirection,
  TTransferState,
} from "@/components/Transaction/Latest/types";
import { useUserExchangeRates } from "@/composable/User/ExchangeRates";
import { ExceptionCause, ExceptionReason } from "@/config/Exception";
import Warning from "@/components/ui/Warning/Warning.vue";
import { useUserStore } from "@/stores/user";
import type { TCardTariffSlug } from "@/composable/Tariff";
import PromoCodeWidget from "@/components/Widgets/PromoCode/Index.vue";
import type { IPromoCodeModel } from "@modules/services/promocode";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import type { TCardTariff } from "@/types/user/user.types";
import UiLoader from "@/components/ui/Loader/Loader.vue";
import SelectUltimaTariff from "@/components/Cards/StandardCreateCard/Stages/Issue/SelectUltimaTariff.vue";
import PaymentSystem from "@/components/Cards/UltimaCreateCard/PaymentSystem.vue";
import CheckboxSecurity from "@/components/Cards/UltimaCreateCard/CheckboxSecurity.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useAutoAnimate } from "@formkit/auto-animate/vue";
import cards from "@/config/cards";
import { useUserIsWarn } from "@/composable/useUserIsWarn";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { loggerConsole } from "@/helpers/logger/Logger";
import { prepareAccountBalance } from "@/helpers";
import { getCurrencySymbolByIso } from "@/composable";
import { calcTopUpFee } from "@/helpers/calcTopUpFee";
import VisaSecurityNotAvailableNotice from "@/components/VisaSecurityNotAvailableNotice.vue";
import TosAgreementCheckbox from "@/components/TosForCardIssue/TosAgreementCheckbox.vue";
// import VerificationModalPreScaleStep from "@/components/VerificationModalPreScaleStep.vue";

defineOptions({ name: "StageIssue" });

const { userIsWarnEffect } = useUserIsWarn();
const minStartBalance = computed(() =>
  userIsWarnEffect.value ? cards.minCardBalanceUserIsWarn : 1
);
const startBalanceDefault = computed(() =>
  userIsWarnEffect.value ? cards.minCardBalanceUserIsWarn : cards.minCardBalance
);
const { t } = useI18n();
interface IPromoCode {
  value?: string;
  discount?: string;
  active?: boolean;
}

interface Props {
  slug: string;
  stateForm: any;
}

const props = defineProps<Props>();

const isSecure = ref<boolean>(props.slug.includes("-3ds"));
const updateIsSecure = (secure: boolean) => {
  isSecure.value = secure;

  const prefix: string = "3ds";

  let newSlug = "";
  if (secure) {
    newSlug = `${props.slug}-${prefix}`;
  } else {
    newSlug = (props.slug as string).replace(`-${prefix}`, "");
  }

  updateCurrentCardSlug(newSlug as TCardTariffSlug);
};
const selectPaymentSystem = (system: number) => {
  if (cardForIssue.value.system === system) return;
  cardForIssue.value.system = system;
  updateIsSecure(false);
};

const { subscriptionsStatus, cardsToRelease } = useSubscriptionsInfo();

const [promocodeWidgetContainer] = useAutoAnimate();

const updateCurrentCardSlug = (slug: TCardTariffSlug) => {
  if (!["ultima", "ultima-3ds"].includes(slug)) {
    resetPromoCode();
  }
  emit("updateCurrentCardSlug", slug);
};

const promoCodeDataState = toRef<any>(() => props.stateForm?.promoCodeData);
const promoCodeData = ref<any>({
  data: promoCodeDataState.value?.data ?? undefined,
  list: promoCodeDataState.value?.list ?? [],
  code: promoCodeDataState.value?.code ?? "",
});

const percentDiscountCostCardPromoCode = computed(() => {
  if (!promoCodeData.value.code) return 0;
  const data = promoCodeData?.value.data;
  if (data?.fields) {
    return Number(data.fields?.card_buy_discount_percent || 0);
  } else {
    return 0;
  }
});

const errorToIssueState: any = toRef<any>(() => props.stateForm?.errorToIssue);
const errorToIssue = ref<string>(errorToIssueState.value ?? "");
const cardForIssueState: any = toRef<any>(() => props.stateForm?.cardForIssue);
const cardForIssue = ref<any>({
  system: cardForIssueState.value?.system ?? 5,
  type: cardForIssueState.value?.type ?? "",
  start_balance:
    cardForIssueState.value?.start_balance ?? startBalanceDefault.value,
  minValue: cardForIssueState.value?.minValue ?? minStartBalance.value,
  description: cardForIssueState.value?.description ?? "",
  account: cardForIssueState.value?.account ?? "",
  address: cardForIssueState.value?.address ?? "",
  id: cardForIssueState.value?.id ?? "",
  count: cardForIssueState.value?.count ?? 1,
});

const autoRefillFormState: any = toRef<any>(
  () => props.stateForm?.autoRefillForm
);
const autoRefillForm = ref<any>({
  minimumBalance: autoRefillFormState.value?.minimumBalance ?? "50",
  amountRefill: autoRefillFormState.value?.amountRefill ?? "50",
  isActive: autoRefillFormState.value?.isActive ?? true,
});

const accountIdState: any = toRef<any>(() => props.stateForm?.accountId);
const tState = ref<TTransferState>({
  type: "",
  step: 1,
  toId: -1,
  toDirection: "",
  toAmount: "0",
  fromId: accountIdState.value ?? -1,
  fromDirection: "account",
  fromAmount: "0",
  isSubmit: false,
  isConfirm: false,
  error: {},
});

const emit = defineEmits<{
  issueHandler: [cardForIssue: any];
  updateCurrentCardSlug: [slug: TCardTariffSlug];
}>();

const { accounts } = useUserAccounts();
const agreement = ref<any>({
  checked: false,
  valid: true,
});

const errorNotEnoughMoney = computed<string>(() => {
  if (accountBalanceUsd.value === undefined) return "";
  if (totalPaymentUsd.value === undefined) return "";
  return accountBalanceUsd.value < totalPaymentUsd.value
    ? t("cards.create.error.not_enough_money")
    : "";
});

/* Calculate Total Payment begin */
const accountBalanceUsd = computed<number | undefined>(() => {
  return convertToCurrency(
    Number(cardForIssue.value?.account?.balance),
    cardForIssue.value?.account?._currency?.iso_code,
    "USD"
  );
});
const convertToCurrency = (
  amount: number,
  sourceIsoCode: string,
  targetIsoCode: string
): number | undefined => {
  if (!amount || !sourceIsoCode || !targetIsoCode) return 0;
  if (sourceIsoCode === targetIsoCode) return amount;
  if (!rates.value) return 0;
  //@ts-ignore
  return amount * parseFloat(rates.value[sourceIsoCode][targetIsoCode]);
};

const totalPaymentUsd = computed<number | undefined>(() => {
  const startBalance = Number(cardForIssue.value?.start_balance ?? "0");
  const feeTopUpPercent = Number(selectedTariff.value?.fee_topup ?? "0");
  const feeTopUp = calcTopUpFee(startBalance, feeTopUpPercent, 1);
  return getCardPrice() + startBalance + feeTopUp;
});

/* Calculate Total Payment end */

const setCardBalance = (value: number): void => {
  cardForIssue.value.start_balance = String(value);
  errorToIssue.value = "";
};

const onCheckboxChange = (value: boolean) => {
  agreement.value.checked = value;
  agreement.value.valid = true;
};

const getCardCode = () => props.slug;

const cardMaxCount = computed<number>(() => {
  let maxCardsEnough: number = 0;
  if (subscriptionsStatus.value) {
    maxCardsEnough = cardsToRelease.value;
  } else {
    maxCardsEnough = ["fb-prem", "adv", "platinum"].includes(getCardCode())
      ? 25
      : 10;
  }
  return maxCardsEnough || 0;
});

const getCardPrice = (): number => {
  let cardPrice = Number(selectedTariff.value?.card_price) || 0;
  if (!cardPrice) return 0;
  return calcDiscount(cardPrice, percentDiscountCostCardPromoCode.value);
};

const calcDiscount = (price: number, discountPercent: number) => {
  return price - (price * discountPercent) / 100;
};

const userTariff = ref<TCardTariff[]>([]);
const userSpecial = ref<any>(null);
const logger = loggerConsole();

// systems
const userStore = useUserStore();
const { userActualLimit } = userStore;

// data
const loading = ref<boolean>(true);

const { data: rates } = useUserExchangeRates();

const changePromoCodeHandler = (
  code: string,
  data: IPromoCodeModel,
  list: IPromoCode[]
) => {
  promoCodeData.value.code = code;
  promoCodeData.value.data = data;
  promoCodeData.value.list = list;
};

const resetPromoCode = () => {
  promoCodeData.value.data = undefined;
  promoCodeData.value.list = [];
  promoCodeData.value.code = "";
};

const loadUserTariff = async () => {
  const response = await UserService.tariff();
  const special = await UserService.special();

  if (!response.status) {
    useCallToast({
      title: t("toast.error"),
      body: response.message || t("toast.error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });

    logger.error(ExceptionCause.USER_TARIFF, ExceptionReason.FAILED_LOAD);
  }

  userSpecial.value = special?.data;
  userTariff.value = response?.data || [];
};

const initDefaultAccount = () => {
  if (tState.value.fromId !== -1) return;
  if (!accounts.value.length) return;
  for (let account of accounts.value) {
    if (account._currency?.iso_code === "USD") {
      setFromDirection(account.id, "account");
      return;
    }
  }
  setFromDirection(accounts.value[0].id, "account");
};

const setFromDirection = (
  id: number,
  fromDirection: TTransactionFromDirection
): void => {
  tState.value.fromId = id;
  tState.value.fromDirection = fromDirection;
  for (let account of accounts.value) {
    if (account.id === id) {
      cardForIssue.value.account = account;
      break;
    }
  }
};

initDefaultAccount();

const selectedTariff = computed<any>(() =>
  userTariff.value.find((tariff: TCardTariff) => tariff.slug === props.slug)
);

const defaultSelectedTariff = computed(() => {
  const slug = isSecure.value ? props.slug.replace("-3ds", "") : props.slug;
  return userTariff.value.find((tariff: TCardTariff) => tariff.slug === slug);
});

//CardIssueSummary data

const exchangeRates = computed(() => {
  const isoCode = cardAccountCurrencyIsoCode.value;
  if (!isoCode) return 1;
  if (isoCode === IsoCodeNames.USD) return 1;
  if (!rates.value || !rates.value[isoCode]) return 1;
  return rates.value[isoCode][IsoCodeNames.USD];
});

const exchangeRate = computed<string | undefined>(() => {
  if (exchangeRates.value === undefined) return;
  return parseFloat(exchangeRates.value).toFixed(2) || "0";
});

const ultimaTariffName = computed(() => {
  const names: { [key: string]: string } = {
    "ultima-weekly": t("cards.plan-weekly"),
    "ultima-annually": t("cards.plan-annually"),
    ultima: t("cards.plan-monthly"),
  };
  const tariffSlug =
    defaultSelectedTariff.value?.slug.replace("-3ds", "") || "";
  const plan = t("cards.plan");
  return `${plan} ${names[tariffSlug]}`;
});

const tariffMonthlyPayment = computed(() => {
  return parseFloat(defaultSelectedTariff.value?.card_price || "0").toFixed(2);
});

const tariffMonthlyPaymentWithDiscount = computed(() => {
  if (!percentDiscountCostCardPromoCode.value) return;
  const price = getCardPrice();
  return parseFloat(String(price)).toFixed(2);
});

const cardStartBalance = computed(() => {
  return parseFloat(cardForIssue.value?.start_balance).toFixed(2) || "0.00";
});

const selectedTariffFeeTopUp = computed(() => {
  const feeTopUpPercent = Number(selectedTariff.value?.fee_topup ?? "0");
  return feeTopUpPercent > 0 ? feeTopUpPercent.toFixed(1) : null;
});

const cardAccountCurrencyIsoCode = computed(
  () => cardForIssue.value.account?._currency?.iso_code
);

const cardTotal = computed(() => {
  let total = totalPaymentUsd.value;
  const isoCode = cardAccountCurrencyIsoCode.value;
  const actualRates = rates.value;

  if (!total || !isoCode || !actualRates) return "";
  if (isoCode !== "USD") {
    total = total / actualRates[isoCode]["USD"];
  }
  return `${prepareAccountBalance(total, isoCode)} ${getCurrencySymbolByIso(
    isoCode
  )}`;
});

const securityAdditionalCost = computed(() => {
  if (!isSecure.value) return;
  return (
    parseFloat(selectedTariff.value?.card_price || "0") -
    parseFloat(defaultSelectedTariff.value?.card_price || "0")
  ).toFixed(2);
});

onMounted(async () => {
  await loadUserTariff();

  loading.value = false;

  const modalOverlayEl = document.querySelector(".modal_overlay");
  if (modalOverlayEl) modalOverlayEl.scrollTo({ top: 0 });
});

const sendForm = async () => {
  // validation
  if (
    accountBalanceUsd.value !== undefined &&
    totalPaymentUsd.value !== undefined
  ) {
    if (accountBalanceUsd.value < totalPaymentUsd.value) {
      errorToIssue.value = t("cards.create.error.not_enough_money");
      loading.value = false;
      return;
    }
  }
  if (Number(cardForIssue.value.start_balance) < 1) {
    errorToIssue.value = t(
      "cards.create.error.desired_balance_have_to_be_least",
      { s: "1 $" }
    );
    loading.value = false;
    return;
  }
  if (
    Number(cardForIssue.value.start_balance) <
    Number(cardForIssue.value.minValue)
  ) {
    errorToIssue.value = t(
      "cards.create.error.desired_balance_have_to_be_least",
      { s: `${cardForIssue.value.minValue} $` }
    );
    loading.value = false;
    return;
  }
  if (
    Number(cardForIssue.value.count) < 1 ||
    Number(cardForIssue.value.count) > cardMaxCount.value
  ) {
    errorToIssue.value = t("cards.create.countError");
    loading.value = false;
    return;
  }
  if (agreement.value.checked === false) {
    agreement.value.valid = false;
    loading.value = false;
    return;
  }

  cardForIssue.value.type = props.slug;

  const result = {
    cardForIssue: cardForIssue.value,
    autoRefillForm: autoRefillForm.value,
    accountId: tState.value.fromId,
    promoCodeData: promoCodeData.value,
  };

  emit("issueHandler", result);
};
</script>

<template>
  <div>
    <div class="flex flex-col items-center gap-6 root">
      <UiLoader v-if="loading" />

      <template v-if="!loading">
        <!-- Title -->
        <div class="flex w-full flex-col">
          <div
            class="flex w-full text-4.5 leading-5 text-fg-primary font-medium mb-2.5">
            {{ $t("cards.section-plans-title") }}
          </div>

          <!-- Ultima Tariffs -->
          <SelectUltimaTariff
            :selected-tariff="selectedTariff"
            :user-tariffs="userTariff"
            :is-secure="isSecure"
            @update-current-card-slug="updateCurrentCardSlug" />
        </div>
        <!-- Select Payment System -->
        <PaymentSystem
          :system="cardForIssue.system"
          @select-system="selectPaymentSystem" />

        <!-- Select balance -->
        <div class="flex flex-col w-full">
          <div
            class="flex w-full text-4.5 leading-5 text-fg-primary font-medium mb-2.5">
            {{ $t("cards.section-start-balance-title") }}
          </div>
          <div class="flex flex-col">
            <div class="flex w-full">
              <UICurrencyInput
                v-model="cardForIssue.start_balance"
                size="m"
                class="w-full"
                :currency="'USD'"
                :min="minStartBalance" />
            </div>

            <div
              v-if="!userIsWarnEffect"
              class="grid grid-cols-4 gap-2 mt-2">
              <UIButton
                v-for="item of [50, 100, 250, 500]"
                :key="item"
                size="s"
                color="grey-solid"
                @click="setCardBalance(item)">
                <span class="text-4 leading-8 font-medium"> {{ item }} $ </span>
              </UIButton>
            </div>
          </div>
        </div>

        <!-- 3D Secure-->
        <CheckboxSecurity
          v-if="cardForIssue.system === 5"
          :is-secure="isSecure"
          @update="updateIsSecure" />
        <VisaSecurityNotAvailableNotice v-else />

        <!-- Select Account -->
        <div class="flex flex-none flex-col w-full">
          <div
            class="flex w-full text-4.5 leading-5 text-fg-primary font-medium mb-2.5">
            {{ $t("cards.section-payment-method-title") }}
          </div>
          <SelectAccount
            :with-icon="false"
            :value="tState.fromId"
            :options="accounts"
            :rates="rates"
            :error="errorNotEnoughMoney"
            backdrop
            searchable
            @change="(v) => setFromDirection(v, 'account')" />
        </div>

        <!-- Total -->
        <CardIssueSummary
          :total="cardTotal"
          :tariff-fee-top-up="selectedTariffFeeTopUp"
          :tariff-name="ultimaTariffName"
          :tariff-monthly-payment="tariffMonthlyPayment"
          :tariff-monthly-payment-with-discount="
            tariffMonthlyPaymentWithDiscount
          "
          :starting-balance-on-card="cardStartBalance"
          :account-iso-code="cardAccountCurrencyIsoCode"
          :security-additional-cost="securityAdditionalCost"
          :is-loading="loading"
          :exchange-rate="exchangeRate" />

        <!-- PromoCode-->
        <div
          ref="promocodeWidgetContainer"
          class="w-full">
          <div
            v-if="['ultima', 'ultima-3ds'].includes(selectedTariff?.slug)"
            class="w-full">
            <PromoCodeWidget
              :list="promoCodeData.list"
              :code="promoCodeData.code"
              :data="promoCodeData.data"
              @change-promo-code="changePromoCodeHandler" />
          </div>
        </div>

        <!-- Errors -->
        <div class="flex flex-none flex-col w-full mt-5">
          <!--   Error    -->
          <Warning
            background="yellow"
            class="col-span-2">
            <template #title>
              {{ $t("cards.nfc-compatibility-title") }}
            </template>
            <template #text>
              {{ $t("cards.nfc-compatibility-text") }}
            </template>
          </Warning>
          <Warning
            v-if="
              errorToIssue && errorToIssue === 'KYC limit' && !!userActualLimit
            "
            data-cy="errorToIssue"
            :error="errorToIssue"
            icon="danger"
            :data="{
              kyc_actual: userActualLimit.userLimit.slug,
              kyc_deposit_limit: userActualLimit.userLimit.card_deposit_limit,
            }"
            class="col-span-2 mt-4" />
        </div>

        <span
          v-if="errorToIssue && errorToIssue !== 'KYC limit'"
          data-cy="errorToIssue"
          class="col-span-2 text-sm text-error-light">
          {{ errorToIssue }}
        </span>

        <div
          class="font-normal text-4 text-fg-primary flex items-start gap-6 test">
          <TosAgreementCheckbox
            :checked="agreement.checked"
            @change="onCheckboxChange" />
        </div>

        <UIButton
          size="m"
          color="black"
          class="w-full"
          :disabled="loading || !agreement.checked"
          data-cy="order_button"
          @click="sendForm">
          {{ $t("cards.create.continue_btn") }}
        </UIButton>
      </template>
    </div>

    <!-- <VerificationModalPreScaleStep /> -->
  </div>
</template>

<style lang="scss" scoped>
.root {
  @apply max-w-[27.5rem] mx-auto px-3;
}

.agreement {
  @apply border-secondary-base;
  animation: pulse 2s infinite;
}

.link-style {
  @apply text-fg-blue border-b border-solid border-fg-blue;
}

.pay-system-wrapper {
  @apply py-1 px-1.5 rounded;
  background: var(
    --sys-color-chart-cell-card-bg-number,
    rgba(21, 25, 29, 0.06)
  );
}
</style>
