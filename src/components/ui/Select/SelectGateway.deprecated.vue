<script lang="ts" setup>
import Select from "@/components/ui/Select/SelectRefactor.deprecated.vue";
import type { TSelectGateway } from "./types";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import Skeleton from "@/components/ui/Skeleton/Skeleton.vue";

const props = defineProps<TSelectGateway>();
const emit = defineEmits(["change", "is-intersecting"]);

const onChange = (id: number, direction: "externalGateway" | "account") => {
  emit("change", id, direction);
};
</script>

<template>
  <Select
    :options="
      props.options.reduce((acc, option) => {
        acc.push({
          ...option,
          groupField: String(option.type),
        });
        return acc;
      }, [])
    "
    :value="props.value"
    :label="props.label"
    :label-type="props.labelType"
    :height="props.height || props.labelType === 'inside' ? 80 : ''"
    :placeholder="props.placeholder"
    :disabled="props.disabled"
    :error="props.error"
    :loading="props.loading"
    :backdrop="props.backdrop"
    :searchable="props.searchable"
    fullscreen
    :group-active="props.groupActive"
    group
    :search-reduce="
      props.options.reduce((acc, option) => {
        acc.push({
          ...option,
          groupField: String(option.type),
          searchField:
            option.title + option.icon + $t(`label.${option.type}Group`),
        });
        return acc;
      }, [])
    "
    :with-intersecting="props.withIntersecting"
    @is-intersecting="emit('is-intersecting')"
    @change="(id, direction) => onChange(id, direction)">
    <template #withIcon="{ option }">
      <div :class="[selectGateway.root]">
        <DynamicIcon
          v-if="option.icon"
          path="gateway"
          :class="{ 'rounded-full': option?.groupField === 'crypto' }"
          :name="option.icon.toLowerCase()" />
        <div :class="selectGateway.title">
          {{ option.title }}
        </div>
      </div>
    </template>
    <template #selectLoader>
      <div :class="selectAccount.root">
        <span :class="[selectAccount.icon]">
          <Skeleton :options="{ width: 32, height: 32 }">
            <template #loader>
              <rect
                x="0"
                y="0"
                rx="4"
                ry="4"
                width="32"
                height="32" />
            </template>
          </Skeleton>
        </span>
        <div :class="selectAccount.title">
          <Skeleton :options="{ width: 90, height: 18, uniqueKey: '********' }">
            <template #loader>
              <rect
                x="0"
                y="0"
                rx="4"
                ry="4"
                width="90"
                height="18" />
            </template>
          </Skeleton>
        </div>
        <div :class="selectAccount.amount">
          <Skeleton :options="{ width: 90, height: 18, uniqueKey: '21213' }">
            <template #loader>
              <rect
                x="0"
                y="0"
                rx="4"
                ry="4"
                width="90"
                height="18" />
            </template>
          </Skeleton>
        </div>
      </div>
    </template>
  </Select>
</template>

<style lang="scss" module="selectGateway">
.root {
  @apply cursor-pointer flex items-center gap-3 w-full;

  & svg {
    @apply w-8 h-8 min-w-[32px];
  }
}

.title {
  @apply text-base font-medium;
}
</style>

<style lang="scss" module="selectAccount">
.root {
  @apply flex items-center gap-3 w-full;
}

.icon {
  @apply flex items-center justify-center w-8 h-8;
  & svg {
    @apply w-full h-full;
  }
}

.title {
  @apply text-base font-medium;
}

.amount {
  @apply flex flex-col ml-auto text-right;
}
</style>
