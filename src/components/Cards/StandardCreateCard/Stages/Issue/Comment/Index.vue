<script lang="ts" setup>
import InputBase from "@/components/ui/Input/InputBase.vue";

const emit = defineEmits(["update", "input"]);
const onChange = (event: Event & { target: HTMLInputElement }) => {
  emit("update", event.target?.value || "");
};
const onInput = (event: Event & { target: HTMLInputElement }) => {
  emit("input", event.target?.value || "");
};
</script>

<template>
  <!-- components/Cards/StandardCreateCard/Stages/Issue/Comment/UltimaCreateCard.vue  -->
  <InputBase
    v-bind="$attrs"
    @change="onChange"
    @input="onInput">
    <template
      v-if="$slots.leftIcon"
      #leftIcon>
      <slot name="leftIcon" />
    </template>

    <template
      v-if="$slots.rightIcon"
      #rightIcon>
      <slot name="rightIcon" />
    </template>
  </InputBase>
</template>
