<script setup lang="ts">
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { onMounted } from "vue";
import UISearchInput from "@/components/ui/UISearchInput/UISearchInput.vue";

interface Props {
  search: string;
}
const props = defineProps<Props>();
const emit = defineEmits<{ input: [value: string] }>();
const route = useRoute();
const { t } = useI18n();

onMounted(() => {
  if (route.query.search) {
    emit("input", route.query.search as string);
  }
});
</script>
<template>
  <div :class="faqSearch.root">
    <h4>{{ $t("How can we help") }}?</h4>
    <UISearchInput
      size="m"
      :placeholder="t('support.enterQuestion')"
      :model-value="props.search"
      @update:model-value="emit('input', $event as string)" />
  </div>
</template>

<style lang="scss" module="faqSearch">
.root {
  @apply flex flex-col gap-3;
  & h4 {
    @apply text-4.5 font-medium leading-6;
  }
}
</style>
