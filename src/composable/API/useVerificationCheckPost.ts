//POST verification/check

import { usePstFetch } from "@/composable/usePstFetch";
import type { UseFetchOptions } from "@vueuse/core";

export type TVerificationCheckPostReq = {
  first_name: string;
  last_name: string;
  birthday: string;
  country_id?: number;
  is_not_located_in_country: boolean;
};

export type VerificationCheckPostRes = {
  success: boolean;
};

export const useVerificationCheckPost = (
  body: TVerificationCheckPostReq,
  options: UseFetchOptions = {}
) => {
  return usePstFetch("verification/check", options)
    .post(body)
    .json<VerificationCheckPostRes>();
};
