<script lang="ts" setup>
import BlockedCountriesModal from "@/components/Auth/BlockedCountriesModal.vue";
import { useState } from "@/helpers/utilities";

const [showCountries, setShowCountries] = useState<boolean>(false);
</script>

<template>
  <div :class="$style.checkboxLabel">
    {{ $t("register.agreementCountry.label_1") }}

    <span @click.prevent.stop="() => setShowCountries(true)">
      {{ $t("register.agreementCountry.label_2") }}
    </span>
  </div>

  <BlockedCountriesModal
    v-if="showCountries"
    @close="() => setShowCountries(false)" />
</template>

<style lang="scss" module>
.checkboxLabel {
  @apply text-neutral-700 text-base font-normal;

  & a,
  & span {
    @apply text-greyscale-900 cursor-pointer font-semibold;
  }
}
</style>
