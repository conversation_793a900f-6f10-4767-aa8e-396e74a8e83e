import { computed, unref, type MaybeRef } from "vue";
import { useI18nGlobal } from "./useI18nGlobal";

interface Props {
  status: number | undefined;
  isDeleted: boolean;
  typeEnum?: string | null;
  offTranslate?: boolean;
  isProMode?: boolean | null;
}

/**
 * Get status for transactions.
 *
 * @param {Props} params - transaction status params
 *
 * @example
 * const c = useTransactionStatus({
 *     status,
 *     isDeleted: <PERSON><PERSON><PERSON>(deleted_at),
 *     offTranslate: true,
 *   });
 *
 * @category Composables
 */
export function useTransactionStatus(params: MaybeRef<Props>) {
  const { t } = useI18nGlobal();

  const props = unref(params);

  return computed(() => {
    // declined
    if (props.status === 35) {
      return props?.offTranslate
        ? "Declined"
        : t("team.transactions.item.status.rejected");
    }

    // refund
    if (props.isDeleted) {
      if (props.typeEnum === "Authorization") {
        return "Canceled";
      }

      return props?.offTranslate
        ? "Refund"
        : t("team.transactions.item.status.refund");
    }
    if (props.typeEnum === "Refund" || props.typeEnum === "Reversal") {
      return props?.offTranslate
        ? "Refund"
        : t("team.transactions.item.status.refund");
    }

    // THIS HOLD STATUS
    if (props.typeEnum === "Authorization" && props.status === 30) {
      if (props?.isProMode) {
        return props?.offTranslate ? "Hold" : t("transaction.status.hold");
      } else {
        return props?.offTranslate
          ? "Success"
          : t("transaction.status.success");
      }
    }

    // success with timer
    if (props.status === 5) {
      return props?.offTranslate
        ? "Success"
        : t("team.transactions.item.status.success");
    }

    // success
    if (props.status && props.status >= 20 && props.status <= 30) {
      return props?.offTranslate
        ? "Success"
        : t("team.transactions.item.status.success");
    }

    return "???";
  });
}
