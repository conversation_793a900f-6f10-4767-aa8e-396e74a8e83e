import type { UIDialogProps } from "@/components/ui/UIDialog/types";
import { RouteName } from "@/constants/route_name";
import { useRouter } from "vue-router";
import { useGlobalUniversalDialog } from "@/components/GlobalUniversalDialog/useGlobalUniversalDialog";
import { useVerificationActualGet } from "@/composable/API";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/user";

/**
 * Custom hook to manage KYC limit dialog operations.
 *
 * @example
 * const { validateKycLimit, openKycLimitDialog } = useKycLimitDialog();
 *
 * @category Composables
 */
export function useKycLimitDialog() {
  const router = useRouter();
  const userStore = useUserStore();
  const { t } = useI18n();
  const { closeDialog, openDialog } = useGlobalUniversalDialog();
  const {
    execute: updateVerificationActual,
    data: verificationActualData,
    isFetching: isFetchingVerificationActual,
  } = useVerificationActualGet({
    immediate: false,
  });

  /**
   * Opens a KYC limit dialog based on the specified variant.
   *
   * @param {"deposit" | "cards"} variant - The type of KYC limit dialog to open.
   * @example
   * openKycLimitDialog("deposit");
   */
  const openKycLimitDialog = (variant: "deposit" | "cards") => {
    const options: UIDialogProps = {
      isOpen: false,
      btnCancelText: t("Close"),
      btnConfirmText: t("common.kyc-limit-dialog.confirm-button-label"),
      withCloseIcon: true,
      callbackConfirm() {
        router.push({ name: RouteName.VERIFICATION_SETTINGS });
        closeDialog();
      },
      callbackCancel() {
        closeDialog();
      },
    };

    const isMember = userStore.isTeamMember;
    const verificationLevelName = userStore.userActualLimit?.userLimit.name;

    switch (variant) {
      case "deposit":
        options.title = t("common.kyc-limit-dialog.deposit.title");
        options.text = t("common.kyc-limit-dialog.deposit.text", {
          status: verificationLevelName,
        });
        break;
      case "cards":
        options.title = t("common.kyc-limit-dialog.cards.title");

        if (!isMember) {
          options.text = t("common.kyc-limit-dialog.cards.text", {
            status: verificationLevelName,
          });
        } else {
          options.text = t("common.kyc-limit-dialog.cards.member-text");
          options.showBtnCancel = false;
          options.showBtnDeny = false;
          options.showBtnConfirm = true;
          options.btnConfirmText = t("OK");
          options.callbackConfirm = closeDialog;
        }
        break;
    }

    openDialog(options);
  };

  /**
   * Validates the KYC limit based on the provided card and deposit amounts, if false - dialog is being opened.
   *
   * @param {number} cardsAmount - The number of cards to validate against the KYC limit.
   * @param {number} depositAmount - The deposit amount to validate against the KYC limit.
   * @returns {Promise<boolean>} A promise that resolves to a boolean indicating if the KYC limit is valid.
   * @example
   * const isValid = await validateKycLimit(2, 1000);
   */
  const validateKycLimit = async (
    cardsAmount: number,
    depositAmount: number
  ): Promise<boolean> => {
    await updateVerificationActual();

    if (
      verificationActualData.value?.data &&
      verificationActualData.value.data?.remaining_cards !== null &&
      verificationActualData.value.data.remaining_cards < cardsAmount
    ) {
      openKycLimitDialog("cards");

      return false;
    }

    if (
      verificationActualData.value?.data &&
      verificationActualData.value?.data?.remaining_deposit !== null &&
      Number(verificationActualData.value?.data?.remaining_deposit) <
        depositAmount
    ) {
      openKycLimitDialog("deposit");

      return false;
    }

    return true;
  };

  return {
    validateKycLimit,
    isValidating: isFetchingVerificationActual,
    openKycLimitDialog,
  };
}
