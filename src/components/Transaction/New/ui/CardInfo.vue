<script setup lang="ts">
import CardsIcon from "@/components/Cards/CardsIcon.vue";
import { computed } from "vue";

interface Props {
  card?: any;
}

const props = withDefaults(defineProps<Props>(), {
  card: undefined,
});

const cardType = computed<string>(() => {
  return props.card.mask?.startsWith(String(4)) ? "visa" : "mastercard";
});

const isVisa = computed<boolean>(() => {
  return cardType.value === "visa";
});
</script>

<template>
  <div :class="$style.root">
    <div :class="$style.root__card">
      <CardsIcon
        :card="props.card"
        size="small" />
      <div class="flex flex-col">
        <span class="text-lg font-extrabold">{{ props.card._tariff }}</span>
        <span class="text-base text-greyscale-600">{{ props.card.mask }}</span>
      </div>
    </div>
    <div :class="$style.root__info">
      <span :class="$style.root__title">
        {{ $t("Card balance") }}
      </span>
      <span :class="$style.root__value">
        {{ props.card.account.balance }}
        {{ props.card.account._currency.iso_code }}
      </span>
    </div>
    <div :class="$style.root__info">
      <span :class="$style.root__title">
        {{ $t("card comment") }}
      </span>
      <span :class="$style.root__value">
        <template v-if="props.card.description"
          >{{ props.card.description }}
        </template>
        <template v-else>N/A</template>
      </span>
    </div>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply grid grid-cols-2 gap-5;

  &__card {
    @apply col-span-2 flex gap-3 items-center;
  }

  &__info {
    @apply flex flex-col;
  }

  &__title {
    @apply text-base text-greyscale-600 font-semibold;
  }

  &__value {
    @apply text-base font-medium break-all;
  }
}
</style>
