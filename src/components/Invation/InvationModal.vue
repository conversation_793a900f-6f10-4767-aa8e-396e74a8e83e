<script setup lang="ts">
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import UITransition from "@/components/ui/UITransition.vue";
import SupportTrigger from "@/components/SupportTrigger.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useNotifications } from "@/stores/notifications";
import { storeToRefs } from "pinia";
import { TBusinessMemberRequestsResource } from "@/types/api/TBusinessMemberRequestsResource";
import { useMasterService } from "@/services/MasterService";
import { TOAST_TYPE, useCallToast } from "@/composable";

const { t } = useI18n();
const { teamInvites } = storeToRefs(useNotifications());

const isLoading = ref<boolean>(false);
const isOpen = ref<boolean>(true);

const invite = computed<TBusinessMemberRequestsResource | null>(() => {
  return teamInvites.value.length ? teamInvites.value[0] : null;
});

const isModalShowed = computed(() => {
  return Boolean(invite.value) && isOpen.value;
});

const approveRequest = async () => {
  if (!invite.value) return;

  isLoading.value = true;

  const response = await useMasterService("businessMemberApprove", {
    urlParams: { id: invite.value.id },
  });

  if (!response.status) {
    console.error("TeamInvite->approveRequest error handled: ", response);
    useCallToast({
      title: response.message || "Error",
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });

    isLoading.value = false;
    isOpen.value = false;
    return;
  }

  // Reloading page instead data updates
  location.reload();
};

const rejectRequest = async () => {
  if (!invite.value) return;

  // Immediately close modal after reject button click
  isOpen.value = false;

  const response = await useMasterService("businessReject", {
    urlParams: { id: invite.value.id },
  });

  if (!response.status) {
    console.error("TeamInvite->rejectRequest error handled: ", response);
    useCallToast({
      title: response.message || "Error",
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });

    return;
  }

  useCallToast({
    title: t("team.inviteTeam.rejectedInvite"),
  });
};
</script>

<template>
  <UITransition>
    <div
      v-if="isModalShowed"
      class="invation-modal">
      <div class="head">
        <SupportTrigger color="blue-light" />
      </div>
      <div class="content">
        <!-- Image -->
        <div class="w-full">
          <img
            class="w-full"
            src="@/assets/img/invation-animojies.png" />
        </div>

        <!-- Text content -->
        <div class="text-center">
          <div class="content__title">
            {{ $t("team-invites.invation-modal.title") }}
          </div>

          <div class="content__text">
            <span class="whitespace-nowrap">
              {{ $t("team-invites.invation-modal.sent-by") }}
              {{ invite?.master_email }}
            </span>
            <br />
            {{ $t("team-invites.invation-modal.cannot-undone") }}
          </div>
        </div>

        <!-- Buttons -->
        <div class="w-full">
          <UIButton
            class="w-full"
            color="black"
            :is-loading="isLoading"
            @click="() => approveRequest()">
            {{ $t("team-invites.invation-modal.accept") }}
          </UIButton>

          <UIButton
            class="w-full mt-2"
            color="grey-free"
            :disabled="isLoading"
            @click="() => rejectRequest()">
            {{ $t("buttons.decline") }}
          </UIButton>
        </div>
      </div>
    </div>
  </UITransition>
</template>

<style lang="scss" scoped>
.invation-modal {
  @apply absolute z-6 top-0 right-0 w-screen h-screen flex flex-col justify-center items-center px-4 py-5 bg-bg-level-0;
}

.head {
  @apply absolute top-6 right-6 flex items-center gap-4;
}

.content {
  @apply max-w-[440px] flex flex-col items-center gap-10;

  &__title {
    @apply text-10 leading-11 font-medium;
  }

  &__text {
    @apply mt-1 text-4.5 leading-6 text-fg-secondary;
  }
}
</style>
