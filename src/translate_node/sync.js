const {
  loadFile,
  parseEnv,
  log,
  writeFile,
  removeFolder,
  checkFolders,
  moveFolder,
} = require("./utility.js");
const path = require("path");
const Constants = require("./constants.js");
const axios = require("axios");

let env = {
  WEBLATE_URL: "",
  WEBLATE_API_KEY: "",
};
const loadEnv = async () => {
  const envFile = await loadFile(path.resolve(__dirname, "../../.env"));
  if (!envFile.status) {
    return exit(Constants.messages.errors.envNotFound, "error");
  }
  env = parseEnv(envFile.data);
};
const validateEnv = () => {
  const keys = ["WEBLATE_API_KEY", "WEBLATE_URL"];
  for (let key of keys) {
    if (!env[key] || env[key] === "") {
      return exit(Constants.messages.errors.envKeyNotFound(key), "error");
    }
  }
};
const exit = (msg = undefined, type = "info") => {
  if (msg) log(type, msg);
  log("info", Constants.messages.info.exit);
  if (type !== "info") {
    process.exit(1);
  }
};
const languages = {
  en: "en",
  bg: "bg",
  zh: "zh",
  fr: "fr",
  de: "de",
  hi: "hi",
  it: "it",
  ja: "ja",
  pt: "pt",
  ru: "ru",
  es: "es",
  th: "th",
  tr: "tr",
  uk: "ua",
  id: "id",
};

const getKey = (slug, key) => {
  if (slug === "main") {
    return key;
  }
  return `${slug}.${key}`;
};
const main = async () => {
  await loadEnv();
  await validateEnv();
  const conf = { headers: { Authorization: `Token ${env.WEBLATE_API_KEY}` } };
  const { data } = await axios.get(
    `${env.WEBLATE_URL}projects/pst/components/`,
    conf
  );
  const projectSlugs = data.results.map((v) => {
    return v.slug;
  });
  console.log("Found ", projectSlugs.length, " projects: ", projectSlugs);

  const langKeys = Object.keys(languages);
  let result = {};

  await removeFolder(`${Constants.folders.temp}`);
  await checkFolders(`${Constants.folders.temp}`);
  await checkFolders(`${Constants.folders.tempLocales}`);

  const promises = [];
  for (const slug in projectSlugs) {
    for (const lang in langKeys) {
      const fileName = Object.values(languages)[lang];
      const langSlug = langKeys[lang];
      const projectSlug = projectSlugs[slug];

      promises.push(
        axios
          .get(
            `${env.WEBLATE_URL}translations/pst/${projectSlug}/${langSlug}/file/?format=json`,
            conf
          )
          .then(({ data }) => {
            Object.entries(data).forEach((entry) => {
              const [key, value] = entry;
              if (!result[fileName]) {
                result[fileName] = {};
              }
              result[fileName][getKey(projectSlug, key)] = value;
            });
          })
          .catch((e) => {
            if (
              e.response &&
              e.response.data &&
              e.response.data.detail !== "Not found." &&
              e.response.data.format !==
                "File format is not compatible with this translation"
            ) {
              console.error(e);
              throw new Error();
            }
          })
      );
    }
  }

  await Promise.all(promises);

  for (const entry of Object.entries(result)) {
    const [fileName, result] = entry;
    await writeFile(
      `${Constants.folders.tempLocales}/${fileName}.json`,
      JSON.stringify(result, null, 2)
    );
  }
  await moveFolder(
    `${Constants.folders.tempLocales}`,
    `${Constants.folders.locales}`
  );
};

main();
