<template>
  <Backdrop v-if="backdrop && isFocused" />
  <div
    :class="[
      $style.UiInputText,
      {
        [$style.UiInputText__with__icon]: icon,
        ['z-[1002]']: backdrop && isFocused,
      },
    ]">
    <label
      v-if="label"
      class="block"
      :class="{
        [labelClass]: true,
        'text-greyscale-600': state === 'default' && !error,
        'text-secondary-base': state === 'focus' && !error,
        'text-error-base': error,
      }">
      {{ label }}
    </label>
    <input
      v-if="autoCompleteProps == !'nope'"
      ref="input"
      :id="id"
      :value="value || modelValue"
      :type="type"
      :autocomplete="type === 'email' ? 'email' : autoCompleteProps"
      :placeholder="placeholder"
      :disabled="disabled"
      :minlength="minlength"
      :maxlength="maxlength"
      @input="onInputHandler"
      @change="$emit('change', $event.target.value)"
      @focus="onFocus"
      @blur="onBlur"
      :class="{
        'border-greyscale-300': state === 'default' && !error,
        'border-error-base': state === 'default' && error,
        [$style.input__focus]: state === 'focus' && !error,
        [$style.input__focus__error]: state === 'focus' && error,
        [$style.input]: true,
      }" />
    <input
      v-else
      ref="input"
      :id="+new Date()"
      :value="value || modelValue"
      :type="type"
      :name="+new Date()"
      autocomplete="off"
      autocorrect="off"
      autocapitalize="off"
      spellcheck="false"
      :placeholder="placeholder"
      :disabled="disabled"
      :minlength="minlength"
      :maxlength="maxlength"
      @input="onInputHandler"
      @change="$emit('change', $event.target.value)"
      @focus="onFocus"
      @blur="onBlur"
      :class="{
        'border-greyscale-300': state === 'default' && !error,
        'border-error-base': state === 'default' && error,
        [$style.input__focus]: state === 'focus' && !error,
        [$style.input__focus__error]: state === 'focus' && error,
        [$style.input]: true,
      }" />
    <span
      v-if="icon"
      :class="{
        [$style.UiInputText__icon]: true,
        [$style.UiInputText__icon__blur]: !isFocused,
        [$style.UiInputText__icon__focus]: true,
      }">
      <DynamicIcon :name="icon" />
    </span>
    <div
      v-if="helperText || $slots.helperText || error || helperLength"
      :class="$style.bottom">
      <span
        v-if="(helperText || $slots.helperText) && !error"
        class="font-semibold"
        :class="{
          'text-greyscale-600': state === 'default',
          'text-secondary-base': state === 'focus',
        }">
        <slot name="helperText" />
        {{ helperText }}
      </span>
      <span
        v-if="helperLength"
        class="font-semibold">
        {{ modelValue }}
      </span>
      <span
        v-if="error.length > 0"
        class="text-error-base font-semibold">
        {{ error }}
      </span>
    </div>
  </div>
</template>

<script>
import DynamicIcon from "@/components/icons/DynamicIcon";
import Backdrop from "@/components/ui/Backdrop/Backdrop.vue";

export default {
  name: "UiInputText",
  emits: ["change", "input", "update:modelValue"],
  components: { DynamicIcon, Backdrop },
  props: {
    placeholder: {
      type: String,
      default: "",
    },
    icon: {
      type: String,
      default: "",
    },
    autoCompleteProps: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "text",
    },
    label: {
      type: String,
      default: "",
    },
    labelClass: {
      type: String,
      default: "font-extrabold pb-2",
    },
    error: {
      type: String,
      default: "",
    },
    value: {
      type: [String, Number],
      default: null,
    },
    modelValue: {
      type: [String, Number],
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    shouldAutoFocus: {
      type: Boolean,
      default: false,
    },
    backdrop: {
      type: Boolean,
      default: false,
    },
    helperText: {
      type: String,
      default: "",
    },
    prefix: {
      type: String,
      default: "",
    },
    helperLength: {
      type: Boolean,
      default: false,
    },
    minlength: {
      type: Number,
      default: 1,
    },
    maxlength: {
      type: Number,
      default: 99999,
    },
  },
  data() {
    return {
      id: null,
      isFocused: false,
      state: "default",
    };
  },
  created() {
    this.id = (+new Date() * Math.random()).toString(30).substring(0, 4);
  },
  mounted() {
    if (this.$refs.input && this.shouldAutoFocus) {
      this.$refs.input.focus();
    }
  },
  methods: {
    onInputHandler(e) {
      this.$emit("input", e.target.value);
      this.$emit("update:modelValue", e.target.value);
    },
    onFocus(v) {
      this.state = "focus";
      this.$emit("focus", v);
      this.toogleFocus();
    },
    onBlur(v) {
      this.state = "default";
      this.$emit("blur", v);
      this.toogleFocus();
    },
    toogleFocus() {
      this.isFocused = !this.isFocused;
    },
  },
};
</script>

<style lang="scss" module>
.UiInputText {
  @apply relative w-full;

  input {
    @apply p-4;
  }

  &__with__icon {
    @apply relative;

    input {
      @apply py-4 pr-4 pl-12;
    }
  }

  &__icon {
    @apply absolute left-0 top-0 h-full my-auto px-4 flex items-center;

    &__focus {
      @apply text-secondary-base;
    }

    &__blur {
      @apply text-greyscale-500;
    }

    & svg {
      @apply w-[18px] h-[18px];
    }
  }

  &__error {
    @apply absolute top-0 left-0 ml-3 -mt-3 p-1 text-sm text-error-light bg-white rounded-full;

    &__label {
      @apply top-8;
    }
  }
}

.input {
  &__focus {
    @apply ring-1 ring-secondary-base border-secondary-base;

    &__error {
      @apply ring-1 ring-error-base border-error-base;
      &:not([disabled]):focus,
      &:not([disabled]):focus-visible,
      &:not([disabled]):active {
        @apply border-error-base;
      }
    }
  }

  /* Chrome, Safari, Edge, Opera */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  &[type="number"] {
    -moz-appearance: textfield;
  }
}

.bottom {
  @apply py-1 flex flex-col w-fit text-left text-[10px];

  & > span {
    & > a {
      @apply font-extrabold text-primary-base;
    }
  }
}
</style>
