<script setup lang="ts">
import UiInputText from "../../ui/InputText/InputText.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import InputCurrency from "@/components/ui/InputCurrency/InputCurrency.vue";
import Backdrop from "@/components/ui/Backdrop/Backdrop.vue";
import TransactionConfirm from "../TransactionConfirm.vue";
import TransactionAlert from "../TransactionAlert.vue";
import CardInfo from "../../ui/CardInfo.vue";
import AccountInfo from "../../ui/AccountInfo.vue";
import WalletInfo from "../../ui/WalletInfo.vue";

import { useAxios } from "@/helpers/axios";
import { useReason } from "@/helpers/validation";
import { computed, reactive, ref, watch } from "vue";
import { useUserStore } from "@/stores/user";
import { useI18n } from "vue-i18n";
import { afterTransfer } from "@/components/Transaction/New/helpers/withdraw";
import WithdrawConfirmModal from "@/components/Transaction/Withdraw/ConfirmModal.vue";
import { useRouter } from "vue-router";
import { TrackerService } from "@/helpers/tracker/tracker.service";

const router = useRouter();

interface Props {
  activeAccount?: any;
  activeCard?: any;
}

const isShowModalAccountNotVerified = ref<boolean>(false);

const userStore = useUserStore();

const { t } = useI18n();

const reason = useReason();

const props = withDefaults(defineProps<Props>(), {
  activeAccount: undefined,
  activeCard: undefined,
});

const emit = defineEmits(["close"]);

const actualKycLevel = ref<string>();

const initActualKycLevel = async () => {
  actualKycLevel.value = await userStore.getActualKycLevel();
};

initActualKycLevel();

const close = () => {
  emit("close");
};

const withdrawForm = reactive<any>({
  address: "",
  fromAmount: 0,
  toAmount: 0,
  isConfirm: false,
  isSubmit: false,
  status: "success",
  error: "",
});
// UI
const isFocused = ref<boolean>(false);
const activeFocus = ref<string>("");

const onClickContinue = () => {
  withdrawForm.isConfirm = true;
};

const onConfirmModal = async () => {
  await TrackerService.logEvent("upgrade kyc from withdraw", {});
  router.push("/app/settings/verification");
};

const onCancelModal = () => {
  isShowModalAccountNotVerified.value = false;
};

const onBlur = () => {
  if (!withdrawForm.fromAmount) {
    withdrawForm.fromAmount = 0;
  }
  if (!withdrawForm.toAmount) {
    withdrawForm.toAmount = 0;
  }
};
const disableFocus = () => {
  isFocused.value = false;
};
const setActiveFocus = (v: string) => {
  activeFocus.value = v;
};
const onFocusFrom = () => {
  isFocused.value = true;
  setActiveFocus("from");
};
const onFocusTo = () => {
  isFocused.value = true;
  setActiveFocus("to");
};

const fromCurrency = computed<string>(() => {
  return props.activeCard
    ? props.activeCard.account._currency.iso_code
    : props.activeAccount._currency.iso_code;
});

const minWithdrawAmount = computed(() => {
  if (
    actualKycLevel.value !== undefined &&
    !["unlimited", "old"].includes(actualKycLevel.value)
  ) {
    return Number(300);
  }
  return Number(userStore.userFees?.min_withdraw_amount ?? 0);
});

const withdrawFee = computed(() => {
  return Number(userStore.userFees?.withdraw || 0);
});

const feeThreshold = computed(() => {
  return Number(userStore.userFees?.min_withdraw_fee_threshold || 0);
});

userStore.getUserFees();

watch(
  () => withdrawForm.fromAmount,
  (newValue) => {
    if (activeFocus.value === "from") {
      const newToAmount =
        Number(newValue) * withdrawFee.value > feeThreshold.value
          ? Number(newValue) - Number(newValue) * withdrawFee.value
          : Number(newValue) - feeThreshold.value;
      withdrawForm.toAmount = newToAmount > 0 ? newToAmount : 0;
    }
  }
);

watch(
  () => withdrawForm.toAmount,
  (newValue) => {
    if (activeFocus.value === "to") {
      withdrawForm.fromAmount =
        Number(newValue) * withdrawFee.value > feeThreshold.value
          ? Number(newValue) / (1 - Number(withdrawFee.value))
          : Number(newValue) + feeThreshold.value;
    }
  }
);

const isButtonDisabled = computed(() => {
  return (
    reason.errorMessage.value !== undefined ||
    reason.value.value.length === 0 ||
    withdrawForm.address.length < 8 ||
    withdrawForm.toAmount <= 0
  );
});

const currentError = computed(() => {
  const activeAccountCurrencyCode = props.activeAccount._currency.iso_code;
  const maxAvailableWithdraw: { [key: string]: number } = {
    USDT: minWithdrawAmount.value,
    BTC: 0.04,
  };

  if (props.activeAccount.currency_id === 14) {
    return t("withdraw.toExternal.btcTemporaryUnavailable");
  }

  if (
    Object.keys(maxAvailableWithdraw).includes(activeAccountCurrencyCode) &&
    Number(withdrawForm.fromAmount) <
      maxAvailableWithdraw[activeAccountCurrencyCode]
  ) {
    return t("withdrawFromPlatform.amountMustBeGreaterThanMinimum");
  }

  if (Number(withdrawForm.fromAmount) > Number(props.activeAccount.balance)) {
    return t("Amount must be less than balance");
  }

  return null;
});

const withdraw = async () => {
  const config = {
    user_account_id: props.activeAccount.id,
    amount: String(withdrawForm.fromAmount),
    wallet: withdrawForm.address,
    reason: reason.value.value,
  };

  await useAxios()
    .post("/withdrawal", config)
    .then(() => {
      withdrawForm.status = "success";
    })
    .catch((e) => {
      withdrawForm.status = "error";
      if (e.response?.data?.message) {
        withdrawForm.error = t(e.response?.data?.message);
      }
    })
    .finally(() => {
      afterTransfer();
    });

  withdrawForm.isSubmit = true;
  withdrawForm.isConfirm = false;
};
</script>

<template>
  <WithdrawConfirmModal
    v-if="isShowModalAccountNotVerified"
    @confirm="onConfirmModal"
    @cancel="onCancelModal" />

  <div class="flex flex-col gap-5">
    <UiInputText
      :value="withdrawForm.address"
      :placeholder="$t('Enter the USDT')"
      :label="$t('Recepient’s wallet address')"
      label-class="text-sm font-medium text-greyscale-600 pb-1 text-greyscale-600"
      :helper-text="
        withdrawForm.address.length >= 8
          ? $t('Recepient will receive funds to USDT wallet')
          : ''
      "
      :backdrop="withdrawForm.address.length <= 8"
      :error="''"
      @input="(v) => (withdrawForm.address = v)" />

    <ui-input-text
      v-if="withdrawForm.address.length >= 8"
      :value="reason.value.value"
      :label="$t('Reason to withdrawal')"
      :placeholder="$t('Enter your reason')"
      default-value=""
      :error="reason.errorMessage.value || ''"
      label-class="text-sm font-medium text-greyscale-600 pb-1 text-greyscale-600"
      class="w-full col-span-2"
      @input="reason.setValue" />

    <div
      v-if="
        withdrawForm.address.length >= 8 &&
        !reason.errorMessage.value &&
        reason.value.value.length > 0
      "
      v-click-outside="disableFocus"
      class="mt-5 grid md:grid-cols-2 gap-5"
      :class="{ 'z-[1002]': isFocused }">
      <h6 class="col-span-2">{{ $t("Specify the transfer amount") }}</h6>
      <InputCurrency
        :label="$t('withdraw_amount')"
        :value="withdrawForm.fromAmount"
        :currency="fromCurrency"
        :error="''"
        :min="minWithdrawAmount + 1"
        :class="{ 'col-span-2': withdrawForm.address.length <= 8 }"
        @input="(v) => (withdrawForm.fromAmount = v)"
        @focus="onFocusFrom"
        @blur="onBlur" />
      <InputCurrency
        :label="$t('Receive amount')"
        :value="withdrawForm.toAmount"
        :currency="fromCurrency"
        @input="(v) => (withdrawForm.toAmount = v)"
        @focus="onFocusTo"
        @blur="onBlur" />

      <div
        v-if="withdrawForm.address.length >= 3"
        class="col-span-2 bg-greyscale-100 w-full flex justify-between text-base p-5 rounded-base mb-5">
        <span class="font-semibold text-greyscale-600">{{ $t("Fee") }}</span>
        <span class="font-medium">
          {{ (withdrawFee * 100).toFixed(1) }}% (but at least
          {{ feeThreshold }},00 USDT)</span
        >
      </div>

      <!--
      <div
        v-if="currentError"
        class="mb-6 text-error-base"
      >
        {{ currentError }}
      </div>
      -->

      <UiButton
        v-if="!currentError"
        type="secondary"
        :title="$t('continue')"
        class="col-span-2"
        :disabled="isButtonDisabled"
        @click="onClickContinue()" />
    </div>

    <Backdrop
      v-if="isFocused && withdrawForm.address.length >= 8"
      :blur="true" />

    <TransactionConfirm
      v-if="withdrawForm.isConfirm"
      type="withdraw"
      :from-amount="withdrawForm.fromAmount"
      :from-currency="fromCurrency"
      :to-amount="withdrawForm.toAmount"
      :to-currency="fromCurrency"
      :confirm="false"
      :from-account-i-d="activeAccount.id"
      @confirm="withdraw"
      @back="withdrawForm.isConfirm = false">
      <template #from>
        <CardInfo
          v-if="props.activeCard"
          :card="props.activeCard" />
        <AccountInfo
          v-else
          :account="props.activeAccount" />
      </template>
      <template #to>
        <WalletInfo
          :address="withdrawForm.address"
          :currency="fromCurrency" />
      </template>
    </TransactionConfirm>

    <TransactionAlert
      v-if="withdrawForm.isSubmit"
      :type="withdrawForm.status"
      :error="withdrawForm.error"
      @close="close" />
  </div>
</template>
