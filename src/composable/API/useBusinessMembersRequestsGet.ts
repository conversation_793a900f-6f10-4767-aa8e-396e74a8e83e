import type { TBusinessMemberRequestsResource } from "@/types/api/TBusinessMemberRequestsResource";
import { useGetRequest } from "../useGetRequest";
import type { UseFetchOptions } from "@vueuse/core";

export const useBusinessMembersRequestsGet = (
  config = {},
  options: UseFetchOptions = {}
) => {
  return useGetRequest<{ data: TBusinessMemberRequestsResource[] }>(
    "business/member-requests",
    config,
    options
  );
};
