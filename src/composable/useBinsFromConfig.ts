import { computed, ref } from "vue";
import { useCardBinsGet } from "@/composable/API/useCardBinsGet";
import type { TCardTariffSlug } from "@/composable/Tariff";
import type { TBinResource } from "@/types/api/TBinResource";
import type { TBinInfo } from "@/components/CreateCardV2/types";
import { binConfig } from "@/config/cards";

/**
 * Getting bins from config.
 *
 * @param {TCardTariffSlug} slug - The slug of the tariff
 * @returns {Object} An object containing the bins and a loading state.
 * @category Composables
 */
export function useBinsFromConfig(slug: TCardTariffSlug) {
  const isFetching = ref<boolean>(false);
  const binsRaw = ref<TBinResource[]>([]);

  if (slug === "ultima") {
    fetchUltimaBins();
  } else {
    fetchBins();
  }

  const bins = computed<TBinInfo[]>(() => {
    if (!binsRaw.value) {
      return [];
    }
    return binsRaw.value.map((item) => getBinFromConfig(item));
  });

  async function fetchBins() {
    isFetching.value = true;
    const { data: advBins } = await useCardBinsGet(slug);
    if (advBins.value?.data) {
      binsRaw.value = advBins.value.data;
    }
    isFetching.value = false;
  }

  async function fetchUltimaBins() {
    isFetching.value = true;
    const [
      // Do not delete. Temporary disabled PST-T-4497
      // { data: ultimaBins },
      { data: ultima3dsBins },
    ] = await Promise.all([
      // useCardBinsGet("ultima"),
      useCardBinsGet("ultima-3ds"),
    ]);
    // Do not delete. Temporary disabled PST-T-4497
    // let ultimaBinsData: TBinResource[] = [];
    // if (Array.isArray(ultimaBins.value?.data)) {
    //   ultimaBinsData = ultimaBins.value?.data.map((item) => {
    //     item.slug = "ultima";
    //     return item;
    //   });
    // }

    let ultima3dsBinsData: TBinResource[] = [];
    if (Array.isArray(ultima3dsBins.value?.data)) {
      ultima3dsBinsData = ultima3dsBins.value.data.map((item) => {
        item.slug = "ultima-3ds";
        return item;
      });
    }

    binsRaw.value = [
      // Do not delete. Temporary disabled PST-T-4497
      // ...ultimaBinsData,
      ...ultima3dsBinsData,
    ];
    isFetching.value = false;
  }

  const getBinFromConfig = (bin: TBinResource): TBinInfo => {
    const found =
      binConfig.find((i) => {
        return i.slug === slug && i.bin === bin.bin;
      }) ?? bin;

    return {
      ...found,
      slug: bin.slug ?? found.slug,
      under_maintenance: bin.under_maintenance,
    };
  };

  return {
    bins,
    isFetching,
  };
}
