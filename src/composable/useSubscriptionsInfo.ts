import { computed } from "vue";
import { currencyFormatter } from "@/helpers/currencyFormatter";
import { useSubscriptionsInfoGet } from "@/composable/API/useSubscriptionsInfoGet";
import { useUserSpecialCondition } from "@/composable/useUserSpecialCondition";
import { SubscriptionStatusCode } from "@/constants/subscription_status_code";

const {
  data,
  execute: fetchSubscriptionsInfo,
  isFetching,
  error,
} = useSubscriptionsInfoGet({ immediate: false });

/**
 * Getting data about user subscriptions
 *
 * @example
 * const { subscriptionsStatus } = useSubscriptionsInfo();
 *
 * @category Composables
 */
export function useSubscriptionsInfo(
  init: { immediate: boolean } = { immediate: true }
) {
  const { hasSpecialCondition, specialData } = useUserSpecialCondition();

  if (init.immediate && !isFetching.value) {
    fetchSubscriptionsInfo();
  }

  const subscriptionsInfo = computed(() => {
    return data.value?.data ?? null;
  });

  /**
   * todo: rename to hasSubscription
   */
  const subscriptionsStatus = computed(() => {
    return subscriptionsInfo.value?.status !== SubscriptionStatusCode.NOT_FOUND;
  });

  const totalCashbackAmount = computed(() => {
    if (!subscriptionsInfo.value) return currencyFormatter(0, "USD", true);

    const totalCashback = Number(subscriptionsInfo.value.approved_cashback!);

    return currencyFormatter(totalCashback, "USD", true);
  });

  const startDate = computed<Date>(() => {
    const date = new Date(subscriptionsInfo.value?.ordered_next!);
    return new Date(date.getFullYear(), date.getMonth() - 1, date.getDate());
  });

  const isUltimaOnlySubscription = computed<boolean>(() => {
    return subscriptionsInfo.value?.tariff_name === "Ultima Only";
  });

  /**
   * @todo rename to cardsBySubscriptionCount
   */
  const cardsToRelease = computed<number>(() => {
    const cardsLimit = subscriptionsInfo.value?.cards_limit;
    const cardsCount = subscriptionsInfo.value?.cards_count;

    if (
      subscriptionsStatus.value &&
      typeof cardsLimit === "number" &&
      typeof cardsCount === "number"
    ) {
      const cardsCountDifference = cardsLimit - cardsCount;
      return cardsCountDifference < 0 ? 0 : cardsCountDifference;
    }

    return 0;
  });

  const cardsByUserSpecialCount = computed<number | null>(() => {
    if (
      hasSpecialCondition.value &&
      specialData.value &&
      specialData.value.data.card_count_max &&
      !subscriptionsStatus.value
    ) {
      const cardsCountDifference =
        specialData.value.data.card_count_max -
        specialData.value.data.total_purchased_cards;

      return cardsCountDifference < 0 ? 0 : cardsCountDifference;
    }

    return null;
  });

  return {
    subscriptionsInfo,
    fetchSubscriptionsInfo,
    totalCashbackAmount,
    cardsByUserSpecialCount,
    subscriptionsStatus,
    cardsToRelease,
    startDate,
    isFetching,
    error,
    isUltimaOnlySubscription,
  };
}
