import { ref, computed } from "vue";
import { defineStore } from "pinia";
import { useAxios } from "@/helpers/axios";
import { useMasterService } from "@/services/MasterService";
import { useWebSocket } from "@/helpers/webSoket";
import { useUserStore } from "@/stores/user";
// import Notification from "@/components/Notifications/NotifyTypes/UnifyNotification.vue";
import config from "@/config/env";
import type { TPaginatedApiResponse } from "@/types/api";

const defaultState = {
  list: [],
  page: 1,
  smallLoading: false,
  teamInvites: [],
  lastPage: 100,
  total: 0,
};

export const useNotifications = defineStore("notifications-deprecated", () => {
  // state
  const state: any = ref(JSON.parse(JSON.stringify(defaultState)));
  const critical = ref<Array<any>>([]);
  const userStore = useUserStore();

  // computed
  const newNotificationsCount = computed(
    () => newNotifications.value.length + state.value.teamInvites.length
  );
  const newNotifications = computed(() =>
    // TODO: DEL
    state?.value?.list?.filter((item: any) => !item?.notification?.read_at)
  );
  const notifications = computed(() => state?.value?.list);
  const canGetMoreItems = computed(
    () => state.value.page <= state.value.lastPage
  );

  // actions
  // fetch new notifications
  async function getNotifications(reset: boolean = false) {
    if (!canGetMoreItems.value) return;

    try {
      state.value.smallLoading = true;

      if (reset) {
        state.value.page = 1;
      }

      const params = { page: state.value.page };

      const result = await useAxios().get("/user/notification", { params });

      const inviteResponse = await useMasterService("businessMemberRequests");

      if (!inviteResponse?.status) {
        console.error(
          "Notifications->getNotifications error handled: ",
          inviteResponse
        );
      }

      state.value.teamInvites = inviteResponse?.data || [];
      state.value.lastPage = result.data.meta.last_page;
      state.value.total = result.data.meta.total;

      if (reset) {
        state.value.list = result.data.data;
      } else {
        state.value.list = [...state.value.list, ...result.data.data];
      }
    } catch (ex) {
      console.error(
        "Notifications->getNotifications->catch error handled: ",
        ex
      );
    } finally {
      state.value.smallLoading = false;
    }
  }

  // set default state
  const setDefaultState = () =>
    (state.value = JSON.parse(JSON.stringify(defaultState)));

  // read all notifications
  async function readAllNotifications() {
    try {
      await useAxios().get("/user/notification/new");

      return true;
    } catch (ex) {
      console.error("Notifications->readAllNotifications error handled: ", ex);

      return false;
    }
  }

  const getCritical = async () => {
    try {
      const result = await useAxios().get<TPaginatedApiResponse<Array<any>>>(
        "/user/notification/new",
        {
          params: { priority: "critical" },
        }
      );

      critical.value = result.data.data;
    } catch (ex) {
      console.error("Notifications->getCritical error handled: ", ex);
    }
  };

  const removeCritical = async (id: number) => {
    critical.value = critical.value.filter(
      (data) => data.notification.id !== id
    );
  };

  // setup
  if (config.wsNotificationsUrl) {
    // socket
    useWebSocket({
      url: `${config.wsNotificationsUrl}uuid=${userStore.user.uuid}`,
      onMessage: (data) => {
        // state.value.list.length < 25 - R-845
        if (data.action === "notification" && state.value.list.length < 25) {
          const notification = data.data.notification;

          state.value.list = [notification, ...state.value.list];
        }

        if (data?.action === "notification") {
          if (data?.data?.notification?.priority === "critical") {
            critical.value.push({ notification: data.data.notification });
          }
        }
      },
    });
  }

  //
  getCritical().then(null);

  return {
    notifications,
    state,
    removeCritical,
    getNotifications,
    readAllNotifications,
    newNotificationsCount,
    newNotifications,
    canGetMoreItems,
    setDefaultState,
    critical,
  };
});
