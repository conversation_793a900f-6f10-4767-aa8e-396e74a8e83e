<script>
import UiInputSearch from "@/components/ui/InputSearch/InputSearch";

export default {
  name: "FaqSearch",
  components: { UiInputSearch },
  props: {
    search: {
      type: String,
      default: "",
    },
  },
  emits: ["input"],
  data() {
    return {
      frequentlySearch: ["Intl trx fee", "Cards", "3D Secure"],
    };
  },
  created() {
    if (this.$route.params?.search) {
      this.$emit("input", this.$route.params.search);
    }
  },
  methods: {
    setSearch(value) {
      this.$emit("input", value);
    },
  },
};
</script>
<template>
  <div :class="faqSearch.root">
    <div class="flex flex-col gap-3">
      <h4>{{ $t("How can we help") }}?</h4>
      <span class="text-base opacity-60 font-medium hidden md:block">
        {{ $t("Type your question or keyword") }}</span
      >
    </div>
    <ui-input-search
      placeholder="Search"
      :value="search"
      @input="(e) => $emit('input', e)" />
    <div class="hidden md:flex flex-col gap-3">
      <span class="text-sm opacity-60 font-medium">
        {{ $t("Frequently searched for") }}:</span
      >
      <ul :class="faqSearch.recent">
        <li
          v-for="(item, index) in frequentlySearch"
          :key="index"
          @click="() => setSearch(item)">
          {{ item }}
        </li>
      </ul>
    </div>
  </div>
</template>

<style lang="scss" module="faqSearch">
.root {
  @apply flex flex-col gap-2;
  @screen md {
    @apply gap-6 py-10 px-8 bg-bg-level-1 rounded-2xl w-fit;
  }
}
.recent {
  @apply flex flex-wrap gap-3;

  & > li {
    @apply font-semibold text-sm py-2 px-3 border border-secondary-base rounded-sm;
    @apply cursor-pointer hover:bg-secondary-base hover:text-white transition-all;
  }
}
</style>
