import type { TSubscriptionTariffResource } from "./TSubscriptionTariffResource";
import type { TUserAccountResource } from "./TUserAccountResource";
import type { TTagResource } from "./TTagResource";
import type { TCardAutoRefillResource } from "@/types/api/TCardAutoRefillResource";
import type { TCardMessage } from "@/types/api/TCardMessage";

export type TCardResource = {
  id: number;
  user_account_id: number;
  holder_name: string;
  holder_address: string;
  mask: string;
  description: string;
  favorite: number;
  status: number;
  simple_status: "active" | "inactive";
  has_waiting_transactions: false;
  ordered_at: string;
  ordered_until: string;
  created_at: string;
  account: TUserAccountResource;
  tariff_id: number;
  need_amount: boolean;
  is_zero_withdrawal_available: boolean;
  limits_equals: boolean;
  tags: TTagResource[];
  subscription_tariff: TSubscriptionTariffResource | null;
  user_id: number;
  user_name: string | null;
  user_email: string;
  auto_refill: null | TCardAutoRefillResource;
  under_maintenance: boolean;
  code: string;
  card_message: TCardMessage | null;
};
