<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";

interface Props {
  title?: string;
  text?: string;
  type?: "info" | "alert";
}

const props = defineProps<Props>();
const { t } = useI18n();
const messageTitle = computed(
  () => props.title || getMessageTitle(props.type || "info")
);
const getMessageTitle = (type: string) => {
  switch (type) {
    case "info":
      return t("label.information");
    case "alert":
      return t("label.attention");
    default:
      return t("label.information");
  }
};
</script>
<template>
  <div
    :class="[
      transactionMessage.root,
      transactionMessage[`type-${props.type || 'info'}`],
    ]">
    <div :class="transactionMessage.title">{{ messageTitle }}</div>
    <div
      v-if="props.text || $slots.default"
      :class="transactionMessage.text">
      <template v-if="$slots.default">
        <slot />
      </template>
      <template v-else>
        {{ props.text }}
      </template>
    </div>
  </div>
</template>

<style lang="scss" module="transactionMessage">
.root {
  @apply flex flex-col gap-3 p-4 rounded-[6px];
}

.type {
  &-info {
    @apply bg-neutral-100;
  }

  &-alert {
    @apply bg-bg-red-light;
  }
}

.title {
  @apply text-xl leading-6 font-semibold;
}

.text {
  @apply flex flex-col text-[15px] text-neutral-800;
}
</style>
