export interface IBinInfo {
  bin: string;
  gateway: "mastercard" | "visa" | "";
  secure: boolean;
  autoSecure?: boolean;
  private: boolean;
  featured: boolean;
  transferFee: string;
  depositFee: string;
  cashback: string;
  monthlyPayment: string;
  recommended: any[];
  slug: string;
  default?: {
    transferFee?: string;
    depositFee?: string;
    cashback?: string;
    monthlyPayment?: string;
  };
  tariffs?: string[];
  under_maintenance: boolean;
}
