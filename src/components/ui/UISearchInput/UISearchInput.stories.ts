import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import { action } from "@storybook/addon-actions";
import { ref } from "vue";
import UISearchInput from "@/components/ui/UISearchInput/UISearchInput.vue";
import BitcoinSign from "@/assets/svg/icon/bitcoin-sign.svg";
import type { ComponentProps } from "vue-component-type-helpers";
import type { UISearchInputProps } from "@/components/ui/UISearchInput/types";

const meta: Meta<ComponentProps<UISearchInputProps>> = {
  title: "UI/UISearchInput",
  component: UISearchInput,
  tags: ["autodocs"],
  argTypes: {
    modelModifiers: {
      control: false,
    },
    error: {
      control: { type: "text" },
    },
    size: {
      control: { type: "radio" },
      options: ["s", "m", "l"],
    },
    disabled: {
      control: { type: "boolean" },
    },
    readonly: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<UISearchInputProps>;

export const Default: Story = {
  args: {
    size: "m",
    placeholder: "Search",
    label: "Search",
    helperText: "Lorem ipsum dolor sit amet.",
    disabled: false,
    readonly: false,
  },
  render: (args) => ({
    components: { UISearchInput },
    setup() {
      const actionTextClickHandler = action("Click on action text");
      const searchValue = ref("");
      return {
        args,
        searchValue,
        actionTextClickHandler,
      };
    },
    template: `
      <section class="flex flex-wrap gap-12 mb-12"> 
        <div>
          <UISearchInput
            class="w-[250px]"
            v-bind="args"
            v-model="searchValue">
            <template #action>
              <button
                class="text-fg-blue hover:text-blue-500"
                @click="actionTextClickHandler">
                Action Slot
              </button>
            </template>
          </UISearchInput>
          <div class="mt-1">input value: {{ searchValue }}</div>
        </div>
      </section>
    `,
  }),
};

export const SearchInputSizes: Story = {
  args: {
    label: "Search",
    placeholder: "Search",
  },
  render: (args) => ({
    components: { UISearchInput },
    setup() {
      const searchValue = ref("");
      return { args, searchValue };
    },
    template: `
      <section class="flex flex-wrap gap-12">
        <UISearchInput
          class="w-[250px]"
          v-bind="args"
          v-model="searchValue"
          size="s" />
        <UISearchInput
          class="w-[250px]"
          v-bind="args"
          v-model="searchValue"
          size="m" />
        <UISearchInput
          class="w-[250px]"
          v-bind="args"
          v-model="searchValue"
          size="l" />
      </section>
    `,
  }),
};

export const WithIcon: Story = {
  // @ts-ignore
  render: () => ({
    components: { UISearchInput, BitcoinSign },
    setup() {
      const searchValue = ref("");
      return { searchValue };
    },
    template: `
      <section class="flex flex-wrap gap-12">
        <UISearchInput
          class="w-[250px]"
          v-model="searchValue"
          label="Search"
          placeholder="Search"
          size="m">
          <template #rightIcon>
            <BitcoinSign class="w-6 h-auto text-fg-tertiary"/>
          </template>
        </UISearchInput>
      </section>
    `,
  }),
};
