import type { TCardTariffSlug } from "@/composable";
import type { TUISelectOption } from "@/components/ui/UISelect/types";

export type TPaymentSystem =
  | "visa"
  | "visa-3ds"
  | "mastercard"
  | "mastercard-3ds";

export const removeSecureSuffix = (slug: TCardTariffSlug): TCardTariffSlug => {
  if (slug.includes("-3ds")) {
    return slug.replace("-3ds", "") as TCardTariffSlug;
  } else {
    return slug;
  }
};

export const addSecureSuffix = (slug: TCardTariffSlug): TCardTariffSlug => {
  if (slug.includes("-3ds")) {
    return slug;
  } else {
    return `${slug}-3ds` as TCardTariffSlug;
  }
};

export type TPaymentSystemOption = TUISelectOption<{
  icon: string;
  value: string;
}>;
