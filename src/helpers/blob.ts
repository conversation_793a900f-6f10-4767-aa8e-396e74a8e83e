import type { TExportFileType } from "@/types/payments";
import { LocalStorageKey } from "@/constants/local_storage_key";

/**
 * Downloads data as a file using a Blob and temporary anchor element.
 * Supports CSV and XLSX file formats.
 * @TODO have nothing to do with stream, rename
 *
 * @example
 * // Download as CSV
 * downloadFromStream(csvData, 'transactions', 'csv');
 *
 * @example
 * // Download as Excel
 * downloadFromStream(excelData, 'report', 'xlsx');
 *
 * @category Helpers
 *
 * @param {any} data - The data to be downloaded
 * @param {string} [name='history'] - The filename without extension
 * @param {TExportFileType} file_extension - File extension ('csv' or 'xlsx')
 * @returns {void}
 */
export function downloadFromStream(
  data: any,
  name: string = "history",
  file_extension: TExportFileType
) {
  const blob = new Blob([data], {
    type: `application/${
      file_extension === "csv"
        ? "csv"
        : "vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    }`,
  });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = `${name}.${file_extension}`;
  link.click();
  URL.revokeObjectURL(link.href);
}

/**
 * Checks if a user can perform an export operation based on rate limiting.
 * Prevents multiple exports within a 60-second window for the same user/group combination.
 * @TODO have nothing to do with blob, move.
 *
 * @example
 * // Check if user can export
 * if (exportIsAvailable('user123')) {
 *   // proceed with export
 * } else {
 *   // show rate limit message
 * }
 *
 * @example
 * // Check if user can export for specific group
 * if (exportIsAvailable('user123', 'payments')) {
 *   // proceed with group-specific export
 * }
 *
 * @category Helpers
 *
 * @param {string} userKey - Unique identifier for the user
 * @param {string} [group] - Optional group identifier for separate rate limiting
 * @returns {boolean} True if export is allowed, false if rate limited
 *
 */
export function exportIsAvailable(userKey: string, group?: string) {
  const finalKey = userKey + LocalStorageKey.PAYMENTS_EXPORT_TS + group;
  const userPreviousExportTs = localStorage.getItem(finalKey);

  // if export does not exist or
  if (
    userPreviousExportTs &&
    Date.now() - Number(userPreviousExportTs) < 60000
  ) {
    return false;
  } else {
    localStorage.setItem(finalKey, Date.now().toString());
    return true;
  }
}
