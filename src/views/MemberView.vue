<template>
  <div class="p-5 flex flex-col gap-7">
    <div>
      <span
        class="text-fg-tertiary cursor-pointer"
        @click="onMembersClick">
        Members /
      </span>
      {{ currentMember?.member ?? currentMember?.email }}
    </div>
    <div class="flex justify-between items-center">
      <div class="flex flex-col">
        <div class="text-6 font-medium">
          {{ t("memberSettings.header.title") }}
        </div>
        <div class="text-4 text-fg-secondary">
          {{ t("memberSettings.header.subTitle") }}
        </div>
      </div>
      <UIButton
        color="red-light"
        @click="onRemoveMember">
        <template #left>
          <DynamicIcon name="user-remove" />
        </template>
        {{ t("memberSettings.removeMember") }}
      </UIButton>
    </div>

    <div class="flex flex-col gap-2.5">
      <div class="title2">
        {{ t("memberSettings.header.details") }}
      </div>
      <div class="flex gap-4">
        <UITextInput
          v-model="memberDetails.name"
          :label="$t('team.addMembers.namePlaceholder')"
          size="m"
          :disabled="isLoading"
          :max-length="255"
          :display-max-length-counter="false"
          @blur="onBlur" />
        <UITextInput
          v-model="memberDetails.mail"
          :label="$t('memberSettings.details.mail')"
          readonly
          size="m"
          :display-max-length-counter="false" />
      </div>
    </div>

    <div>
      <div class="title2">
        {{ t("accounts") }}
      </div>
      <MemberAccounts :member-id="memberId" />
    </div>

    <CardsTable />

    <MembersDeleteModal @close="onDeleteModalClose" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";

import {
  TOAST_TYPE,
  useBusinessMemberUpdatePost,
  useCallToast,
} from "@/composable";
import CardsTable from "@/components/CardsTable/CardsTable.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import MemberAccounts from "@/components/Member/MemberAccounts.vue";
import { useMembersState } from "@/components/Members/useMembersState";
import MembersDeleteModal from "@/components/Members/MembersDeleteModal.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UITextInput from "@/components/ui/UITextInput/UITextInput.vue";
import { RouteName } from "@/constants/route_name";

const { t } = useI18n();
const router = useRouter();
const route = useRoute();

const memberId = Number(route.params.memberId);

const { setModalsState, currentMember, ensureMember } = useMembersState();
ensureMember(memberId);

const memberDetails = computed(() => {
  return {
    name: currentMember.value?.member,
    mail: currentMember.value?.email,
  };
});

const isLoading = ref(false);

const onBlur = async () => {
  if (memberDetails.value.name?.trim()) {
    isLoading.value = true;
    const { onFetchError } = await useBusinessMemberUpdatePost(memberId, {
      member_name: memberDetails.value.name ?? "",
    });
    isLoading.value = false;

    onFetchError(() => {
      memberDetails.value.name = currentMember.value?.member;
      useCallToast({
        title: t("errors.universal-request-error"),
        options: {
          id: "transfer-card-to-member-error",
          type: TOAST_TYPE.ERROR,
        },
      });
    });
  } else {
    memberDetails.value.name = currentMember.value?.member;
  }
};

const onRemoveMember = () => {
  setModalsState("deleteMemberModal", true);
};

const onDeleteModalClose = () => {
  onMembersClick();
};

const onMembersClick = () => {
  router.push({ name: RouteName.TEAM_MEMBERS });
};
</script>

<style lang="scss" scoped>
.title2 {
  @apply text-5 font-medium;
}
</style>
