import type {
  TFetchFunctions,
  TLoadMoreCardsPagination,
  TLoadMoreFnRequest,
  TLoadPaginationState,
  TransferFormState,
} from "@/components/TransferModal/types";
import type { TCardResource } from "@/types/api/TCardResource";
import type { TResponseMeta } from "@/types/api/TResponseMeta";
import { useBusinessMembersCardsGet, useCardsGet } from "@/composable";
import { perPage } from "@/components/TransferModal/useTransferContext/factory/factoryFetch";
import type { Ref } from "vue";

export const createLoadMorePagination = (): TLoadPaginationState => {
  return {
    from: {
      // we start from page 2 because we already have first page in state from initialization
      current_page: 2,
      per_page: perPage,
      last_page: 1,
      search: "",
    },
    to: {
      current_page: 2,
      per_page: perPage,
      last_page: 1,
      search: "",
    },
  };
};

export const loadMoreMemberCards =
  (member_id: number) =>
  async (
    request: TLoadMoreFnRequest
  ): Promise<{ data: TCardResource[]; meta: Partial<TResponseMeta> }> => {
    const response = await useBusinessMembersCardsGet({
      user_id: member_id?.toString(),
      ...request,
    });

    return {
      data: response.data.value?.data || [],
      meta: response.data.value?.meta || {},
    };
  };

export const loadMoreUserCards =
  () =>
  async (
    request: TLoadMoreFnRequest
  ): Promise<{ data: TCardResource[]; meta: Partial<TResponseMeta> }> => {
    const response = await useCardsGet({
      ...request,
    });

    return {
      data: response.data.value?.data || [],
      meta: response.data.value?.meta || {},
    };
  };
export const createLoadMoreMethod = (
  direction: "from" | "to",
  state: Ref<TransferFormState>,
  pagination: TLoadMoreCardsPagination,
  fetchFunctions: TFetchFunctions
) => {
  const loadMoreCardsFn =
    direction === "from"
      ? fetchFunctions.from.loadMoreCards
      : fetchFunctions.to.loadMoreCards;

  const cardsKey = direction === "from" ? "fromCardsOptions" : "toCardsOptions";

  return async (value: boolean) => {
    if (!value) return;

    if (pagination.current_page > pagination.last_page + 1) {
      return;
    }

    const response = await loadMoreCardsFn({
      page: pagination.current_page,
      per_page: pagination.per_page,
      search: pagination.search,
    });

    pagination.last_page = response.meta?.last_page || 1;

    state.value[cardsKey] = state.value[cardsKey].concat(response.data || []);

    pagination.current_page += 1;
  };
};
