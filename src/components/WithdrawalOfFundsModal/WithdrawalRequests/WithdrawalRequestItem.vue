<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { computed } from "vue";
import {
  statusesToValues,
  statusNumbersToColors,
  statusNumbersToNames,
  WithdrawalRequestsStatuses,
} from "@/components/WithdrawalOfFundsModal/WithdrawalRequests/useWithdrawalRequests";
import { useDateFormattedWithLocale } from "@/composable";
import { type TWithdrawalResource } from "@/types/api/TWithdrawalResource";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

type TProps = {
  item: TWithdrawalResource;
  shortMode?: boolean;
};

const props = defineProps<TProps>();
const emit = defineEmits(["cancel"]);
const { t } = useI18n();

const statusName = computed(() => {
  return t(statusNumbersToNames[props.item.status]);
});

const statusColor = computed(() => {
  return statusNumbersToColors[props.item.status];
});

const dateDisplay = computed(() => {
  return useDateFormattedWithLocale(props.item.created_at, "DD MMM YYYY");
});

const canCancel = computed(() =>
  statusesToValues[WithdrawalRequestsStatuses.IN_PROGRESS].includes(
    props.item.status
  )
);

const onCancel = () => {
  emit("cancel", props.item);
};
</script>

<template>
  <div class="withdrawal-request-item bg-bg-level-1 p-3 rounded-xl">
    <div class="flex flex-col gap-3">
      <div class="flex gap-3">
        <div class="flex-1">
          <div class="amount-label label">
            {{ $t("withdrawal.withdrawal-sidebar.item.amount-label") }}
          </div>
          <div class="value">{{ item.amount }} USDT</div>
        </div>
        <div class="flex-1">
          <div class="date-label label">
            {{ $t("withdrawal.withdrawal-sidebar.item.date-label") }}
          </div>
          <div class="value">
            {{ dateDisplay.value }}
          </div>
        </div>
      </div>
      <div>
        <div class="status-label label">
          {{ $t("withdrawal.withdrawal-sidebar.item.recipient-address-label") }}
        </div>
        <div class="text-4 leading-5 font-normal">
          {{ item.wallet }}
        </div>
      </div>
      <div
        v-if="item.tx_id"
        class="truncate">
        <div class="status-label label">
          {{ $t("withdrawal.withdrawal-sidebar.item.hash-label") }}
        </div>
        <a
          :href="`https://tronscan.org/#/transaction/${item.tx_id}`"
          class="text-4 flex items-center underline leading-5 text-fg-blue font-normal">
          <span class="truncate">
            {{ item.tx_id }}
          </span>
          <DynamicIcon
            name="send-outline"
            class="text-fg-secondary flex-shrink-0" />
        </a>
      </div>
      <template v-if="!shortMode">
        <div>
          <div class="status-label label">
            {{ $t("withdrawal.withdrawal-sidebar.item.status-label") }}
          </div>
          <div
            class="text-4 leading-5 font-normal"
            :class="statusColor">
            {{ statusName }}
          </div>
        </div>
        <div v-if="item.reason">
          <div class="reason-label label">
            {{ $t("withdrawal.withdrawal-sidebar.item.reason-label") }}
          </div>
          <div class="value">
            {{ item.reason }}
          </div>
        </div>
        <div v-if="canCancel">
          <UIButton
            size="m"
            class="w-full mt-4 bg-bg-level-2"
            @click="onCancel">
            {{ $t("Cancel") }}
          </UIButton>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.label {
  @apply text-fg-secondary text-4 leading-5 font-normal;
}

.value {
  @apply text-fg-primary text-4 leading-5 font-normal;
}
</style>
