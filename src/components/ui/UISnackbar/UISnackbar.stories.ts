import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import UISnackbar from "@/components/ui/UISnackbar/UISnackbar.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import type { ComponentProps } from "vue-component-type-helpers";
import { action } from "@storybook/addon-actions";

const meta: Meta<
  ComponentProps<typeof UISnackbar> & {
    content?: string;
  }
> = {
  title: "UI/UISnackbar",
  tags: ["autodocs"],
  component: UISnackbar,
  render: (args: any) => ({
    components: { UISnackbar, UIButton },
    setup() {
      const handleClick = action("@close");
      const buttonClick = action("slot button click");
      return { args, handleClick, buttonClick };
    },
    template: `
      <div>
      <UISnackbar v-bind="args" @close="handleClick">
        {{args.content }}
        <template #buttons>
          <UIButton color="white" size="xs" class="text-fg-primary" @click="buttonClick">Button</UIButton>
        </template>
      </UISnackbar>
      </div>
    `,
  }),
  argTypes: {
    closeable: {
      control: "boolean",
    },
    container: { control: "boolean" },
    variant: {
      control: "inline-radio",
      options: [
        "error",
        "warning",
        "info",
        "red-solid",
        "yellow-solid",
        "contrast",
        "purple",
      ],
    },
  },
  args: {
    variant: "info",
    closeable: true,
    container: true,
    content: "I'm UISnackbar!",
  },
};

export default meta;

type Story = StoryObj<typeof UISnackbar>;
/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/vue/api/csf
 * to learn how to use render functions.
 */
export const Default: Story = {};
