import type { RouteLocationRaw } from "vue-router";

export interface INavigationItem {
  id: number;
  to: RouteLocationRaw;
  icon: string;
  label: string;
  count?: number;
  event?: string;
  basePath?: "/app" | "/team";
  isHaveActiveRequests?: boolean;
  notification?: boolean;
}

export enum NavigationHeading {
  FINANCE = "finance",
  TEAM = "team",
}

export interface PropsNavigation {
  withLabels?: boolean;
  navigationMasterMenuTitles?: boolean;
}
