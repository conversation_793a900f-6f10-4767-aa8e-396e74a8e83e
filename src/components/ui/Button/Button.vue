<template>
  <button
    v-ripple
    :class="{
      [$style.root]: true,
      [$style.padding]: !padding,
      [String(padding)]: !!padding,
      [$style['type-' + type]]: true,
      [$style['size-' + size]]: true,
    }"
    :disabled="loading || disabled"
    title="">
    <DynamicIcon
      v-if="icon && !loading"
      :name="icon"
      :data-testId="uiTestIds.buttonIcon + `-${icon}`"
      :class="[
        $style['icon-size-' + size],
        $style['icon-position-' + iconPosition || 'left'],
      ]" />

    <Loader
      v-if="loading"
      :data-testId="'testIdIcon'"
      :padding="false"
      width="w-6" />

    <slot v-else>
      <span
        v-if="title"
        :data-testId="uiTestIds.buttonTitle"
        :class="titleClass">
        {{ title }}
      </span>
    </slot>
  </button>
</template>

<script lang="ts" setup>
import Loader from "@/components/ui/Loader/Loader.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

import { uiTestIds } from "@/config/specs";
import type { IButtonProps } from "./types";

withDefaults(defineProps<IButtonProps>(), {
  type: "primary",
  size: "medium",
});
</script>

<style lang="scss" module>
.root {
  @apply font-extrabold flex outline-none;
}

.padding {
  @apply px-8 gap-1;
}

.type {
  &-kyc {
    @apply bg-neutral-900 text-white;

    &:hover {
      @apply bg-opacity-80;
    }

    &:disabled {
      @apply bg-opacity-40;
    }
  }

  &-primary {
    @apply text-white bg-secondary-base;

    &:hover {
      @apply bg-[#2F3A50];
    }

    &:disabled {
      @apply bg-secondary-300;
    }
  }

  &-quaternary {
    @apply text-white;
    background: rgba(255, 255, 255, 0.15);

    &:hover {
      @apply bg-[#2F3A50];
    }

    &:disabled {
      @apply bg-secondary-300;
    }
  }

  &-gray {
    @apply text-gray-900 bg-neutral-200;

    &:hover {
      @apply bg-neutral-300;
    }

    &:disabled {
      @apply bg-secondary-300;
    }
  }

  &-gray-outline {
    @apply text-gray-900 border-neutral-200 border;

    &:hover {
      @apply border-neutral-400;
    }

    &:disabled {
      @apply opacity-50;
    }
  }

  &-secondary {
    @apply text-secondary-base border border-secondary-base;

    &:hover {
      @apply text-[#2F3A50] border-[#2F3A50];
    }

    &:disabled {
      @apply text-secondary-300 border-secondary-300;
    }
  }

  &-approve {
    @apply text-success-base border border-success-base;
    background-color: rgba(34, 197, 94, 0.12);

    &:disabled {
      @apply opacity-50;
    }
  }

  &-tertiary {
    @apply bg-gray-50 text-gray-600;

    &:disabled {
      @apply opacity-50;
    }
  }

  &-orange {
    @apply bg-accent-primary text-white;

    &:hover {
      @apply opacity-80;
    }
    &:disabled {
      @apply opacity-50;
    }
  }

  &-white {
    @apply bg-bg-level-0 text-fg-primary;
  }

  &-reject {
    @apply text-error-base;
    background: rgba(255, 71, 71, 0.08);

    &:disabled {
      @apply opacity-50;
    }
  }

  &-social {
    @apply p-4 text-base font-medium border border-gray-300;
    @apply rounded-base gap-2;

    & svg {
      @apply h-6 w-6 min-w-[1.5rem];
    }
  }

  &-blue {
    @apply text-white;
    background: linear-gradient(0deg, #0088cc, #0088cc), #15191d;

    &:hover {
      @apply opacity-80;
    }

    &:disabled {
      @apply opacity-50;
    }
  }

  &-transparent {
    @apply bg-transparent text-neutral-900;

    &:disabled {
      @apply opacity-50;
    }
  }

  &-light {
    @apply text-neutral-900 bg-neutral-150;

    &:hover {
      @apply bg-neutral-200;
    }

    &:disabled {
      @apply opacity-30 pointer-events-none;
    }
  }
}

.size {
  &-medium {
    @apply text-base py-[0.875rem] rounded-base;
  }

  &-large {
    @apply text-lg py-4 rounded-base;
  }

  &-small {
    @apply text-sm py-2 rounded-sm;
  }

  &-xs {
    @apply text-sm rounded-sm px-1;
  }

  &-normal {
    @apply text-[15px] font-normal px-3 py-2.5 rounded-base gap-3;
  }
}

.icon-position {
  &-left {
    @apply order-none;
  }

  &-right {
    @apply order-1;
  }
}
.icon-size {
  &-medium {
    @apply w-5;
  }

  &-large {
    @apply w-6;
  }

  &-small {
    @apply w-4;
  }

  &-xs {
    @apply w-3;
  }

  &-normal {
    @apply w-6 h-6;
  }
}
</style>
