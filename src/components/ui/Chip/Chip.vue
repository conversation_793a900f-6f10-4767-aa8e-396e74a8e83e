<template>
  <div
    :class="[
      $style.UiChip,
      getRootClass(state),
      inline ? $style.UiChip__inline : '',
    ]">
    <span
      :class="{
        [$style.UiChip__text]: true,
        [getTextClass(state)]: true,
      }">
      <slot>
        {{ $t(`transactions-state-${state}`) }}
      </slot>
    </span>
  </div>
</template>

<script>
const states = {
  success: {
    rootClass: "UiChip__success",
    textClass: "UiChip__success__text",
  },
  warn: {
    rootClass: "UiChip__warn",
    textClass: "UiChip__warn__text",
  },
  failed: {
    rootClass: "UiChip__danger",
    textClass: "UiChip__danger__text",
  },
  warnSecondary: {
    rootClass: "UiChip__warnSecondary",
    textClass: "UiChip__warnSecondary__text",
  },
  refund: {
    rootClass: "UiChip__refund",
    textClass: "UiChip__refund__text",
  },
};

export default {
  name: "UiChip",
  props: {
    state: {
      type: String,
      default: "success",
    },
    inline: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    getRootClass(state) {
      return this.$style[states[state]?.rootClass] || "";
    },
    getTextClass(state) {
      return this.$style[states[state]?.textClass] || "";
    },
  },
};
</script>

<style module lang="scss">
.UiChip {
  @apply w-full p-2 rounded-sm flex items-center justify-center;

  &__inline {
    @apply w-auto inline-flex;
  }

  &__text {
    @apply text-base font-medium;
  }

  // states
  &__warnSecondary {
    background-color: rgba(250, 204, 21, 0.12);

    &__text {
      @apply text-warning-base;
    }
  }
  &__success {
    background-color: #f6fdf9;

    &__text {
      @apply text-success-base;
    }
  }
  &__warn {
    background-color: #fff7f5;

    &__text {
      @apply text-orange;
    }
  }
  &__danger {
    background-color: #fff5f5;

    &__text {
      @apply text-error-base;
    }
  }
  &__refund {
    @apply bg-greyscale-100;

    &__text {
      @apply text-greyscale-500;
    }
  }
}
</style>
