<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
interface Props {
  title: string;
  border?: boolean;
  subtitle?: string | undefined;
}
defineEmits(["close"]);
const props = withDefaults(defineProps<Props>(), {
  border: true,
});
</script>
<template>
  <div :class="$style.root">
    <div
      class="flex flex-col"
      :class="[$style.header, !props.border ? 'border-none' : null]">
      <h2 :class="$style.title">{{ props.title }}</h2>
      <div class="w-full">
        <h3
          v-if="props.subtitle"
          :class="$style.subtitle">
          {{ props.subtitle }}
        </h3>
      </div>
      <div :class="$style.close_icon_wrap">
        <DynamicIcon
          name="close"
          :class="$style.close_icon"
          @click="$emit('close')" />
      </div>
    </div>
    <div :class="$style.body">
      <slot name="content"></slot>
    </div>
  </div>
</template>
<style module lang="scss">
.root {
  @apply bg-white fixed z-7 w-full h-full top-0 left-0 font-granate;
  .header {
    @apply flex items-center justify-center 
    border-2 xl:border-[3px] border-b-neutral-900 relative px-4 h-20 xl:h-24;
    .title {
      @apply font-semibold text-2xl xl:text-3xl;
    }
    .subtitle {
      @apply text-fg-primary text-center text-lg not-italic font-normal leading-5;
    }
    .close_icon_wrap {
      @apply absolute top-0 right-0 h-full cursor-pointer flex items-center justify-center pr-4;
      .close_icon {
        @apply w-6 xl:w-8 h-auto;
      }
    }
  }
  .body {
    @apply px-4 mt-10 flex items-start justify-center;
  }
}
</style>
