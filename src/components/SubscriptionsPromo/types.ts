export type TNavigationKey = "features" | "pricing" | "cards" | "faq";

export type TNavigationTab = {
  label: string;
  value: TNavigationKey;
};

export type TTariffsKey = "xs-s" | "m-l";

export type TTariffsTab = {
  label: string;
  value: TTariffsKey;
};

type TQuestion = {
  question: string;
  answer: string;
};

export type TQustionsGroup = {
  id: number;
  title: string;
  questions: TQuestion[];
};
