import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import type { ComponentProps } from "vue-component-type-helpers";
import { ref } from "vue";

const meta: Meta<ComponentProps<typeof UIFullScreenModal>> = {
  title: "UI/UIFullScreenModal",
  component: UIFullScreenModal,
  tags: ["autodocs"],
  argTypes: { onClose: { action: "Close modal" } },
};

export default meta;
type Story = StoryObj<ComponentProps<typeof UIFullScreenModal>>;

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Default: Story = {
  render: (args) => ({
    components: { UIFullScreenModal, UIButton, DynamicIcon },
    setup() {
      const isOpen = ref(false);
      return { args, isOpen };
    },
    template: `
      <UIButton @click="isOpen = true">Open modal</UIButton>
      <UIFullScreenModal v-bind="args" :is-open="isOpen" @close="isOpen=false">
        <template #rightIcon>
          <UIButton icon-only color="white"><DynamicIcon name="headphone" class="text-fg-blue"/></UIButton>
        </template>
        
        <template #content>
          <div>
            <ul class="">
            <li v-for="item in 100" class="pb-1">Item number {{item}}</li>
          </ul>
          </div>
          
        </template>
      </UIFullScreenModal>
    `,
  }),
  args: {
    title: "Title",
  },
};
