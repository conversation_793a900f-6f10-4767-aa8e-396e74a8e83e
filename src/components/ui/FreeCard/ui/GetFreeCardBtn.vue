<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import ButtonBase from "@/components/ui/Button/ButtonBase.vue";
</script>
<template>
  <div>
    <DynamicIcon
      name="free-card"
      path="cards" />
    <ButtonBase
      v-track:button="{
        page_version: 'free_card-1',
        name: 'Pick up',
        version: 'free_card-1',
        location: 'free_card_before',
      }"
      :title="$t('freeCard.getBtn')"
      class="w-full mt-2"></ButtonBase>
  </div>
</template>
