<script lang="ts" setup>
import Modal from "@/components/ui/Modal/Modal.vue";
import { useCountries } from "@/composable";

const { blockedCountriesCodes } = useCountries();

const emit = defineEmits<{
  close: [];
}>();
const onClose = () => {
  emit("close");
};
</script>

<template>
  <Modal
    scrolled
    @close="onClose">
    <template #title>
      {{ $t("register.agreementCountry.popupTitle") }}
    </template>

    <ul
      class="grid grid-cols-2 md:grid-cols-3 gap-x-5 gap-y-2 text-neutral-800 font-medium text-base">
      <li
        v-for="countryCode in blockedCountriesCodes"
        :key="countryCode">
        {{ $t(`unavailable-country-list.${countryCode}`) }}
      </li>
    </ul>
  </Modal>
</template>
