import { usePstFetch } from "@/composable/usePstFetch";
import type { UseFetchOptions } from "@vueuse/core";

export type TWithdrawalPatchReq = {
  decline_reason: string;
};

export const useWithdrawalPatch = (
  requestId: number,
  body: TWithdrawalPatchReq,
  options: UseFetchOptions = {}
) => {
  return usePstFetch(`withdrawal/${requestId}/decline`, options)
    .patch(body)
    .json<{ success?: boolean; message?: string; errors?: object }>();
};
