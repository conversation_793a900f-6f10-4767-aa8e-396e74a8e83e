/* eslint-disable max-len */
import type {
  IExchang<PERSON><PERSON><PERSON>,
  TExchangeResult,
  TExchangeStatus,
  TRateResult,
} from "@/helpers/exchange/types";
import { EExchangeCurrency } from "@/helpers/exchange/types";
import axios from "axios";

const getCurrency = (v: EExchangeCurrency): string => {
  switch (v) {
    case EExchangeCurrency.ADA:
      return "ada";
    case EExchangeCurrency.BNBBSC:
      return "bnb";
    case EExchangeCurrency.BUSDBSC:
      return "busd";
    case EExchangeCurrency.DASH:
      return "dash";
    case EExchangeCurrency.DOGE:
      return "doge";
    case EExchangeCurrency.ETH:
      return "eth";
    case EExchangeCurrency.LTC:
      return "ltc";
    case EExchangeCurrency.MATIC:
      return "matic";
    case EExchangeCurrency.SOL:
      return "sol";
    case EExchangeCurrency.USDCERC20:
      return "usdc";
    case EExchangeCurrency.USDCTRC20:
      return "usdc";
    case EExchangeCurrency.XRP:
      return "xrp";
    case EExchangeCurrency.USDTERC20:
      return "usdt";
    case EExchangeCurrency.USDTTRC20:
      return "usdt";
    case EExchangeCurrency.USDTTON:
      return "usdt";
    case EExchangeCurrency.BITCOINCASH:
      return "bch";
    case EExchangeCurrency.TRX:
      return "trx";
  }
  return "";
};
const getNetwork = (v: EExchangeCurrency): string => {
  switch (v) {
    case EExchangeCurrency.ADA:
      return "ada";
    case EExchangeCurrency.BNBBSC:
      return "bsc";
    case EExchangeCurrency.BUSDBSC:
      return "bsc";
    case EExchangeCurrency.DASH:
      return "dash";
    case EExchangeCurrency.DOGE:
      return "doge";
    case EExchangeCurrency.ETH:
      return "eth";
    case EExchangeCurrency.LTC:
      return "ltc";
    case EExchangeCurrency.MATIC:
      return "matic";
    case EExchangeCurrency.SOL:
      return "sol";
    case EExchangeCurrency.USDCERC20:
      return "eth";
    case EExchangeCurrency.XRP:
      return "xrp";
    case EExchangeCurrency.USDTERC20:
      return "eth";
    case EExchangeCurrency.USDTTRC20:
      return "trx";
    case EExchangeCurrency.USDCTRC20:
      return "trx";
    case EExchangeCurrency.USDTTON:
      return "ton";
    case EExchangeCurrency.BITCOINCASH:
      return "bch";
    case EExchangeCurrency.TRX:
      return "trx";
  }
  return "";
};

/**
 * Class encapsulates exchange flow.
 * Used only for deposit in DepositByAnyCrypto component.
 * @TODO this is not helper, move
 *
 * @category Helpers
 */
export class DefaultExchangeFlow implements IExchangeFlow {
  private fromCurrency: string | undefined;
  private toCurrency: string | undefined;
  private fromNetwork: string | undefined;
  private toNetwork: string | undefined;
  private fromSum: string | undefined;
  private rateId: string | undefined;
  private address: string | undefined;
  private id: string | undefined;

  setFrom(from: EExchangeCurrency) {
    this.fromCurrency = getCurrency(from);
    this.fromNetwork = getNetwork(from);
  }
  setTo(to: EExchangeCurrency, address: string) {
    this.toCurrency = getCurrency(to);
    this.toNetwork = getNetwork(to);
    this.address = address;
  }

  getExchangeRate(
    fromSum: string | undefined,
    toSum: string | undefined
  ): Promise<TRateResult> {
    return new Promise<TRateResult>((resolve, reject) => {
      const urlQuery = {
        fromCurrency: this.fromCurrency || "",
        toCurrency: this.toCurrency || "",
        fromAmount: fromSum || "",
        toAmount: toSum || "",
        fromNetwork: this.fromNetwork || "",
        toNetwork: this.toNetwork || "",
        flow: "fixed-rate",
        type: fromSum !== undefined ? "direct" : "reverse",
      };
      axios
        .get(
          `${this.baseUrl}estimated-amount?${new URLSearchParams(
            urlQuery
          ).toString()}`
        )
        .then((result) => {
          this.fromSum = String(result.data.fromAmount);
          this.rateId = result.data.rateId;
          resolve({
            fromAmount: String(result.data.fromAmount),
            toAmount: String(result.data.toAmount),
            rateId: result.data.rateId,
          });
        })
        .catch((err) => {
          reject({
            error: err.response.data.error,
            message: err.response.data.message,
          });
          console.error(
            `getExchangeRateError->ExchangeDefaultProvider->src/helpers/exchange/providers/default.ts -> Error: ${err.response.data.error}. Message: ${err.response.data.message}`
          );
        });
    });
  }

  createExchange(): Promise<TExchangeResult> {
    return new Promise<TExchangeResult>((resolve, reject) => {
      const query = {
        fromCurrency: this.fromCurrency || "",
        toCurrency: this.toCurrency || "",
        fromAmount: this.fromSum || "",
        fromNetwork: this.fromNetwork || "",
        toNetwork: this.toNetwork || "",
        flow: "fixed-rate",
        rateId: this.rateId || "",
        address: this.address || "",
      };
      axios
        .post(`${this.baseUrl}`, query)
        .then((result) => {
          this.id = result.data.id;
          resolve({
            payinAddress: result.data.payinAddress,
            id: result.data.id,
            payinExtraIdName: result.data.payinExtraIdName,
            payinExtraId: result.data.payinExtraId,
            fromNetwork: result.data.fromNetwork,
          });
        })
        .catch(reject);
    });
  }

  getStatus(): Promise<TExchangeStatus> {
    return new Promise<TExchangeStatus>((resolve, reject) => {
      axios
        .get(`${this.baseUrl}by-id?id=${this.id}`)
        .then((result) => {
          resolve({
            status: result.data.status,
          });
        })
        .catch(reject);
    });
  }
  postNotify(body: {
    email: string;
    sum: string;
    from: string;
    exchangeId: string;
  }): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      axios
        .post(`https://exchange.pst.net/new`, body)
        .then((result) => {
          resolve({
            data: result.data,
          });
        })
        .catch(reject);
    });
  }

  // Use exchange ID as 'Mark'
  getMark(): string | undefined {
    return this.id;
  }

  private baseUrl: string = "https://exchange.pst.net/1/";
}
