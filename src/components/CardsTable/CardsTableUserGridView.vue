<script lang="ts" setup>
import { Skeletor } from "vue-skeletor";
import UIPagination from "@/components/ui/UIPagination/UIPagination.vue";
import CardsTableNotFoundCards from "@/components/CardsTable/CardsTableNotFoundCards.vue";
import type { TCardResource } from "@/types/api/TCardResource";
import type { TResponseMeta } from "@/types/api/TResponseMeta";
import type { TUISelectOption } from "../ui/UISelect/types";
import UISelect from "@/components/ui/UISelect/UISelect.vue";
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import CardPreview from "@/components/CardPreview/CardPreview.vue";
import UITransition from "../ui/UITransition.vue";
import { useCardsTableState } from "@/components/CardsTable/useCardsTableState";
import type { SubscriptionStatusCode } from "@/constants/subscription_status_code";
import { isCardBinDeprecated } from "@/helpers/isCardBinDeprecated";
import { groupBy } from "lodash";
import { useUserStore } from "@/stores/user";

const props = defineProps<{
  cards: { data: TCardResource[] | null; meta: TResponseMeta } | null;
  isLoading: boolean;
  perPage: number;
  subscriptionStatus?: SubscriptionStatusCode;
  isGrouped?: boolean;
}>();

const emit = defineEmits<{
  setPage: [page: number];
  clearFilters: [];
  setFavorite: [state: boolean, id: number];
  setPerPage: [value: number];
}>();

const { setModalState, setSelectedCard } = useCardsTableState();

const { t } = useI18n();

const perPageValue = ref(props.perPage ?? "24");
const { userMasterId } = useUserStore();

const perPageOptions = computed<
  TUISelectOption<{ label: string; value: number }>[]
>(() => {
  return [
    {
      label: `${t("cards.pagination.perPage.by")} 12`,
      value: 12,
    },
    {
      label: `${t("cards.pagination.perPage.by")} 24`,
      value: 24,
    },
    {
      label: `${t("cards.pagination.perPage.by")} 48`,
      value: 48,
    },
  ];
});

watch(perPageValue, (newVal) => {
  emit("setPerPage", Number(newVal));
  changePageHandle(1);
});

const changePageHandle = (page: number) => {
  emit("setPage", page);
};

const openFullDetailCard = (card: TCardResource) => {
  setSelectedCard(card);
  setModalState(true, "cardDetailFull");
};

const createCardGroupKey = (card: TCardResource) => {
  if (card.user_id === userMasterId) {
    return "Master (You)";
  }

  if (!card.user_name) {
    return card.user_email;
  }

  return `${card.user_name} (${card.user_email})`;
};

const groupedCards = computed<{ [key: string]: TCardResource[] } | null>(() => {
  if (!props.cards || !props.cards.data) {
    return null;
  }

  return groupBy(props.cards.data, createCardGroupKey);
});
</script>
<template>
  <div>
    <UITransition mode="out-in">
      <div
        v-if="props.isLoading"
        class="cards-grid">
        <Skeletor
          v-for="(_, idx) in 8"
          :key="idx"
          as="div"
          class="rounded"
          height="164"
          width="100%"></Skeletor>
      </div>

      <div
        v-else-if="props.cards?.data?.length! > 0 && props.cards?.data"
        class="flex flex-col gap-5">
        <template v-if="isGrouped">
          <div
            v-for="(items, key) in groupedCards"
            :key="key">
            <p class="whitespace-nowrap h-8 text-5 leading-6 mb-1.5">
              {{ key }}
            </p>
            <div class="cards-grid">
              <CardPreview
                v-for="item in items"
                :key="item.id"
                :card="item"
                :is-card-bin-deprecated="isCardBinDeprecated(item)"
                :subscription-status="props.subscriptionStatus"
                is-clickable
                use-deprecated-color
                @click="openFullDetailCard(item)" />
            </div>
          </div>
        </template>
        <template v-else>
          <div class="cards-grid">
            <CardPreview
              v-for="item in cards?.data"
              :key="item.id"
              :card="item"
              :is-card-bin-deprecated="isCardBinDeprecated(item)"
              :subscription-status="props.subscriptionStatus"
              is-clickable
              use-deprecated-color
              @click="openFullDetailCard(item)" />
          </div>
        </template>
      </div>

      <CardsTableNotFoundCards
        v-else
        @clear-filters="$emit('clearFilters')" />
    </UITransition>

    <!--    Pagination -->
    <div
      v-if="props.cards?.data?.length! > 0"
      class="py-3 px-4 bg-bg-level-1 mt-2 rounded">
      <div
        class="flex items-center justify-between px-2 md:flex-row flex-col gap-2">
        <div class="flex gap-4 items-center">
          <div class="text-4">
            {{ props.cards?.meta?.from }} —
            {{ props.cards?.meta?.to }}
            {{ $t("UiTable.pagination.of") }}
            {{ props.cards?.meta.total }}
          </div>

          <UISelect
            v-if="props.cards?.meta && props.cards?.meta?.total > 12"
            v-model="perPageValue"
            :cleared="false"
            :options="perPageOptions"
            :placeholder="'By 12'"
            class="w-[5.5rem]" />
        </div>

        <UIPagination
          v-if="props.cards?.meta?.total! > props.cards?.meta?.per_page!"
          :count="props.cards?.meta.last_page!"
          :loading="props.isLoading"
          :page="props.cards?.meta.current_page!"
          @change="changePageHandle" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.cards-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full gap-2;
}
</style>
