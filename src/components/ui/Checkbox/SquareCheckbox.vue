<template>
  <div :class="$style.root">
    <input
      :id="id"
      type="checkbox"
      :checked="checked"
      hidden
      :disabled="disabled"
      @change="onChangeCustom" />

    <div
      tabindex="0"
      :class="[$style.checkbox, customClass]"
      @keydown.space="onChangeCustom"
      @click="onChangeCustom">
      <DynamicIcon
        v-show="checked"
        class="text-white"
        name="check" />
    </div>

    <label :for="id">
      <div
        v-if="label || $slots.label"
        :class="[
          isNewDesign ? $style['new-label'] : $style.label,
          props.error ? $style.error : '',
        ]">
        <template v-if="label">
          {{ label }}
        </template>
        <template v-if="$slots.label">
          <slot name="label" />
        </template>
      </div>
    </label>
  </div>
</template>

<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  checked: boolean;
  label?: string;
  customClass?: string;
  disabled?: boolean;
  error?: string;
  isNewDesign?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(["change"]);

const id = String(new Date().getTime());

const onChangeCustom = () => !props.disabled && emit("change", !props.checked);
</script>

<style lang="scss" module>
.root {
  @apply flex items-start gap-3 cursor-pointer;

  &:hover {
    & .checkbox {
      @apply border-gray-500;
    }
  }

  & input:checked {
    & ~ .checkbox {
      @apply bg-secondary-base border-none;
    }
  }
}

.checkbox {
  @apply min-w-[20px] w-5 h-5 rounded-[4px] border border-gray-300 translate-y-[4px];
}

.new-label {
  @apply text-[color:var(--neutral-n-700,#4c4f53)] text-[15px] not-italic font-semi-medium leading-6;
  font-family: "ALS Granate VF";
}

.new-label a {
  @apply text-[color:var(--neutral-n-900,#15191d)] cursor-pointer text-[15px] not-italic font-semibold leading-6;
  font-family: "ALS Granate VF";
}

.label {
  @apply text-gray-700 text-lg font-semibold cursor-pointer;
  a {
    @apply text-neutral-900;
  }
}

.label.error {
  @apply text-red-500 text-lg font-semibold cursor-pointer;
  a {
    @apply text-red-500;
  }
}
</style>
