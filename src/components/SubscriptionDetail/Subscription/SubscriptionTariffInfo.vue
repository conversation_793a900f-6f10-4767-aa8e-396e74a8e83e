<script setup lang="ts">
import { computed, ref } from "vue";
import { useSubscriptionsModalFlow } from "@/components/Subscriptions/useSubscriptionsModalFlow";
import UILayoutColumns from "@/components/ui/UILayoutColumns/UILayoutColumns.vue";
import { useRouter } from "vue-router";
import { maxBy } from "lodash";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useI18n } from "vue-i18n";
import { currencyFormatter } from "@/helpers/currencyFormatter";
import { useDateFormattedWithLocale } from "@/composable/useDateFormattedWithLocale";
import SubscriptionLayoutLoader from "./SubscriptionLayoutLoader.vue";
import { useAccountGet } from "@/composable/API/useAccountGet";
import { useUserExchangeRatesGet } from "@/composable/API/useUserExchangeRatesGet";
import { getExchangeBalance } from "@/helpers/getExchangeBalance";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import SubscriptionManageModal from "./SubscriptionManageModal.vue";
import type { TAccountsCurrencyId } from "@/types/api/TUserAccountResource";
import SubscriptionDetailModal from "./SubscriptionDetailModal.vue";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useSubscriptionsList } from "@/composable/useSubscriptionsList";
import { LARGE_TARIFFS } from "@/constants/large_tariffs";

const { t } = useI18n();
const router = useRouter();

const { setModalState } = useSubscriptionsModalFlow();

const { subscriptionsInfo, isFetching: isFetchingInfo } =
  useSubscriptionsInfo();
const { subscriptionsList, isFetching: isFetchingList } =
  useSubscriptionsList();

const { data: accounts } = useAccountGet();
const { data: rates } = await useUserExchangeRatesGet();

const getAccountWithMaxUsdBalance = () => {
  const mapAccounts = accounts.value?.data?.map((item) => {
    const { isoCode } = getAccountCurrencyByCurrencyId(
      item.currency_id as TAccountsCurrencyId
    );

    const { exchangeBalance } = getExchangeBalance(item.balance, {
      inputIsoCode: isoCode!,
      outputIsoCode: "USD",
      rates: rates.value?.data!,
    });

    return {
      currency_id: item.currency_id,
      balance: exchangeBalance,
    };
  });

  const acc = maxBy(mapAccounts, "balance");
  const { isoCode } = getAccountCurrencyByCurrencyId(
    acc?.currency_id as TAccountsCurrencyId
  );

  return isoCode;
};

const currentMainTariff = computed(() => {
  return subscriptionsList.value?.find(
    (s) => s.subscription_tariff.type_name === "MAIN"
  );
});

const tariffSlug = computed(() => {
  return currentMainTariff.value?.subscription_tariff?.slug.toLowerCase() ?? "";
});

const improveTariff = () => {
  if (LARGE_TARIFFS.includes(tariffSlug?.value)) {
    setModalState(true, "subscriptionUpLimitModal");
  } else {
    router.push({ name: "subscription.tariffs" });
  }
};

const orderedNextDate = useDateFormattedWithLocale(
  subscriptionsInfo.value?.ordered_next!,
  "DD MMMM YYYY"
);

const items = computed(() => {
  return [
    {
      id: 1,
      title: t("Current tariff"),
      subtitle: currentMainTariff.value?.subscription_tariff?.name?.replace(
        /_[AB]$/,
        ""
      ),
      btn: t("pst-private.modal.manage.header.title"),
    },
    {
      id: 2,
      title: t("Next write-off date"),
      subtitle: orderedNextDate.value,
    },
    {
      id: 3,
      title: t("pst-private.subscription.info.title"),
      subtitle: `${getAccountWithMaxUsdBalance()} account`,
    },
    {
      id: 4,
      title: t("Cost per month"),
      subtitle: currencyFormatter(
        subscriptionsInfo.value?.amount!,
        "USD",
        true
      ),
      btn: t("pst-private.modal.details.header.title"),
    },
  ];
});
const isOpenSubscriptionDetailModal = ref(false);
const isOpenSubscriptionManageModal = ref(false);

const clickItemBtnHandle = (id: number) => {
  switch (id) {
    case 4:
      isOpenSubscriptionDetailModal.value = true;
      break;
    case 1:
      isOpenSubscriptionManageModal.value = true;
      break;
  }
};
</script>
<template>
  <div v-if="!isFetchingInfo || !isFetchingList">
    <div class="flex items-center justify-between">
      <h2 class="text-5 font-medium">
        {{ $t("pst-private.subscription.info.block.title") }}
      </h2>
      <UIButton
        color="orange-solid"
        size="s"
        @click="improveTariff"
        >{{ $t("pst-private.subscription.tariff.info.btn") }}</UIButton
      >
    </div>

    <UILayoutColumns
      :items="items"
      grid-cols-class="grid-cols-1 md:grid-cols-4"
      class="mt-2">
      <template #col="{ item }">
        <p class="text-3.5 text-fg-secondary">{{ item.title }}</p>
        <p class="text-fg-primary">
          {{ item.subtitle }}
        </p>
        <button
          v-if="item.btn"
          class="text-fg-blue text-3.5 mt-2"
          @click="clickItemBtnHandle(item.id)">
          {{ item.btn }}
        </button>
      </template>
    </UILayoutColumns>
  </div>

  <SubscriptionLayoutLoader v-else />

  <SubscriptionDetailModal
    v-if="isOpenSubscriptionDetailModal"
    @improve-tariff="improveTariff"
    @close="isOpenSubscriptionDetailModal = false" />

  <SubscriptionManageModal
    v-if="isOpenSubscriptionManageModal"
    @improve-tariff="improveTariff"
    @close="isOpenSubscriptionManageModal = false" />
</template>
