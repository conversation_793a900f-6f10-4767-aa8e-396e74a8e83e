import type { TGetAllCard } from "@/types/api/TGetAllCard";
import { useGetRequest } from "@/composable/useGetRequest";
import type { UseFetchOptions } from "@vueuse/core";

export type UseCardsGetAllCardsGetReq = { is_only_master?: 0 | 1 };

export const useCardsGetAllCardsGet = (
  reqParams?: UseCardsGetAllCardsGetReq,
  useFetchOptions: UseFetchOptions = {}
) => {
  return useGetRequest<{ data: TGetAllCard[] }, UseCardsGetAllCardsGetReq>(
    "card/get-all-cards",
    reqParams,
    useFetchOptions
  );
};
