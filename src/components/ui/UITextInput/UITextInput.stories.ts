import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/vue3";
import { action } from "@storybook/addon-actions";
import { ref } from "vue";
import UITextInput from "@/components/ui/UITextInput/UITextInput.vue";
import UIPasswordInput from "@/components/ui/UIPasswordInput/UIPasswordInput.vue";
import UISearchInput from "@/components/ui/UISearchInput/UISearchInput.vue";
import BitcoinSign from "@/assets/svg/icon/bitcoin-sign.svg";
import type { UITextInputProps } from "@/components/ui/UITextInput/types";
import type { ComponentProps } from "vue-component-type-helpers";

const meta: Meta<ComponentProps<UITextInputProps>> = {
  title: "UI/UITextInput",
  component: UITextInput,
  tags: ["autodocs"],
  argTypes: {
    modelModifiers: {
      control: false,
    },
    error: {
      control: { type: "text" },
    },
    size: {
      control: { type: "radio" },
      options: ["s", "m", "l"],
    },
    disabled: {
      control: { type: "boolean" },
    },
    readonly: {
      control: { type: "boolean" },
    },
    prefix: {
      control: { type: "text" },
    },
    postfix: {
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<UITextInputProps>;

export const Default: Story = {
  args: {
    size: "m",
    placeholder: "Hello input",
    label: "Text input",
    helperText: "Lorem ipsum dolor sit amet.",
    disabled: false,
    readonly: false,
  },
  render: (args) => ({
    components: { UITextInput },
    setup() {
      const actionTextClickHandler = action("Click on action text");
      const inputValue = ref("Hello world");
      const inputLazyValue = ref("Hello lazy");
      return {
        args,
        inputValue,
        inputLazyValue,
        actionTextClickHandler,
      };
    },
    template: `
      <section class="flex flex-wrap gap-12 mb-12"> 
        <div>
          <UITextInput
            class="w-[250px]"
            v-bind="args"
            v-model="inputValue">
            <template #action>
              <button
                class="text-fg-blue hover:text-blue-500"
                @click="actionTextClickHandler">
                Action Slot
              </button>
            </template>
          </UITextInput>
          <div class="mt-1">input value: {{ inputValue }}</div>
        </div>
        <div>
          <UITextInput
            class="w-[250px]"
            v-bind="args"
            v-model.lazy="inputLazyValue"
            label="Text input lazy">
            <template #action>
              <button
                class="text-fg-blue hover:text-blue-500"
                @click="actionTextClickHandler">
                Action Slot
              </button>
            </template>
          </UITextInput>
          <div class="mt-1">input value lazy: {{ inputLazyValue }}</div>
        </div>
      </section>
    `,
  }),
};

export const InputType: Story = {
  render: () => ({
    components: { UITextInput, UIPasswordInput, UISearchInput },
    setup() {
      const inputValue = ref("Hello world");
      return { inputValue };
    },
    template: `
      <section class="flex flex-wrap gap-12">
        <UITextInput
          class="w-[250px]"
          v-model="inputValue"
          size="m"
          label="Text" />
        <UISearchInput
          class="w-[250px]"
          v-model="inputValue"
          size="m"
          label="Search"
          placeholder="Search" />
        <UIPasswordInput
          class="w-[250px]"
          v-model="inputValue"
          size="m"
          :max-length="255"
          label="Password"
          placeholder="Enter password" />
      </section>
    `,
  }),
};

export const WithIcon: Story = {
  // @ts-ignore
  render: () => ({
    components: { UITextInput, BitcoinSign },
    setup() {
      const inputValue = ref("Hello world");
      return { inputValue };
    },
    template: `
      <section class="flex flex-wrap gap-12">
        <UITextInput
          class="w-[250px]"
          v-model="inputValue"
          size="m">
          <template #leftIcon>
            <p class="text-fg-tertiary">BTC</p>
          </template>
          <template #rightIcon>
            <p class="text-fg-tertiary">BTC</p>
          </template>
        </UITextInput>
        <UITextInput
          class="w-[250px]"
          v-model="inputValue"
          size="m">
          <template #rightIcon>
            <BitcoinSign class="w-6 h-auto text-fg-tertiary"/>
          </template>
        </UITextInput>
      </section>
    `,
  }),
};

export const WithPrefixAndPostfix: Story = {
  // @ts-ignore
  render: () => ({
    components: { UITextInput, BitcoinSign },
    setup() {
      const inputValue = ref("Hello world");
      return { inputValue };
    },
    template: `
      <section class="flex flex-wrap gap-12">
        <UITextInput
          class="w-[250px]"
          v-model="inputValue"
          size="m"
          prefix="say:">
        </UITextInput>
        <UITextInput
          class="w-[250px]"
          v-model="inputValue"
          size="m"
          postfix="!!!" />
      </section>
    `,
  }),
};
