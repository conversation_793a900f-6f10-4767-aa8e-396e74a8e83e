import { useUserSecuritySettings } from "@/composable";
import { useUserInfoGet } from "@/composable/API/useUserInfoGet";
import { computed } from "vue";
import type { TwoFactorAuthState } from "@/components/TwoFactorAuth/types";

export const useUserTfaData = (immediate = true) => {
  // get user security settings for telegram and whatsapp contacts, commands and codes
  const { userSecuritySettings, fetchUserSecuritySettings } =
    useUserSecuritySettings({
      immediate,
    });

  // get user info for user provider connections
  const { data: userInfo, execute: fetchUserInfo } = useUserInfoGet({}, {}, 0);

  const enabledProvider = computed<string | null>(() => {
    if (userInfo.value?.data?.telegram_2fa) {
      return "telegram";
    } else if (userInfo.value?.data?.whatsapp_2fa) {
      return "whatsapp";
    } else {
      return null;
    }
  });

  const twoFactorAuthState = computed<TwoFactorAuthState>(() => {
    return {
      enabledProvider: enabledProvider.value,
      user_telegram: userInfo.value?.data?.telegram,
      user_telegram_2fa: userInfo.value?.data?.telegram_2fa,
      user_whatsapp: userInfo.value?.data?.whatsapp,
      user_whatsapp_2fa: userInfo.value?.data?.whatsapp_2fa,
      telegramProvider: userSecuritySettings.value?.telegram,
      whatsappProvider: userSecuritySettings.value?.whatsapp,
      registration_method: userInfo.value?.data?.registration_method,
    };
  });

  const refetch = async () => {
    await fetchUserInfo();
    await fetchUserSecuritySettings();
  };

  return {
    userSecuritySettings,
    fetchUserSecuritySettings,
    refetch,
    twoFactorAuthState,
  };
};
