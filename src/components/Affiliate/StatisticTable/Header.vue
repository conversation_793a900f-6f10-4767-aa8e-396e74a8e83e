<script setup lang="ts">
import TeamDashboardTableHeader from "@/components/ui/Table/TeamDashboardTableHeader.vue";
import type { TGroupVariant } from "@/components/Affiliate/types";

import { useI18n } from "vue-i18n";
import { computed } from "vue";
import type { THeader } from "@/components/ui/Table/types";

interface Props {
  template: string;
  group: TGroupVariant;
  hide?: boolean;
}

const { t } = useI18n();
const props = defineProps<Props>();

const headers = computed<Array<THeader>>(() =>
  [
    {
      withSort: true,
      justifyEnd: false,
      title: t(`affiliate.statisticTable.header.${props.group}`),
      code: "group",
    },
    {
      withSort: true,
      justifyEnd: true,
      title: t("affiliate.statisticTable.header.traffic"),
      code: "traffic",
    },
    {
      withSort: true,
      justifyEnd: true,
      title: t("affiliate.statisticTable.header.registrations"),
      code: "registration",
    },
    {
      withSort: true,
      justifyEnd: true,
      title: t("affiliate.statisticTable.header.cr"),
      code: "conversion",
    },
    {
      withSort: true,
      justifyEnd: true,
      title: t("affiliate.statisticTable.header.revenue"),
      code: "revenue",
    },
  ].filter(
    (item) =>
      (props.hide &&
        !["registration", "traffic", "conversion"].includes(item.code)) ||
      !props.hide
  )
);
</script>

<template>
  <TeamDashboardTableHeader
    :light-header="true"
    :headers="headers"
    :template="template" />
</template>

<style scoped lang="scss"></style>
