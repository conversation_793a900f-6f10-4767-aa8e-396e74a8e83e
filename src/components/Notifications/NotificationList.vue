<template>
  <div class="flex flex-col gap-5 py-3">
    <UnifyNotification
      v-for="data in items"
      :key="'notify' + data.notification.id"
      :type="String(data.notification.notify_type_id)"
      :data="data"
      @master-notification-click="$emit('master-notification-click')"
      @redirect="$emit('redirect')" />

    <Loader
      v-if="isLoading"
      :padding="false" />

    <div ref="lastItem" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";

import Loader from "@/components/ui/Loader/Loader.vue";
import UnifyNotification from "@/components/Notifications/NotifyTypes/UnifyNotification.vue";
import { useNotifications } from "@/stores/notifications";
import { storeToRefs } from "pinia";
import type { TUserNotificationResource } from "@/types/api/TUserNotificationResource";

const lastItem = ref<Element>(document.createElement("div"));
const notificationsStore = useNotifications();
const { isLoading } = storeToRefs(notificationsStore);

defineProps<{
  items: TUserNotificationResource[];
}>();

defineEmits<{
  (e: "master-notification-click"): void;
  (e: "redirect"): void;
}>();

onMounted(() => {
  try {
    const options = {
      root: document,
      rootMargin: "0px",
      threshold: 1.0,
    };
    const callback = async function (entries: any) {
      const isIntersecting = entries[0]?.isIntersecting;

      if (isIntersecting && !isLoading.value) {
        notificationsStore.incrementPage();
        await notificationsStore.getNotifications();
      }
    };

    const obs = new IntersectionObserver(callback, options);

    obs.observe(lastItem.value);
  } catch (ex) {
    console.warn("NotificationList->onMounted error handled: ", ex);
  }
});
</script>
