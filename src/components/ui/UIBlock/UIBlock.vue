<template>
  <div
    :class="[`background-${props.background || 'grey'}`]"
    class="ui-block">
    <div class="flex flex-col gap-4 items-start min-w-full max-w-full">
      <div
        v-if="$slots.title"
        class="title">
        <slot name="title" />
      </div>

      <div
        v-if="$slots.content"
        class="content">
        <slot name="content" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  background?: "grey" | "red" | "orange" | "black";
}>();
</script>

<style lang="scss" scoped>
.ui-block {
  @apply rounded p-4;

  .title {
    @apply font-medium text-4.5 w-full leading-6;
  }

  .content {
    @apply text-4 w-full leading-5;
  }
}

.background {
  &-grey {
    @apply bg-bg-level-1;
  }

  &-red {
    @apply bg-bg-red-light;
  }

  &-orange {
    @apply bg-bg-orange-light;
  }

  &-black {
    @apply bg-bg-contrast;
  }
}
</style>
