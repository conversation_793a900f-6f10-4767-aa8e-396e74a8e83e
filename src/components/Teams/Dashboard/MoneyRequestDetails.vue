<script lang="ts" setup>
import ModalScreen from "@/components/ui/Modal/ModalScreen.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import Comment from "@/components/ui/Comment/Comment.vue";
import SummaryPlate from "@/components/ui/Summary/SummaryPlate.vue";
import AmountBalance from "@/components/Teams/AmountBalance.vue";

import { format } from "date-fns";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { getFormattedDate } from "@/helpers/time";
import { balanceWithSymbol } from "@/helpers/account";
import { useTeamEstimate } from "@/composable/Team/Estimate";
import { useQueryClient } from "vue-query";

interface Props {
  data: any;
  disabled: boolean;
}

const { t } = useI18n();
const emit = defineEmits(["onApprove", "onDecline"]);
const props = defineProps<Props>();
const queryClient = useQueryClient();

const estimate = useTeamEstimate({
  days: ref<number>(1),
  queryClient,
  enabled: ref<boolean>(true),
});
const isEditMode = ref<boolean>(false);
const editAmount = ref<number>(Number(props.data.amount));

// computed
const currentUserEstimate = computed(() => {
  return estimate.data.value.find(
    (item: any) => item.member?.email === props.data.email
  );
});
const fields = computed(() => [
  {
    title: t("team.dashboard.moneyRequests.details.accountsBalance"),
    value: balanceWithSymbol(currentUserEstimate.value?.account_balance || 0, {
      symbol: "$",
      iso_code: "USD",
    }),
  },
  {
    title: t("team.dashboard.moneyRequests.details.cardsBalance"),
    value: balanceWithSymbol(currentUserEstimate.value?.card_balance || 0, {
      symbol: "$",
      iso_code: "USD",
    }),
  },
  {
    title: t("team.dashboard.moneyRequests.details.amountPeriod"),
    value: balanceWithSymbol(
      currentUserEstimate.value?.amount_full_period || 0,
      {
        symbol: "$",
        iso_code: "USD",
      }
    ),
  },
  {
    title: t("team.dashboard.moneyRequests.details.cardsCount"),
    value: currentUserEstimate.value?.cards || 0,
  },
]);

// methods
const onApprove = () =>
  emit("onApprove", { ...props.data, amount: editAmount.value });
const onDecline = () =>
  emit("onDecline", { ...props.data, amount: editAmount.value });
const onEditModeToggle = () => (isEditMode.value = !isEditMode.value);
const onChange = (value: number) => (editAmount.value = value);
</script>

<template>
  <ModalScreen
    content-class="flex flex-col gap-10"
    :boxed="true"
    :esc="true">
    <template #title>
      {{ $t("team.dashboard.moneyRequests.details.title") }}
    </template>

    <!--  Member information  -->
    <SummaryPlate
      border="border-none"
      padding="p-5"
      class="bg-neutral-100">
      <p class="text-lgt text-neutral-900 font-extrabold">{{ data.email }}</p>

      <div class="grid grid-cols-2 gap-5 mt-5">
        <div
          v-for="field in fields"
          :key="field.title">
          <p class="text-base text-neutral-500">{{ field.title }}</p>

          <p class="text-base text-neutral-900 mt-0.5">{{ field.value }}</p>
        </div>
      </div>
    </SummaryPlate>

    <!--  Request information  -->
    <div class="flex flex-col gap-5">
      <h6>{{ $t("team.dashboard.moneyRequests.details.requestAmount") }}</h6>

      <div class="flex flex-col gap-[10px]">
        <!--    Amount sum    -->
        <div
          v-if="!isEditMode"
          class="flex items-center gap-4">
          <h3>
            {{
              balanceWithSymbol(Number(data.amount), {
                iso_code: "USD",
                symbol: "$",
              })
            }}
          </h3>

          <div
            class="rounded-full p-2 bg-gray-100 cursor-pointer"
            @click="onEditModeToggle">
            <DynamicIcon
              name="edit-alt"
              class="h-6 w-6" />
          </div>
        </div>
        <div v-else>
          <AmountBalance
            :label="$t('team.dashboard.details.amountEdit')"
            :value="String(editAmount)"
            @change="onChange" />
        </div>

        <Comment>{{ data.description }}</Comment>
      </div>

      <p class="text-neutral-500">
        {{
          getFormattedDate(new Date(data.created_at)) +
          format(new Date(data.created_at), " • HH:mm (z)")
        }}
      </p>
    </div>

    <!--  Buttons  -->
    <div class="flex flex-col gap-2">
      <UiButton
        :title="$t('buttons.approve')"
        :disabled="disabled"
        @click="onApprove" />

      <UiButton
        type="secondary"
        :title="$t('buttons.decline')"
        :disabled="disabled"
        @click="onDecline" />
    </div>
  </ModalScreen>
</template>

<style lang="sass" module></style>
