<template>
  <MembersReportTable
    :reports="finalReports"
    :start-date="startDate"
    :end-date="endDate"
    :currency="currency"
    :loading="loading"
    :full="full" />
</template>

<script lang="ts" setup>
import MembersReportTable from "./MembersReportTable.vue";
import type { TMemberReport } from "./types";

import { computed } from "vue";

type TRawMemberReport = {
  deposit_by_currency: Array<any>;
  deposit_gift: string;
  deposit_total: string;
  end_balance_accounts: string;
  end_balance_cards: string;
  end_balance_total: string;
  spend_expense: string;
  spend_fee: string;
  spend_purchased: string;
  spend_reordered: string;
  spend_total: string;
  spend_transaction_delay: string;
  spend_transaction_to_outside_user: string;
  spend_transaction_to_another_user: string;
  spend_withdraw: string;
  start_balance_accounts: string;
  start_balance_cards: string;
  start_balance_total: string;
  user_email: string;
  user_name: string;
};

interface Props {
  reports: Array<any>;
  startDate?: Date;
  endDate?: Date;
  currency: string;
  loading?: boolean;
  full?: boolean;
}

const props = defineProps<Props>();

const finalReports = computed<Array<TMemberReport>>(() => {
  return props.reports.map((item: TRawMemberReport) => ({
    name: item.user_name,
    startBalance: Number(item.start_balance_total),
    income: Number(item.deposit_total),
    expenses: Number(item.spend_expense),
    purchasedCard: Number(item.spend_purchased),
    reorderedCard: Number(item.spend_reordered),
    fee: Number(item.spend_fee),
    outcome: Number(item.spend_transaction_to_outside_user),
    endBalance: Number(item.end_balance_total),
  }));
});
</script>

<style lang="sass" module></style>
