<script lang="ts" setup>
import { computed, ref } from "vue";
import { prepareAccountBalance } from "@/helpers/account";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import TopUpMemberWallet from "@/components/Teams/Modals/TopUpMemberWallet.vue";
import WithdrawFromTheUser from "@/components/Teams/Modals/WithdrawFromTheUser.vue";

import { teamMemberEstimateKey } from "@/config/queryConfig";
import { useQueryClient } from "vue-query";

interface Props {
  item: any;
}
const emit = defineEmits(["update"]);
const props = defineProps<Props>();

const memberForTopUpId = ref<number | null>(null);
const memberForWithdrawId = ref<number | null>(null);
const queryClient = useQueryClient();

const name = computed(() => {
  return props.item.name || props.item.email;
});
const isDeleted = computed<boolean>(
  () => !!props.item?.accounts?.find((account: any) => account?.is_deleted)
);

const onMemberTopUp = (id: number) => (memberForTopUpId.value = id);
const onMemberWithdraw = (id: number) => (memberForWithdrawId.value = id);

const closeWithdraw = (needUpdate: boolean = false) => {
  if (needUpdate) {
    queryClient.invalidateQueries([teamMemberEstimateKey]);
    emit("update");
  }

  memberForWithdrawId.value = null;
};

const closeTopUp = (needUpdate: boolean = false) => {
  if (needUpdate) {
    queryClient.invalidateQueries([teamMemberEstimateKey]);
    emit("update");
  }

  memberForTopUpId.value = null;
};

const selectedMember = {
  id: props.item.id,
};
</script>

<template>
  <div :class="$style.row">
    <div :class="$style.header">
      <p class="overflow-ellipsis overflow-hidden text-left w-full md:w-fit">
        {{ name }}
      </p>

      <div class="flex items-center gap-2 w-full md:w-fit">
        <button
          class="w-full"
          :class="$style.button"
          @click="() => onMemberWithdraw(item.id)">
          {{ $t("team.estimate.withdrawButton") }}
        </button>

        <button
          v-if="!isDeleted"
          class="w-full"
          :class="$style.button"
          @click="() => onMemberTopUp(item.id)">
          {{ $t("team.estimate.depositButton") }}
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4 mt-5">
      <div
        v-for="item in props.item.accounts"
        :key="item.id"
        :class="$style.item">
        <span :class="$style.icon">
          <DynamicIcon
            v-if="item._currency"
            :name="item._currency.iso_code.toLowerCase() + '-circle'" />
        </span>

        <span :class="$style.name">
          {{ item._currency.iso_code + " " + $t("account") }}
        </span>

        <span :class="$style.balance">
          {{ prepareAccountBalance(item.balance, item._currency.iso_code) }}
        </span>
      </div>
    </div>

    <TopUpMemberWallet
      v-if="memberForTopUpId"
      :data="selectedMember"
      @close="closeTopUp" />

    <WithdrawFromTheUser
      v-if="memberForWithdrawId"
      :data="selectedMember"
      @close="closeWithdraw" />
  </div>
</template>

<style lang="scss" module>
.row {
  @apply w-full mt-8 bg-white p-6 rounded-2xl border-greyscale-200 border;
}
.item {
  @apply relative flex flex-col gap-1 border p-5 rounded-2xl h-max w-full transition-all bg-white;
}
.icon {
  @apply bg-white border w-max rounded-sm p-[7px] mb-1;
  & svg {
    @apply w-[18px] h-[18px];
  }
}
.header {
  @apply font-bold flex flex-col md:flex-row items-center flex-nowrap justify-between w-full gap-2;
}
.button {
  @apply cursor-pointer border border-gray-400  rounded-base p-1 px-3;
  @apply flex items-center flex-col text-sm text-gray-400 w-full md:w-fit min-w-fit;
  @apply hover:text-white hover:bg-gray-700  hover:border-gray-700;
}

.name {
  @apply text-greyscale-600 font-medium text-sm;
  letter-spacing: 0.4px;
}

.balance {
  @apply text-h5 font-extrabold overflow-ellipsis overflow-hidden;
}
</style>
