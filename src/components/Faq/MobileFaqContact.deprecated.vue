<script lang="ts" setup>
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useSupportManager } from "@/composable";
import { openLink } from "@/helpers/events";

const { whatsAppLink, telegramLink, isSuccessLoadData } = useSupportManager();

const openSupport = (key: "telegram" | "skype" | "whatsapp") => {
  if (key === "telegram") {
    openLink(`${telegramLink.value}`, true);
  }

  if (key === "whatsapp") {
    openLink(`${whatsAppLink.value}`, true);
  }
};
</script>
<template>
  <div
    name="support"
    class="flex flex-col gap-3 w-full">
    <h4 class="text-4.5 font-medium leading-6">{{ $t("Support") }}</h4>

    <div
      v-if="isSuccessLoadData"
      class="grid grid-cols-1 gap-2">
      <UIButton
        type="button"
        color="black"
        :icon-only="false"
        :disabled="false"
        class="font-medium"
        @click="openSupport('telegram')">
        <template #left>
          <DynamicIcon
            name="telegram-color-circle"
            class="w-6" />
        </template>
        Telegram
      </UIButton>
      <UIButton
        type="button"
        color="black"
        :icon-only="false"
        :disabled="false"
        class="font-medium"
        @click="openSupport('whatsapp')">
        <template #left>
          <DynamicIcon
            name="whatsapp_colored"
            class="w-6" />
        </template>
        WhatsApp
      </UIButton>
    </div>
  </div>
</template>
