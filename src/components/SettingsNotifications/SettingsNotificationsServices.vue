<script lang="ts" setup>
import { computed } from "vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useUserStore } from "@/stores/user";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import type { TUserSecuritySettings } from "@/types/api/TUserSecuritySettings";
import OneSignalService from "@/services/OneSignalService";
import { useNotificationSettings } from "@/components/SettingsNotifications/useNotificationSettings";

const props = defineProps<{
  settings: TUserSecuritySettings;
}>();

const { t } = useI18n();
const router = useRouter();
const { user } = useUserStore();
const { isWebPushEnabled, availableServices } = useNotificationSettings();

const userTelegram = computed(() => {
  return user.telegram ? `@${user.telegram}` : t("settings.security.hiddenTg");
});

const userWhatsapp = computed(() => {
  return user.whatsapp ? `+${user.whatsapp}` : t("settings.security.hiddenTg");
});

const openTelegramBot = () => {
  window.open(
    // eslint-disable-next-line max-len
    `https://t.me/${props.settings?.telegram?.bot}?${props.settings?.telegram?.command}=${props.settings?.telegram?.code}`,
    "_blank"
  );
  setTimeout(() => {
    router.go(0);
  }, 10000);
};

const openWhatsAppHandler = () => {
  window.open(
    // eslint-disable-next-line max-len
    `https://wa.me/${props.settings?.whatsapp?.bot}?text=/${props.settings?.whatsapp?.command} ${props.settings?.whatsapp?.code}`,
    "_blank"
  );
  setTimeout(() => {
    router.go(0);
  }, 10000);
};

const connectWebPushHandler = () => {
  OneSignalService.subscribe(props.settings.onesignal_hash);
};
</script>

<template>
  <div class="notifications-services">
    <div
      v-for="notifyService in availableServices"
      :key="notifyService.id"
      class="service-card"
      :title="notifyService.description ?? ''">
      <h4 class="text-base font-semibold text-nowrap truncate mb-2">
        {{ notifyService.name }}
      </h4>

      <template v-if="notifyService.slug === 'tg'">
        <div
          v-if="settings.telegram.activated"
          class="service-card__value">
          <span class="truncate">{{ userTelegram }}</span>
        </div>
        <UIButton
          v-else
          size="s"
          color="black"
          @click="openTelegramBot">
          {{ $t("Connect") }}
        </UIButton>
      </template>

      <template v-if="notifyService.slug === 'email'">
        <div class="service-card__value">
          <span class="truncate">{{ user.email }}</span>
        </div>
      </template>

      <template v-if="notifyService.slug === 'web-push'">
        <div
          v-if="isWebPushEnabled"
          class="service-card__value justify-center">
          <span class="truncate">{{ $t("connected") }}</span>
        </div>
        <UIButton
          v-else
          size="s"
          color="black"
          @click="connectWebPushHandler">
          {{ $t("Connect") }}
        </UIButton>
      </template>

      <template v-if="notifyService.slug === 'whatsapp'">
        <div
          v-if="settings.whatsapp.activated"
          class="service-card__value justify-center">
          <span class="truncate">{{ userWhatsapp }}</span>
        </div>
        <UIButton
          v-else
          size="s"
          color="black"
          @click="openWhatsAppHandler">
          {{ $t("Connect") }}
        </UIButton>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.notifications-services {
  @apply grid items-start grid-cols-4 gap-x-2;

  .service-card {
    @apply flex flex-col p-3 rounded-base bg-gray-100 overflow-hidden;

    &__value {
      @apply flex w-full items-center bg-bg-level-2 opacity-50 px-2 h-8 rounded;
    }
  }
}
</style>
