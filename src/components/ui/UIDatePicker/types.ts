import type {
  DatePickerModel,
  DatePickerRangeObject,
} from "v-calendar/dist/types/src/use/datePicker";
import type { AttributeConfig } from "v-calendar/dist/types/src/utils/attribute";

export type UIDatePickerModel = DatePickerModel;
export type UIDatePickerRangeObject = DatePickerRangeObject;

export interface UIDatePickerProps {
  modelValue?: UIDatePickerModel;
  mode?: "date" | "dateTime" | "time";
  maxDate?: Date;
  minDate?: Date;
  isRange?: boolean;
  masks?: Record<string, string | string[]>;
  attributes?: AttributeConfig;
  defaultValue?: UIDatePickerModel;

  // input props
  label?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;

  isFullscreen?: boolean;
  fullscreenTitle?: string;
}
