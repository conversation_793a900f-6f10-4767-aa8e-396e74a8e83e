<script setup lang="ts">
import UICheckbox from "@/components/ui/UICheckbox/UICheckbox.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

const props = withDefaults(
  defineProps<{
    selected?: boolean;
    caption?: string;
    multiple?: boolean;
  }>(),
  {
    caption: "",
  }
);

defineSlots<{
  leadIcon(): any;
  default(): any;
  badge(): any;
  content(): any;
}>();

const onCheckboxClick = (e: Event) => {
  e.preventDefault();
};
</script>

<template>
  <div
    class="ui-select-option"
    :class="{ selected }">
    <div class="ui-select-option__wrapper">
      <slot>
        <UICheckbox
          v-if="multiple"
          :model-value="props.selected"
          @click="onCheckboxClick" />

        <div v-if="$slots.leadIcon">
          <slot name="leadIcon" />
        </div>

        <div class="text-ellipsis overflow-hidden">
          <slot name="content" />
        </div>

        <div
          v-if="caption"
          class="text-fg-base-tertiary">
          {{ caption }}
        </div>

        <div v-if="$slots.badge">
          <slot name="badge" />
        </div>
      </slot>
      <div
        v-if="!multiple"
        class="ms-auto flex items-center text-fg-base-primary"
        :class="{ 'opacity-0': !selected }">
        <DynamicIcon
          class="w-5 h-5"
          name="check-thin" />
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.ui-select-option {
  @apply select-none;

  .ui-select-option__wrapper {
    @apply overflow-hidden flex items-center gap-1 rounded text-nowrap cursor-pointer
      py-2.5 pl-3 pr-2
      transition-colors
      bg-bg-overlay-static
      hover:bg-bg-overlay-hover;
  }

  &.selected .ui-select-option__wrapper {
    @apply bg-bg-overlay-active-static
      hover:bg-bg-overlay-active-hover;
  }
}
</style>
