import { computed, type ComputedRef } from "vue";

import { CARDS_SIMPLE_STATUS } from "@/constants/cards_simple_status";
import { isCardDisabledForTopUp } from "@/helpers/isCardDisabledForTopUp";
import { type TCardResource } from "@/types/api/TCardResource";

/**
 * A utility function for managing card-related computed properties.
 *
 * @param {ComputedRef<TCardResource | undefined | null>} card - A reactive reference to the card resource.
 * @returns {Object} An object containing various computed properties related to the card.
 *
 * @category Composables
 */
export function useCardParams(
  card: ComputedRef<TCardResource | undefined | null>
) {
  const balance = computed(() => Number(card.value?.account.balance ?? 0));
  const isBalanceLessThan1 = computed(() => balance.value < 1);
  const isBalanceLessThan1_1 = computed(() => balance.value < 1.1);
  const isBalanceLessThan0 = computed(() => balance.value < 0);
  const isBalanceLessThan0_1 = computed(() => balance.value < 0.1);

  /**
   * Check if card is in active state
   */
  const isCardActive = computed(
    () => card.value?.simple_status === CARDS_SIMPLE_STATUS.ACTIVE
  );

  /**
   * Check if account balance is less than 0 or 1
   */
  const isBalanceNegative = computed(() =>
    card.value?.is_zero_withdrawal_available
      ? isBalanceLessThan0.value
      : isBalanceLessThan1.value
  );

  /**
   * Check if TopUp action is disabled
   */
  const isTopUpDisabled = computed(() => {
    if (!card.value) return true;

    return (
      (!isCardActive.value && !isBalanceNegative.value) ||
      isCardDisabledForTopUp(card.value.mask, card.value.created_at)
    );
  });

  /**
   * Check if Withdraw action is disabled
   */
  const isWithdrawDisabled = computed(() =>
    card.value?.is_zero_withdrawal_available
      ? isBalanceLessThan0_1.value
      : isBalanceLessThan1_1.value
  );

  /**
   * Minimum available card balance for block
   */
  const cardMinimumBalanceForBlock = computed(() =>
    card.value?.is_zero_withdrawal_available ? 0 : 1
  );

  return {
    isCardActive,
    isBalanceNegative,
    isTopUpDisabled,
    isWithdrawDisabled,
    cardMinimumBalanceForBlock,
  };
}
