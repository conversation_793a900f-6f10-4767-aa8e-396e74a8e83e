import { usePstFetch } from "@/composable/usePstFetch";
import type { UseFetchOptions } from "@vueuse/core";

export type UseUserCardMultiBuyPostReq = {
  account_id: number;
  type: string;
  start_balance: string;
  description: string;
  system: number;
  bin?: number | null;
  count: number;
  with_error_data?: boolean;
};

export const useUserCardMultiBuyPost = (
  body: UseUserCardMultiBuyPostReq,
  useFetchOpts?: UseFetchOptions
) => {
  const url: string = "card/multi-buy";
  return usePstFetch(url, useFetchOpts || {})
    .post(body)
    .json<{
      success: boolean;
      message: string;
      type: string;
      field?: string;
    }>();
};
