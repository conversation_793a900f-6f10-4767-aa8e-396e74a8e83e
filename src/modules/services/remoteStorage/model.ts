export enum ERemoteStorageKeys {
  FREE_CARD = "freeCard",
  CARD_DATA = "freeCard_data",
  IS_PRO_MODE = "is_pro_mode",
  SUBSCRIPTION_AB_1 = "subscriptions_ab_1",
  GEO_BLOCK = "experiment_17",
  SHOULD_GEO_BLOCK = "should_block_by_location",
  TWO_STEP_VERIFICATION = "2_step_verification3",
  DISABLED_WELCOME = "disabled_welcome",
  NEW_SUBSCRIPTIONS_TARIFFS = "new_subscriptions_tariffs1",
  NEW_SUBSCRIPTIONS_TARIFFS_2 = "new_subscriptions_tariffs2",
  MOBILE_APP_BANNER_CLOSED = "mobile-app-banner-closed",
  PRIVATE_MANAGER = "private-manager",
  SUBSCRIPTION_PLUS_CARD_AUTOBUY1 = "subscription_plus_card_autobuy1",
  SUBSCRIPTION_RETENTION_USERS1 = "subscription_retention_users1",
  NEW_SUBSCRIPTIONS_PROMO_1 = "new_subscriptions_promo_1",
}

export interface IRemoteStorageModel<K = ERemoteStorageKeys, V = string> {
  key: K;
  value?: V;
}
