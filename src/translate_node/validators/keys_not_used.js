/*
    Поиск неиспользуемых ключей по всем языкам
    Алгоритм: Берем все ключи en.json к примеру и ищем их в проекте, если не найдено, то отправляем уведомление
*/

const { glob } = require("glob");
const Constants = require("../constants");
const { loadFile, getAllKeysEnLocale } = require("../utility");

class Validator {
  constructor() {
    this.error = false;
    this.message = "";
  }

  getName() {
    return "KeysNotUsed";
  }

  countNotUsedKeys(dic) {
    const arr = [];
    for (let key in dic) {
      const count = dic[key];
      if (count === 0) {
        arr.push(key);
        console.log(key);
      }
    }
    return arr.length;
  }

  async run() {
    const dic = {};

    //1. Получаем ключи
    const keys = await getAllKeysEnLocale();
    for (let key of keys) {
      if (!dic[key]) {
        dic[key] = 0;
      }
    }

    //1. Все файлы
    const files = await glob(
      `${Constants.folders.src}**/**/**/**/**.{ts,vue}`,
      {
        ignore: "node_modules/**",
        nodir: true,
      }
    );

    for (let filePath of files) {
      const { status, data } = await loadFile(filePath);

      if (!status) {
        console.log(`File download error: ${filePath}`);
        continue;
      }

      for (let key of keys) {
        if (data.indexOf(key) !== -1) {
          dic[key]++;
        }
      }
    }

    console.log(
      this.getName() + " info: \n\tNumber of unused keys: ",
      this.countNotUsedKeys(dic)
    );
  }
}

module.exports = new Validator();
