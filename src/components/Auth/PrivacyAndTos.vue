<template>
  <div :class="$style.checkboxLabel">
    {{ $t("auth.agreement-1") }}
    <a
      data-cy="privacy_policy"
      @click.prevent="openPrivacy">
      {{ $t("auth.agreement-2") }} </a
    >,
    <a
      data-cy="terms_of_use"
      @click.prevent="openTos">
      {{ $t("auth.agreement-3") }}
    </a>
    {{ $t("auth.agreement-4") }}
    <a
      data-cy="restrictions"
      @click.prevent="openRestrictions">
      {{ $t("auth.agreement-5") }} </a
    >.
  </div>
</template>

<script lang="ts" setup>
import { openPrivacy, openRestrictions, openTos } from "@/helpers/other";
</script>

<style lang="scss" module>
.checkboxLabel {
  @apply text-neutral-700 text-base font-normal;

  & a,
  & span {
    @apply text-greyscale-900 cursor-pointer font-semibold;
  }
}
</style>
