<template>
  <div :class="[$style.root, icon && $style.withIcon]">
    <label
      v-if="props.label"
      class="text-greyscale-600 block"
      :class="props.labelClass">
      {{ label }}
    </label>

    <input
      :value="value || modelValue"
      :type="props.type"
      :name="String(new Date())"
      :autocomplete="type === 'email' ? 'email' : autoCompleteProps"
      :placeholder="placeholder"
      :disabled="disabled"
      autocapitalize="off"
      spellcheck="false"
      @input="onInput"
      @change="onChange"
      @focus="emit('focus')"
      @blur="emit('blur')" />

    <span
      v-if="icon"
      :class="$style.icon">
      <DynamicIcon :name="icon" />
    </span>

    <transition name="error-input">
      <span
        v-if="error"
        data-cy="error_msg"
        :class="[$style.error, label && $style.withLabel]">
        {{ error }}
      </span>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  placeholder?: string;
  icon?: string;
  autoCompleteProps?: string;
  type?: string;
  label?: string;
  labelClass?: string;
  error?: string;
  value?: string | number;
  modelValue?: string | number;
  disabled?: boolean;
}

const emit = defineEmits([
  "change",
  "blur",
  "focus",
  "input",
  "update:modelValue",
]);

const props = withDefaults(defineProps<Props>(), {
  type: "text",
  labelClass: "font-extrabold pb-2",
  autoCompleteProps: "off",
});

// mutations
const onInput = (event: Event) => {
  const value = (event.target as HTMLInputElement).value;
  emit("input", value);
  emit("update:modelValue", value);
};

const onChange = (event: Event) => {
  const value = (event.target as HTMLInputElement).value;
  emit("change", value);
  emit("update:modelValue", value);
};
</script>

<style lang="scss" module>
.root {
  @apply relative;

  & input {
    @apply border-greyscale-300 p-4;
  }
}

.withIcon {
  @apply relative;

  input {
    @apply py-4 pr-4 pl-12;
  }
}

.icon {
  @apply absolute left-0 top-0 h-full my-auto px-4 flex items-center;

  &__focus {
    @apply text-secondary-base;
  }

  &__blur {
    @apply text-greyscale-500;
  }

  & svg {
    @apply w-[18px] h-[18px];
  }
}

.error {
  @apply absolute top-0 left-0 ml-3 -mt-3 p-1 text-sm text-error-light bg-white rounded-full;
}
.withLabel {
  @apply top-8;
}
</style>
