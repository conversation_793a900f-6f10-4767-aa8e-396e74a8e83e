<script setup lang="ts">
import CardWithFire from "@/assets/svg/card-fire-v2.svg";
import CardForAdvertise from "@/assets/svg/card-advertise.svg";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/user";
import router from "@/router";
import { RouteName } from "@/constants/route_name";
import {
  useCountrySets,
  useSubscriptionPlusCardAutobuy1Experiment,
  useSubscriptionsInfo,
  useUserSubscriptionsTariffs,
  useUserTariff,
} from "@/composable";
import { useGlobalUniversalDialog } from "@/components/GlobalUniversalDialog/useGlobalUniversalDialog";
import { isMobile } from "@/helpers";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

const { t } = useI18n();
const { userTariff } = useUserTariff();
const { userSubscriptionsTariffs: subscriptionsTariffs } =
  useUserSubscriptionsTariffs();
const { subscriptionsStatus } = useSubscriptionsInfo();
const userStore = useUserStore();
const { countrySetGuard } = useCountrySets();
const { openDialog, closeDialog } = useGlobalUniversalDialog();
const { getValue } = useSubscriptionPlusCardAutobuy1Experiment();
const { isActive: isAdvWithSubActive } = getValue();

const { isTeamMember } = userStore;
const isFreeIssuing = computed<boolean>(() => {
  return subscriptionsStatus.value;
});

const ultimaTariffs = ["ultima-weekly", "ultima-weekly-3ds"];
const ultimaCardPriceDisplay = computed<number | undefined>(() => {
  return userTariff.value?.data
    ?.filter((t) => ultimaTariffs.includes(t.slug))
    .map((t) => Number(t.card_price))
    .sort((a, b) => a - b)[0];
});
const ultimaPriceText = computed(() => {
  if (isFreeIssuing.value) {
    return t("cards.cardTariffStartingPrice.free-issuing");
  }
  return t("cards.cardTariffStartingPrice", {
    p: ultimaCardPriceDisplay.value,
  });
});

const largestCashbackPercent = computed<number | undefined>(() => {
  return subscriptionsTariffs.value
    ?.map((t) => Number(t.cashback_percent))
    .sort((a, b) => b - a)[0];
});

const largestCashbackPercentText = computed(() => {
  return t("cards.largestCashbackPercent", {
    p: largestCashbackPercent.value,
  });
});

const advCardText = computed(() => {
  if (isAdvWithSubActive.value && !subscriptionsStatus.value) {
    return t("issue_a_card");
  } else if (subscriptionsStatus.value) {
    return t("cards.select-card-type-button");
  } else {
    return t("CardsTableNoCards.moreDetails");
  }
});

const onCardSelected = (cardType: "ultima" | "forAdv") => {
  if (!subscriptionsStatus.value && isTeamMember && cardType === "forAdv") {
    onOpenRequestPrivateFromMasterDialog();
  } else if (
    cardType === "forAdv" &&
    !subscriptionsStatus.value &&
    isAdvWithSubActive.value
  ) {
    router.push({
      name: RouteName.CREATE_CARD,
      query: { type: cardType },
    });
  } else if (cardType === "forAdv" && !subscriptionsStatus.value) {
    router.push({
      name: RouteName.SUBSCRIPTION_TARIFF,
    });
  } else {
    countrySetGuard(
      () => {
        router.push({
          name: RouteName.CREATE_CARD,
          query: { cardType },
        });
      },
      () => {},
      () => {},
      cardType === "forAdv"
    );
  }
};

const onOpenRequestPrivateFromMasterDialog = () => {
  openDialog({
    title: t("pst-private.required-private-dialog-title"),
    text: t("pst-private.required-private-dialog-text"),
    showBtnConfirm: true,
    btnConfirmText: t("OK"),
    showBtnCancel: false,
    withCloseIcon: true,
    callbackConfirm: closeDialog,
  });
};
</script>

<template>
  <div class="email-verified">
    <div class="container">
      <div class="header">
        <div class="header-title">
          {{ $t("email-verification.email-verification-complete.title") }}
        </div>
        <div class="header-subtitle">
          {{ $t("email-verification.email-verification-complete.subtitle") }}
        </div>
      </div>
      <div class="content">
        <div class="action">
          <div class="action-top">
            <div class="fire-card-icon">
              <CardWithFire class="w-[222px] h-[140px] -mb-[10px]" />
            </div>
            <div
              v-if="(ultimaCardPriceDisplay || isFreeIssuing) && !isTeamMember"
              class="badge-container">
              <div class="badge orange-badge">
                {{ ultimaPriceText }}
              </div>
            </div>
          </div>
          <div class="action-middle">
            <div class="title">
              {{ $t("CardsTableNoCards.ultimaIssueCardTitle") }}
            </div>
            <p class="description">
              {{ $t("CardsTableNoCards.ultimaIssueCardDescription") }}
            </p>
          </div>
          <div class="action-bottom">
            <UIButton
              class="w-full"
              color="orange-solid"
              @click="onCardSelected('ultima')">
              <template
                v-if="isMobile"
                #left>
                <DynamicIcon name="card-physical" />
              </template>
              {{ $t("issue_a_card") }}
            </UIButton>
          </div>
        </div>
        <div class="action">
          <div class="action-top">
            <div>
              <CardForAdvertise class="w-[160px] h-[140px]" />
            </div>
            <div
              v-if="largestCashbackPercent && !isTeamMember"
              class="badge-container mt-2.5">
              <div class="badge blue-badge">
                {{ largestCashbackPercentText }}
              </div>
            </div>
          </div>
          <div class="action-middle">
            <div class="title">
              {{ $t("CardsTableNoCards.advertiseIssueCardTitle") }}
            </div>
            <p class="description">
              {{
                $t(
                  "email-verification.email-verification-complete.adv-description"
                )
              }}
            </p>
          </div>
          <div class="action-bottom">
            <UIButton
              class="w-full"
              color="blue-solid"
              @click="onCardSelected('forAdv')">
              {{ advCardText }}
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.email-verified {
  .container {
    @apply w-[472px] px-4 py-5;
  }
  .header {
    @apply text-center pt-[218px] pb-10;
  }
  .header-title {
    @apply font-medium text-10 leading-11 text-fg-primary pb-1;
  }

  .header-subtitle {
    @apply font-normal text-4.5 leading-6 text-fg-secondary;
  }
  .content {
    @apply flex flex-row gap-2;
  }
  .action {
    @apply w-1/2 px-4 flex flex-col gap-[30px];
  }
  .action-top {
    @apply h-[180px] flex flex-col items-center justify-end;
  }

  .action-middle {
    @apply flex-grow;
  }
  .title {
    @apply text-5 leading-6 font-medium text-center px-3;
  }

  .description {
    @apply text-3.5 text-center leading-4 px-2 mt-3;
  }

  .badge-container {
    @apply flex items-center justify-center;

    .blue-badge {
      @apply bg-bg-blue-light text-fg-blue;
    }
    .orange-badge {
      @apply bg-bg-orange-light text-fg-orange;
    }
    .badge {
      @apply rounded-[6px] font-medium text-lg leading-4 px-1.5 py-1;
    }
  }
}
</style>
