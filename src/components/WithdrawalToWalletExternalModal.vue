<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { Skeletor } from "vue-skeletor";

import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import { IsoCodeNames } from "@/constants/iso_code_names";
import { getCurrencyById } from "@/helpers/store";
import {
  type TWithdrawalPostReq,
  useWithdrawalPost,
} from "@/composable/API/useWithdrawalPost";
import {
  TOAST_TYPE,
  useCallToast,
  useUserAccountGet,
  useUserExchangeRatesGet,
} from "@/composable";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useDictionaryExchangeRatesGet } from "@/composable/API/useDictionaryExchangeRatesGet";
import { useValidator } from "@/composable/useValidator";

import AccountsAndCardsSelect from "@/components/AccountsAndCardsSelect/AccountsAndCardsSelect.vue";
import SupportTrigger from "@/components/SupportTrigger.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UICurrencyInput from "@/components/ui/UICurrencyInput/UICurrencyInput.vue";
import UISelect from "@/components/ui/UISelect/UISelect.vue";
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import UITextarea from "@/components/ui/UITextarea/UITextarea.vue";
import UITextInput from "@/components/ui/UITextInput/UITextInput.vue";
import { useUserStore } from "@/stores/user";
import { useWithdrawalsRequests } from "@/composable/useWithdrawalsRequests";
import TransferCompleted from "@/components/TransferCompleted/TransferCompleted.vue";

const { validateAddressUsdtTRC20 } = useValidator();

const userStore = useUserStore();

interface Props {
  isOpen: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
});

const emit = defineEmits<{
  close: [];
}>();

type TWithdrawalView = "form" | "confirm" | "success";

const { t } = useI18n();

const withrawalView = ref<TWithdrawalView>("form");

const { setWithdrawalsTotals } = useWithdrawalsRequests();
const { data: ratesData } = useUserExchangeRatesGet();
const { data: dropdownDisplayRatesData } = useDictionaryExchangeRatesGet();
const dropdownDisplayRates = computed(() => {
  return dropdownDisplayRatesData.value?.data ?? null;
});

const getDefaultValueBalance = (): number => {
  return Number(userStore.userFees.min_withdraw_amount) || 10.1;
};

const getFeePercent = () => {
  return parseFloat(userStore.userFees.withdraw || "0") * 100 || 0;
};

const MIN_START_BALANCE = getDefaultValueBalance();
const DEFAULT_VALUE_BALANCE = getDefaultValueBalance();
const FEE_PERCENT = getFeePercent();
const MIN_COMMISSION =
  parseFloat(userStore.userFees.min_withdraw_fee_threshold || "0") || 0;
const FEE_PERCENT_CALC_VALUE = 1 - FEE_PERCENT / 100;
const MIN_REASON_DESCRIPTION_LENGTH = 10;

const form = reactive({
  user_account_id: 0,
  wallet: "",
  amount: DEFAULT_VALUE_BALANCE,
  reason: "",
  reasonDescription: "",
});

const formReasonDescriptionError = ref<string>("");
const onChangeFormReasonDescription = () => {
  if (form.reasonDescription.length < 10) {
    formReasonDescriptionError.value = t(
      "withdrawal.reason-description-min-length"
    );
  } else {
    formReasonDescriptionError.value = "";
  }
};

const formWalletError = ref<string | null>(null);

const onChangeFormWallet = () => {
  formWalletError.value = validateAddressUsdtTRC20(form.wallet);
};

const willBeEnrolledAmount = ref<number>(0);

const { data: dataUserAccounts, isFetching: isFetchingUserAccounts } =
  useUserAccountGet();

const accountUsdt = ref<TUserAccountResource | null>(null);

const isFormUserAccountIdFounded = computed(() => {
  const accounts = dataUserAccounts.value?.data || [];
  for (const account of accounts) {
    const currency = getCurrencyById(account.currency_id);
    if (currency?.iso_code === IsoCodeNames.USDT) {
      return !!account?.id;
    }
  }
  return false;
});

const accountOptions = computed(() => {
  if (!dataUserAccounts.value?.data) return [];
  for (const account of dataUserAccounts.value.data) {
    const currency = getCurrencyById(account.currency_id);
    if (currency?.iso_code === IsoCodeNames.USDT) {
      return [account];
    }
  }
  return [];
});

const isErrorAccountNotEnoughMoney = computed(() => {
  if (accountUsdt.value?.balance) {
    return Number(accountUsdt.value?.balance) < form.amount;
  } else {
    return false;
  }
});

const isShowTransferSelect = computed(() => {
  return (
    !isFetchingUserAccounts.value &&
    isFormUserAccountIdFounded.value &&
    ratesData.value?.data &&
    accountOptions?.value?.length
  );
});

const isFeeApplicable = computed(() => {
  return (
    Number(userStore.userFees.withdraw) > 0 ||
    Number(userStore.userFees.min_withdraw_fee_threshold) > 0
  );
});

const calcWillBeEnrolledAmount = (amount: number) => {
  if (!isFeeApplicable.value) return amount;
  const willBeEnrolledAmount = amount * FEE_PERCENT_CALC_VALUE;
  const commission = form.amount - willBeEnrolledAmount;
  return commission < MIN_COMMISSION
    ? form.amount - MIN_COMMISSION
    : willBeEnrolledAmount;
};

willBeEnrolledAmount.value = calcWillBeEnrolledAmount(form.amount);

const commissionWithdrawal = ref<string>("0.00");

const calcCommissionWithdrawal = () => {
  if (!isFeeApplicable.value) {
    return "0.00";
  }
  const feeAmount = form.amount - willBeEnrolledAmount.value;
  return feeAmount.toFixed(2);
};

type TReasonOption = {
  value: string;
  label: string;
};
const reasonOptions: TReasonOption[] = [
  {
    value: "1",
    label: t("withdrawal.reason.fine-with-pst-no-reason"),
  },
  {
    value: "2",
    label: t("withdrawal.reason.dont-need-account-anymore"),
  },
  {
    value: "3",
    label: t("withdrawal.reason.i-urgently-need-money"),
  },
  {
    value: "4",
    label: t("withdrawal.reason.withdrawal-for-test"),
  },
  {
    value: "5",
    label: t("withdrawal.reason.i-cant-pass-the-verification"),
  },
  {
    value: "6",
    label: t("withdrawal.reason.i-wont-use-pst-services-anymore"),
  },
  {
    value: "7",
    label: t("withdrawal.reason.very-high-fees-and-exchange-rates"),
  },
  {
    value: "8",
    label: t("withdrawal.reason.other"),
  },
];
const isReasonOther = computed(() => {
  return form.reason === "8";
});

const getReasonLabelById = (id: string): string => {
  for (const item of reasonOptions) {
    if (item.value === id) {
      return item.label;
    }
  }
  return "";
};

const setMaxTransferAmount = () => {
  form.amount = Number(accountUsdt.value?.balance ?? "0");
  onChangeFormAmount();
};

const onChangeFormAmount = () => {
  if (form.amount < MIN_START_BALANCE) {
    form.amount = MIN_START_BALANCE;
    willBeEnrolledAmount.value = calcWillBeEnrolledAmount(MIN_START_BALANCE);
  } else {
    willBeEnrolledAmount.value = calcWillBeEnrolledAmount(form.amount);
  }
  commissionWithdrawal.value = calcCommissionWithdrawal();
};

onChangeFormAmount();

const isLoadingForm = ref<boolean>(false);
const isBtnContinueDisabled = computed(() => {
  //Check reason
  let isReasonValid;
  if (!form.reason) {
    isReasonValid = false;
  } else {
    if (isReasonOther.value) {
      isReasonValid =
        form.reasonDescription.length >= MIN_REASON_DESCRIPTION_LENGTH;
    } else {
      isReasonValid = true;
    }
  }

  return (
    form.amount < MIN_START_BALANCE ||
    willBeEnrolledAmount.value < MIN_START_BALANCE ||
    !form.wallet ||
    !!formWalletError.value ||
    !isReasonValid ||
    isErrorAccountNotEnoughMoney.value
  );
});
const openConfirmPage = () => (withrawalView.value = "confirm");
const openFormPage = () => (withrawalView.value = "form");
const openSuccessPage = () => (withrawalView.value = "success");

const formReason = computed(() => {
  return isReasonOther.value
    ? form.reasonDescription
    : getReasonLabelById(form.reason);
});
const submit = async () => {
  isLoadingForm.value = true;

  const req: TWithdrawalPostReq = {
    user_account_id: Number(form.user_account_id),
    amount: String(form.amount),
    wallet: form.wallet,
    reason: formReason.value,
  };

  const { data } = await useWithdrawalPost(req);
  if (data.value?.data?.status === "new") {
    openSuccessPage();
    setWithdrawalsTotals();
  } else {
    useCallToast({
      title: data.value?.message ?? t("errors.universal-request-error"),
      options: {
        type: TOAST_TYPE.ERROR,
        id: "withdrawal-form",
      },
    });
  }

  isLoadingForm.value = false;
};

watch(isFormUserAccountIdFounded, () => {
  const accounts = dataUserAccounts.value?.data || [];
  for (const account of accounts) {
    const currency = getCurrencyById(account.currency_id);
    if (currency?.iso_code === IsoCodeNames.USDT) {
      accountUsdt.value = account;
      if (account?.id) {
        form.user_account_id = Number(account.id);
      }
      break;
    }
  }
});

onMounted(() => {
  if (!userStore.userFees.min_withdraw_amount) {
    useCallToast({
      title: t("errors.universal-request-error"),
      options: {
        type: TOAST_TYPE.ERROR,
        id: "transfer-card-to-member-error",
      },
    });
  }
});
</script>

<template>
  <UISideModal
    :is-open="props.isOpen"
    :title="$t('withdrawal.withdrawal-of-funds')"
    @close="emit('close')">
    <template
      v-if="withrawalView === 'confirm'"
      #left>
      <UIButton
        color="grey-free"
        icon-only
        size="s"
        @click="openFormPage">
        <DynamicIcon
          class="w-6 h-6 text-fg-secondary"
          name="arrow-left-thin" />
      </UIButton>
    </template>
    <template #buttons>
      <div class="mr-3">
        <SupportTrigger />
      </div>
    </template>
    <template #content="{ contentHeight }">
      <!-- Form view -->
      <div
        v-if="withrawalView === 'form'"
        class="flex flex-col gap-10">
        <!-- Select Account or Card -->
        <div
          v-if="isShowTransferSelect"
          class="flex flex-col">
          <div class="flex flex-none input-title mb-1">
            <p class="truncate">{{ $t("withdrawal.from-where") }}</p>
          </div>
          <AccountsAndCardsSelect
            v-if="dropdownDisplayRates"
            v-model="form.user_account_id"
            :accounts="accountOptions"
            :error="
              isErrorAccountNotEnoughMoney ? $t('errors.not-enough-money') : ''
            "
            :rates="dropdownDisplayRates"
            :readonly="true"
            :tariffs="[]" />
        </div>
        <Skeletor
          v-else
          class="rounded"
          height="66"
          width="100%" />

        <!-- To address -->
        <div class="flex flex-col">
          <div class="flex flex-none input-title mb-1">
            <p class="truncate">{{ $t("withdrawal.to-where") }}</p>
          </div>
          <div class="flex flex-none">
            <UITextInput
              v-model="form.wallet"
              :error="formWalletError"
              class="w-full"
              size="m"
              @change="onChangeFormWallet" />
          </div>
        </div>

        <!-- Sum -->
        <div class="flex flex-col">
          <div class="flex flex-col">
            <div class="flex flex-none input-title mb-1 justify-between">
              <p class="truncate">{{ $t("withdrawal.transfer-amount") }}</p>
              <div
                class="text-fg-blue hover:text-blue-500 text-4 text-medium leading-5 cursor-pointer transition-colors"
                @click="setMaxTransferAmount">
                Max
              </div>
            </div>
            <div class="flex flex-none flex-col">
              <UICurrencyInput
                v-model="form.amount"
                :currency="IsoCodeNames.USDT"
                :min="MIN_START_BALANCE"
                class="mb-0.5"
                size="m"
                @change="onChangeFormAmount" />
            </div>
          </div>
          <div
            v-if="!isFeeApplicable"
            class="flex flex-row rounded bg-bg-level-1 mt-2 p-4 justify-between items-center">
            <div class="flex flex-none input-title">
              <p class="truncate">{{ $t("withdrawal.service-fee") }}</p>
            </div>
            <div class="flex flex-none">
              {{ $t("withdrawal.no-charge") }}
            </div>
          </div>
        </div>

        <!-- Info -->
        <div
          v-if="isFeeApplicable"
          class="flex flex-row rounded bg-bg-level-1 p-4 justify-between items-center">
          <div class="flex flex-none flex-col gap-0.5">
            <div class="flex flex-none input-title">
              {{ $t("withdrawal.withdrawal-fee", { percent: FEE_PERCENT }) }}
            </div>
            <div
              class="flex flex-none text-fg-secondary text-3.5 leading-5 font-semibold">
              {{
                $t("withdrawal.at-least-10-usdt", {
                  min: userStore.userFees.min_withdraw_fee_threshold,
                })
              }}
            </div>
          </div>
          <div class="flex flex-none">
            {{ commissionWithdrawal }} {{ IsoCodeNames.USDT }}
          </div>
        </div>

        <!-- Reason -->
        <div class="flex flex-col">
          <div class="flex flex-none input-title mb-1">
            <p class="truncate">{{ $t("withdrawal.reason-for-withdrawal") }}</p>
          </div>

          <div class="flex flex-none w-full">
            <UISelect
              v-model="form.reason"
              :cleared="false"
              :options="reasonOptions"
              :placeholder="$t('withdrawal.select-reason')"
              class="w-full"
              size="m">
            </UISelect>
          </div>
        </div>

        <!-- Reason description -->
        <div
          v-if="isReasonOther"
          class="flex flex-col">
          <div class="flex flex-none input-title mb-1">
            {{ $t("withdrawal.describe-reason") }}
          </div>

          <div class="flex flex-none w-full">
            <UITextarea
              v-model="form.reasonDescription"
              :error="formReasonDescriptionError"
              :native-max-length="200"
              :placeholder="$t('withdrawal.placeholder-reason-description')"
              :total-characters-in-counter="200"
              class="w-full"
              @change="onChangeFormReasonDescription" />
          </div>
        </div>

        <div class="flex flex-col gap-2">
          <!-- Information -->
          <div class="flex flex-col rounded bg-bg-level-1 p-4 gap-4">
            <div
              class="flex flex-none text-fg-primary text-4.5 leading-6 font-semibold">
              {{ $t("withdrawal.information.title") }}
            </div>
            <div class="flex flex-col text-fg-primary text-4 leading-6">
              <ul class="pl-5 list-disc">
                <li class="mb-1">
                  {{ $t("withdrawal.information.network-withdrawal-of-funds") }}
                </li>
                <li>
                  {{
                    $t(
                      "withdrawal.information.withdrawal-of-funds-external-usdt-wallet"
                    )
                  }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Warning -->
          <div class="flex flex-col rounded bg-bg-red-light p-4 gap-4">
            <div
              class="flex flex-none text-fg-primary text-4.5 leading-6 font-semibold">
              {{ $t("withdrawal.attention.title") }}
            </div>
            <div class="flex flex-col text-fg-primary text-4 leading-6">
              <ul class="pl-5 list-disc">
                <li class="mb-1">
                  {{
                    $t(
                      "withdrawal.attention.make-sure-correct-usdt-wallet-address"
                    )
                  }}
                </li>
                <li>
                  {{ $t("withdrawal.attention.make-sure-network-matches") }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="flex">
          <UIButton
            :disabled="isBtnContinueDisabled"
            class="w-full"
            color="black"
            @click="openConfirmPage">
            {{ $t("withdrawal.continue-btn") }}
          </UIButton>
        </div>
      </div>

      <!-- Confirm view -->
      <div
        v-if="withrawalView === 'confirm'"
        class="flex flex-col gap-10">
        <div class="flex text-5 text-fg-primary leading-6">
          {{ $t("withdrawal.confirm-transaction") }}
        </div>
        <!-- Select Account or Card -->
        <div
          v-if="isShowTransferSelect"
          class="flex flex-col">
          <div class="flex flex-none input-title mb-1">
            <p class="truncate">{{ $t("withdrawal.from-where") }}</p>
          </div>
          <AccountsAndCardsSelect
            v-if="dropdownDisplayRates"
            v-model="form.user_account_id"
            :accounts="accountOptions"
            :error="
              isErrorAccountNotEnoughMoney ? $t('errors.not-enough-money') : ''
            "
            :rates="dropdownDisplayRates"
            :readonly="true"
            :tariffs="[]" />
        </div>
        <Skeletor
          v-else
          class="rounded"
          height="66"
          width="100%" />

        <!-- To address -->
        <div class="flex flex-col">
          <div class="flex flex-none input-title mb-1">
            <p class="truncate">{{ $t("withdrawal.to-where") }}</p>
          </div>
          <div class="flex flex-none">
            <UITextInput
              v-model="form.wallet"
              :disabled="true"
              :readonly="true"
              class="w-full"
              size="m" />
          </div>
        </div>

        <div class="flex flex-col bg-bg-level-1 p-4 rounded">
          <div class="flex flex-row justify-between items-center mb-4">
            <div class="flex text-fg-secondary text-4 leading-5">
              {{ $t("withdrawal.transfer-amount") }}
            </div>
            <div class="flex text-fg-primary text-4 leading-5">
              {{ form.amount.toFixed(2) }} {{ IsoCodeNames.USDT }}
            </div>
          </div>

          <div
            v-if="isFeeApplicable"
            class="flex flex-row justify-between">
            <div class="flex flex-none flex-col">
              <div class="flex text-fg-secondary text-4 leading-5">
                {{ $t("withdrawal.withdrawal-fee", { percent: FEE_PERCENT }) }}
              </div>
              <div
                class="flex text-fg-secondary text-3.5 font-semibold leading-5">
                {{
                  $t("withdrawal.at-least-10-usdt", {
                    min: userStore.userFees.min_withdraw_fee_threshold,
                  })
                }}
              </div>
            </div>

            <div class="flex text-fg-primary text-4 leading-5">
              {{ commissionWithdrawal }} {{ IsoCodeNames.USDT }}
            </div>
          </div>

          <div
            v-else
            class="flex flex-row justify-between">
            <div class="flex text-fg-secondary text-4 leading-5">
              {{ $t("withdrawal.service-fee") }}
            </div>

            <div class="flex text-fg-primary text-4 leading-5">
              {{ $t("withdrawal.no-charge") }}
            </div>
          </div>

          <!-- Div -->
          <div class="flex flex-none">
            <DynamicIcon
              class="w-full my-7"
              name="dots-new-card" />
          </div>

          <div
            class="flex flex-none items-center justify-between flex-row text-4.5 font-semibold leading-6">
            <div class="flex flex-none">
              {{ $t("withdrawal.amount-credited") }}
            </div>
            <div class="flex flex-none">
              {{ willBeEnrolledAmount.toFixed(2) }} {{ IsoCodeNames.USDT }}
            </div>
          </div>
        </div>

        <div class="flex">
          <UIButton
            :is-loading="isLoadingForm"
            class="w-full"
            color="black"
            @click="submit">
            {{ $t("withdrawal.btn-send") }}
          </UIButton>
        </div>
      </div>

      <!-- Success view -->
      <TransferCompleted
        v-if="withrawalView === 'success'"
        :style="{ height: contentHeight - 35 + 'px' }"
        :is-speed-up-available="false"
        :to-currency="IsoCodeNames.USDT"
        :to-amount="form.amount"
        @close="emit('close')">
        <template #title>{{ $t("withdrawal.request-submitted") }}</template>
        <template #from>{{ form.wallet }}</template>
        <template #delay>{{ $t("withdrawal.delay") }}</template>
      </TransferCompleted>
    </template>
  </UISideModal>
</template>

<style scoped>
.input-title {
  @apply text-fg-secondary truncate text-4 leading-5;
}
</style>
