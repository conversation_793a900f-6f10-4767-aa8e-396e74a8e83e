import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import { action } from "@storybook/addon-actions";
import { ref } from "vue";
import UIPasswordInput from "@/components/ui/UIPasswordInput/UIPasswordInput.vue";
import BitcoinSign from "@/assets/svg/icon/bitcoin-sign.svg";
import type { ComponentProps } from "vue-component-type-helpers";
import type { UIPasswordInputProps } from "@/components/ui/UIPasswordInput/types";

const meta: Meta<ComponentProps<UIPasswordInputProps>> = {
  title: "UI/UIPasswordInput",
  component: UIPasswordInput,
  tags: ["autodocs"],
  argTypes: {
    modelModifiers: {
      control: false,
    },
    error: {
      control: { type: "text" },
    },
    size: {
      control: { type: "radio" },
      options: ["s", "m", "l"],
    },
    disabled: {
      control: { type: "boolean" },
    },
    readonly: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<UIPasswordInputProps>;

export const Default: Story = {
  args: {
    size: "m",
    placeholder: "Enter your password",
    label: "Password",
    maxLength: 255,
    disabled: false,
    readonly: false,
  },
  render: (args) => ({
    components: { UIPasswordInput },
    setup() {
      const actionTextClickHandler = action("Click on action text");
      const passwordValue = ref("yourPassword");
      return {
        args,
        passwordValue,
        actionTextClickHandler,
      };
    },
    template: `
      <section class="flex flex-wrap gap-12 mb-12"> 
        <div>
          <UIPasswordInput
            class="w-[250px]"
            v-bind="args"
            v-model="passwordValue">
            <template #action>
              <button
                class="text-fg-blue hover:text-blue-500"
                @click="actionTextClickHandler">
                Action Slot
              </button>
            </template>
          </UIPasswordInput>
          <div class="mt-1">input value: {{ passwordValue }}</div>
        </div>
      </section>
    `,
  }),
};

export const PasswordInputSizes: Story = {
  args: {
    label: "Password",
    placeholder: "Enter your password",
  },
  render: () => ({
    components: { UIPasswordInput },
    setup(args) {
      const passwordValue = ref("yourPassword");
      return { args, passwordValue };
    },
    template: `
      <section class="flex flex-wrap gap-12">
        <UIPasswordInput
          class="w-[250px]"
          v-bind="args"
          v-model="passwordValue"
          size="s" />
        <UIPasswordInput
          class="w-[250px]"
          v-bind="args"
          v-model="passwordValue"
          size="m" />
        <UIPasswordInput
          class="w-[250px]"
          v-bind="args"
          v-model="passwordValue"
          size="l" />
      </section>
    `,
  }),
};

export const WithIcon: Story = {
  // @ts-ignore
  render: () => ({
    components: { UIPasswordInput, BitcoinSign },
    setup() {
      const passwordValue = ref("yourPassword");
      return { passwordValue };
    },
    template: `
      <section class="flex flex-wrap gap-12">
        <UIPasswordInput
          class="w-[250px]"
          v-model="passwordValue"
          label="Password"
          placeholder="Enter your password"
          size="m">
          <template #leftIcon>
            <BitcoinSign class="w-6 h-auto text-fg-tertiary"/>
          </template>
        </UIPasswordInput>
      </section>
    `,
  }),
};
