<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { prepareAccountBalance } from "@/helpers";

interface Props {
  label?: string;
  size?: "small" | "default";
  amount?: number | String;
  currency?: string;
  isoCode?: string;
  copy?: boolean;
}
const { t } = useI18n();
const props = defineProps<Props>();
const isCopied = ref<boolean>(false);
const onCopyHandler = () => {
  navigator.clipboard
    .writeText(props.amount ? String(props.amount) : "0")
    .catch((ex) =>
      console.warn(
        "transactionAmount->copyAmountValue->navigator.clipboard error handled: ",
        ex
      )
    );
  isCopied.value = true;
  onLeaveCopy();
};
const onLeaveCopy = () => {
  setTimeout(() => {
    isCopied.value = false;
  }, 900);
};
</script>
<template>
  <div
    :class="[
      amountTransaction.root,
      amountTransaction[props.size || 'default'],
    ]">
    <span>
      {{ props.label }}
    </span>
    <div :class="amountTransaction.amount">
      <span :class="amountTransaction.value">
        {{
          prepareAccountBalance(
            Number(props.amount ?? "0"),
            props?.isoCode || "USD"
          )
        }}
        {{ props.currency }}
      </span>
      <span
        v-if="props.copy"
        v-tooltip="{
          content: !isCopied ? t('label.copy', { a: '' }) : $t('Copied'),
          distance: 8,
          delay: {
            hide: 800,
          },
        }"
        :class="amountTransaction.copy"
        @mouseleave="onLeaveCopy"
        @click="onCopyHandler">
        <DynamicIcon
          class="text-fg-primary"
          name="copy" />
      </span>
    </div>
  </div>
</template>

<style lang="scss" module="amountTransaction">
.root {
  @apply flex gap-2 items-center justify-between flex-wrap;
  & svg {
    @apply w-5 h-5 min-w-[20px];
  }
}
.default {
  @apply font-semibold text-xl;
}
.small {
  @apply text-normal text-neutral-600;
}
.value {
  @apply ml-auto;
}
.amount {
  @apply flex items-center gap-2 ml-auto;
}
.copy {
  @apply cursor-pointer p-1 rounded-[8px] transition-all;
  @apply hover:bg-neutral-100;

  & > svg {
    @apply min-w-[20px] w-5 h-5;
  }
}
</style>
