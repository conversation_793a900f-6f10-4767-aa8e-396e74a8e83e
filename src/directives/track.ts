import { TrackerService } from "@/helpers/tracker/tracker.service";
import { TrackerEvent } from "@/helpers/tracker/tracker.types";

const vTrack = {
  created(el: any, binding: any) {
    if (binding.arg === "button") {
      el.addEventListener("click", async () => {
        await TrackerService.logEvent(TrackerEvent.BUTTON, binding.value);
      });
    }
  },

  mounted(el: any, binding: any) {
    if (binding.arg === "visit") {
      TrackerService.logEvent(TrackerEvent.VISIT, binding.value);
    }
  },
};

export default vTrack;
