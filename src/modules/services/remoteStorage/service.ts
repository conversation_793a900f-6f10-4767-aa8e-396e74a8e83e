import { useAxios } from "@/helpers";
import type { ERemoteStorageKeys, IRemoteStorageModel } from "./model";
import type { IRemoteStorageCardDto } from "./dto";
import type { IRemoteStorageService } from "./modelService";
import type { IServiceResponse } from "@modules/services/base/types";
import type { AxiosError } from "axios";

const baseUrl: string = "/remote-storage";

export class RemoteStorageService implements IRemoteStorageService {
  async getCard(
    key = "freeCard_data"
  ): ReturnType<IRemoteStorageService["getCard"]> {
    try {
      const res = await useAxios().post(baseUrl, { key });
      return res.data.data;
    } catch (err) {
      return undefined;
    }
  }
  async setCard(
    config: IRemoteStorageModel<
      ERemoteStorageKeys.CARD_DATA,
      IRemoteStorageCardDto
    >
  ): ReturnType<IRemoteStorageService["setCard"]> {
    try {
      const res = await useAxios().put(baseUrl, config);
      return res.data;
    } catch (err) {
      return undefined;
    }
  }

  async get(key: ERemoteStorageKeys): ReturnType<IRemoteStorageService["get"]> {
    const config = {
      key,
    };
    try {
      const res = await useAxios().post(baseUrl, config);
      return res.data.data;
    } catch (e) {
      return undefined;
    }
  }

  async set(
    key: ERemoteStorageKeys,
    value: any
  ): ReturnType<IRemoteStorageService["set"]> {
    const config = {
      key,
      value,
    };
    try {
      const res = await useAxios().put(baseUrl, config);
      return res.data.data;
    } catch (e) {
      return undefined;
    }
  }

  async setProMode(
    config: IRemoteStorageModel<ERemoteStorageKeys.IS_PRO_MODE, boolean>
  ): Promise<
    IServiceResponse<
      {
        success: boolean;
      },
      unknown
    >
  > {
    try {
      const res = await useAxios().put(baseUrl, config);
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
}

export default new RemoteStorageService();
