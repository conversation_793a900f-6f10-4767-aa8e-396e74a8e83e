<script>
import { defineAsyncComponent } from "vue";
import { shallowRef, ref, watch } from "vue";

export default {
  props: {
    name: {
      type: String,
      required: true,
    },
    path: {
      type: String,
      default: "",
    },
    fallbackName: String,
  },
  setup(props) {
    const result = shallowRef(() => null);
    const name = ref("" || props.name);

    watch(
      () => props.name,
      (newName) => {
        name.value = newName;
        result.value = defineAsyncComponent(promise);
      }
    );

    const promise = () => {
      switch (props.path) {
        case "":
          return import(`../../assets/svg/icon/${name.value}.svg`);
        case "menu":
          return import(`../../assets/svg/menu/${name.value}.svg`);
        case "cards":
          return import(`../../assets/svg/cards/${name.value}.svg`);
        case "cards-new":
          return import(`../../assets/svg/cards-new/${name.value}.svg`);
        case "cards-first":
          return import(`../../assets/svg/cards-first/${name.value}.svg`);
        case "alerts":
          return import(`../../assets/svg/alerts/${name.value}.svg`);
        case "pattern":
          return import(`../../assets/svg/pattern/${name.value}.svg`);
        case "gateway":
          return import(`../../assets/svg/gateway/${name.value}.svg`);
        case "accounts":
          return import(`../../assets/svg/accounts/${name.value}.svg`);
        case "./":
          return import(`../../assets/svg/${name.value}.svg`);
      }
    };

    result.value = defineAsyncComponent({
      loader: promise,
      loadingComponent: () => {},
      errorComponent: () => {},
      onError(error, retry, fail, attempts) {
        if (attempts !== 1) {
          return fail();
        }
        if (props.fallbackName) {
          name.value = props.fallbackName;
          retry();
        } else {
          fail();
        }
      },
    });

    return { result };
  },
};
</script>

<template>
  <component :is="result" />
</template>
