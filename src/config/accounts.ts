export const allGatewayTypes = [
  {
    key: "crypto-coin",
    title: "deposit.gateway.crypto_coin",
    items: [
      {
        key: "btc",
        title: "Bitcoin",
      },
      {
        key: "usdt-trc20",
        title: "Tether • TRC-20 (Tron)",
      },
      // {
      //   key: "usdt-erc20",
      //   title: "Tether • ERC-20",
      // },
      // {
      //   key: "usdt-bep20",
      //   title: "Tether • BEP-20",
      // },
      // {
      //   key: "usdc",
      //   title: "USD Coin",
      // },
      // {
      //   key: "busd",
      //   title: "Binance coin",
      // },
      // {
      //   key: "eth",
      //   title: "Etherium",
      // },
      // {
      //   key: "ltc",
      //   title: "Litecoin",
      // },
      // {
      //   key: "xrp",
      //   title: "Ripple",
      // },
      // {
      //   key: "doge",
      //   title: "<PERSON>ecoi<PERSON>",
      // },
      // {
      //   key: "dash",
      //   title: "Dash",
      // },
    ],
  },
  {
    key: "bank-card",
    title: "deposit.gateway.bank_card",
    items: [
      {
        key: "visa",
        title: "Visa",
      },
      {
        key: "mastercard",
        title: "Mastercard",
      },
      // {
      //   key: "american-express",
      //   title: "American Express",
      // },
      // {
      //   key: "union-pay",
      //   title: "UnionPay",
      // },
      // {
      //   key: "mir",
      //   title: "Mir",
      // },
    ],
  },
  // {
  //   key: "bank-account",
  //   title: "deposit.gateway.bank_account",
  //   items: [
  //     {
  //       key: "swift",
  //       title: "SWIFT",
  //     },
  //     {
  //       key: "ach",
  //       title: "ACH",
  //     },
  //     {
  //       key: "sepa",
  //       title: "SEPA",
  //     },
  //   ],
  // },
  // {
  //   key: "payment-system",
  //   title: "deposit.gateway.payment_system",
  //   items: [
  //     {
  //       key: "wise",
  //       title: "Wise",
  //     },
  //     {
  //       key: "capitalist",
  //       title: "Capitalist",
  //     },
  //     {
  //       key: "qiwi",
  //       title: "QIWI",
  //     },
  //     {
  //       key: "webmoney",
  //       title: "Webmoney",
  //     },
  //     {
  //       key: "paxum",
  //       title: "Paxum",
  //     },
  //     {
  //       key: "advcash",
  //       title: "Advcash",
  //     },
  //     {
  //       key: "perfect-money",
  //       title: "Perfect Money",
  //     },
  //     {
  //       key: "payeer",
  //       title: "Payeer",
  //     },
  //     {
  //       key: "skrill",
  //       title: "Skrill",
  //     },
  //   ],
  // },
];

export const allExternalGateway = [
  {
    key: "ach",
    title: "ACH payment",
  },
  {
    key: "visa",
    title: "VISA",
  },
  {
    key: "mastercard",
    title: "Mastercard",
  },
  {
    key: "wise",
    title: "Wise",
  },
  {
    key: "capitalist",
    title: "Capitalist",
  },
  {
    key: "webmoney",
    title: "Webmoney",
  },
  {
    key: "paxum",
    title: "Paxum",
  },
  {
    key: "advcash",
    title: "Advcash",
  },
  {
    key: "perfect-money",
    title: "Perfect Money",
  },
  {
    key: "payeer",
    title: "Payeer",
  },
  {
    key: "skrill",
    title: "Skrill",
  },
];

export const allCryptoGateway = [
  {
    key: "eth",
    iso_code: "ETH",
    title: "Ethereum",
  },
  {
    key: "usdterc20",
    iso_code: "USDT",
    title: "USDT (Ethereum)",
  },
  {
    key: "usdtton",
    iso_code: "USDT",
    title: "USDT (TON)",
  },
  {
    key: "bnbbsc",
    iso_code: "BSC",
    title: "Binance Coin (BSC)",
  },
  {
    key: "xrp",
    iso_code: "XRP",
    title: "Ripple",
  },
  {
    key: "trx",
    iso_code: "TRX",
    title: "TRX (Tron)",
  },
  {
    key: "bitcoincash",
    iso_code: "BCH",
    title: "Bitcon Cash",
  },
  {
    key: "usdcerc20",
    iso_code: "USDC",
    title: "USDC (Ethereum)",
  },
  {
    key: "usdctrc20",
    iso_code: "USDC",
    title: "USDC (Tether)",
  },
  {
    key: "ada",
    iso_code: "ADA",
    title: "Cardano (ADA)",
  },
  {
    key: "sol",
    iso_code: "SOL",
    title: "Solana (SOL)",
  },
  {
    key: "matic",
    iso_code: "MATIC",
    title: "Polygon",
  },
  {
    key: "busdbsc",
    iso_code: "BUSD",
    title: "Binance USD",
  },
  {
    key: "ltc",
    iso_code: "LTC",
    title: "Litecoin",
  },
  {
    key: "dash",
    iso_code: "DASH",
    title: "Dash",
  },
  {
    key: "doge",
    iso_code: "DOGE",
    title: "Doge",
  },
];

export const gatewayBankAccountTypes = [
  {
    id: 1,
    title: "deposit.gateway.bank_account.type.personal",
  },
  {
    id: 2,
    title: "deposit.gateway.bank_account.type.business",
  },
];

export const sortOrders = {
  USDT: 1,
  BTC: 2,
  USD: 3,
  EUR: 4,
};

export const fiatIso = ["USD", "EUR"];
