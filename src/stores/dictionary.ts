import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { useDictionaryAllGet } from "@/composable/API/useDictionaryAllGet";
import { useTariffGet } from "@/composable/API/useTariffGet";
import type { TypeExchangeRates } from "@/composable/useAccounts";
import type { TDictionaryAll } from "@/types/api/TDictionaryAll";
import type { TTariffResource } from "@/types/api/TTariffResource";
import type { TCurrencyResource } from "@/types/api/TCurrencyResource";
import type {
  TAccountTypes,
  TCardStatuses,
  TCardSystem,
  TContentTypes,
  TFees,
  TOauth,
  TTariffTypes,
  TTransactionStatuses,
  TVerificationStatuses,
} from "@/types/dictionary/dictionary.types";
import type { TPaymentSystemResource } from "@/types/api/TPaymentSystemResource";
import type { TLanguageResource } from "@/types/api/TLanguageResource";
import type { TCountryResource } from "@/types/api/TCountryResource";
import type { TNotifyServiceResource } from "@/types/api/TNotifyServiceResource";
import type { TNotifyTypeResource } from "@/types/api/TNotifyTypeResource";
import type { TUtmSourceResource } from "@/types/api/TUtmSourceResource";
import type { TVerificationStepResource } from "@/types/api/TVerificationStepResource";

const getDictionaries = async () => {
  const [resDictionary, resTariff] = await Promise.allSettled([
    useDictionaryAllGet(),
    useTariffGet(),
  ]);
  let dataValue: Partial<TDictionaryAll> = {};
  if (resDictionary.status === "fulfilled") {
    dataValue = resDictionary.value?.data?.value || {};
  }
  let dataTariff: Partial<TTariffResource>[] = [];
  if (resTariff.status === "fulfilled") {
    dataTariff = resTariff.value?.data?.value?.data || [];
  }

  return {
    currencies: dataValue?.currencies,
    accountTypes: dataValue?.account_types,
    verificationStatuses: dataValue?.verification_statuses,
    tariffTypes: dataValue?.tariff_types,
    cardStatuses: dataValue?.card_statuses,
    cardSystems: dataValue?.card_systems,
    contentTypes: dataValue?.content_types,
    exchangeRates: dataValue?.exchange_rates,
    paymentSystems: dataValue?.payment_systems,
    defaultCurrency: dataValue?.default_currency, //new
    transactionStatuses: dataValue?.transaction_statuses,
    languages: dataValue?.languages,
    countries: dataValue?.countries,
    fees: dataValue?.fees,
    notificationServices: dataValue?.notification_services,
    notificationTypes: dataValue?.notification_types,
    autoBuyStatuses: dataValue?.auto_buy_statuses, //new
    oauth: dataValue?.oauth,
    utmSource: dataValue?.utm_source, //new
    moneyRequestStatuses: dataValue?.money_request_statuses,
    webhookEvents: dataValue?.webhook_events, //new
    verificationSteps: dataValue?.verification_steps,
    userApiKeyAbilities: dataValue?.user_api_key_abilities,
    notificationPriorities: dataValue?.notification_priorities, //new
    tariff: dataTariff as TTariffResource[],
  };
};

type TDictionaryStoreData = {
  currencies: TCurrencyResource[];
  accountTypes: TAccountTypes;
  verificationStatuses: TVerificationStatuses;
  tariffTypes: TTariffTypes;
  cardStatuses: TCardStatuses;
  cardSystems: TCardSystem[];
  contentTypes: TContentTypes;
  exchangeRates: TypeExchangeRates | null;
  paymentSystems: TPaymentSystemResource[];
  defaultCurrency: TCurrencyResource;
  transactionStatuses: TTransactionStatuses;
  languages: TLanguageResource[];
  countries: TCountryResource[];
  fees: TFees;
  notificationServices: TNotifyServiceResource[];
  notificationTypes: TNotifyTypeResource[];
  autoBuyStatuses: string;
  oauth: TOauth;
  utmSource: TUtmSourceResource[];
  moneyRequestStatuses: [];
  webhookEvents: string[];
  verificationSteps: TVerificationStepResource[];
  userApiKeyAbilities: string[];
  notificationPriorities: string[];
  tariff: TTariffResource[];
};

type TDictionaryStore = {
  dictionary: Partial<TDictionaryStoreData> | null;
  getDictionary: () => Promise<void>;
  blockedCountriesCodes: Set<string>;
};

export const useDictionary: () => TDictionaryStore = defineStore(
  "dictionary",
  () => {
    const dictionary = ref<Partial<TDictionaryStoreData>>({});
    const getDictionary = async (): Promise<void> => {
      dictionary.value = await getDictionaries();
    };

    const blockedCountriesCodes = computed<Set<string>>(() => {
      const countries = new Set<string>();
      dictionary.value.countries?.forEach((country) => {
        if (country.block) countries.add(country.iso2);
      });
      return countries;
    });

    return { dictionary, getDictionary, blockedCountriesCodes };
  }
);
