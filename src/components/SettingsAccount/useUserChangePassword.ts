import { computed, reactive, ref } from "vue";
import { useUserConfirmationEmailGet } from "@/composable/API/useUserConfirmationEmailGet";
import { useUserSetPasswordPost } from "@/composable/API/useUserSetPasswordPost";

const modalState = reactive({
  changePassword: false,
  sendConfirmationCode: false,
  success: false,
});

const changePasswordFormData = reactive({
  password: "",
  repeatPassword: "",
  code: "",
});

const timer = reactive<{
  value: number;
  time: number;
  timer?: ReturnType<typeof setInterval>;
}>({
  value: 180,
  time: 0,
  timer: undefined,
});

export const useUserChangePassword = () => {
  const isBusy = ref(false);

  const minutes = computed(() => {
    return String(Math.floor(timer.time / 60)).padStart(2, "0");
  });

  const seconds = computed(() => {
    return String(timer.time % 60).padStart(2, "0");
  });

  const timeLeft = computed(() => {
    return `${minutes.value}:${seconds.value}`;
  });

  const setModalState = (state: boolean, modal: keyof typeof modalState) => {
    modalState[modal] = state;
  };

  const updateTimers = () => {
    timer.time = timer.value;
    timer.timer = setInterval(decrementTimer, 1000);
  };

  const decrementTimer = () => {
    if (timer.time > 0) {
      timer.time--;
      return;
    }
    clearInterval(timer.timer);
  };

  const sendConfirmationCode = async (update = true) => {
    try {
      isBusy.value = true;

      return await useUserConfirmationEmailGet();
    } finally {
      isBusy.value = false;

      if (update) {
        updateTimers();
      }
    }
  };

  const saveNewPassword = async () => {
    try {
      isBusy.value = true;

      return await useUserSetPasswordPost({
        code: changePasswordFormData.code,
        password: changePasswordFormData.password,
      });
    } finally {
      isBusy.value = false;
    }
  };

  const resetForm = () => {
    for (const key of Object.keys(changePasswordFormData)) {
      changePasswordFormData[key as keyof typeof changePasswordFormData] = "";
    }
  };

  return {
    isBusy,
    changePasswordFormData,
    timer,
    timeLeft,
    modalState,
    setModalState,
    sendConfirmationCode,
    saveNewPassword,
    resetForm,
  };
};
