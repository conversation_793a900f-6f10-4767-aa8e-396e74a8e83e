import { LocalStorageKey } from "@/constants/local_storage_key";
import { StorageSerializers, useLocalStorage } from "@vueuse/core";
import { computed, onMounted } from "vue";

type UnpaidOrder = {
  name?: string;
  amount?: string;
  uuid: string;
  dt: string;
};

/**
 * Handle unpaid orders from local storage.
 *
 * @category Composables
 */
export function useUnpaidOrder() {
  const storageOrder = useLocalStorage<UnpaidOrder | null>(
    LocalStorageKey.UNPAID_ORDER,
    null,
    {
      serializer: StorageSerializers.object,
    }
  );

  const unpaidOrder = computed(() => {
    return storageOrder.value;
  });

  const isActual = computed<boolean>(() => {
    if (!unpaidOrder.value) {
      return false;
    }

    const { dt } = unpaidOrder.value;

    const delta = Date.now() - parseInt(dt);
    const day = 1000 * 60 * 60 * 24;
    return delta < day;
  });

  const set = (order: Omit<UnpaidOrder, "dt">) => {
    storageOrder.value = {
      uuid: order.uuid,
      amount: order.amount,
      name: order.name,
      dt: String(Date.now()),
    };
  };

  const clear = () => {
    storageOrder.value = null;
  };

  onMounted(() => {
    if (!isActual.value) {
      clear();
    }
  });

  return {
    unpaidOrder,
    isActual,
    set,
    clear,
  };
}
