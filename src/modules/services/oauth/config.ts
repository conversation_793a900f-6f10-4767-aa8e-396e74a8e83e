import { useAxios } from "@/helpers/axios";
import { ServiceBase, ServiceEndpoint } from "./types";
import type { AxiosRequestConfig } from "axios";
import type { TServiceResponse } from "@modules/services/base/types";

export const oauthServiceConfig = {
  [ServiceEndpoint.GOOGLE_LINK_KEY]: {
    fetcher: async (requestConfig?: AxiosRequestConfig) => {
      const result = await useAxios().get(
        `/${ServiceBase.value}/${ServiceEndpoint.GOOGLE_LINK}`,
        requestConfig
      );

      return result.data.data;
    },
    extractError: (result: TServiceResponse): TServiceResponse => result,
  },
  [ServiceEndpoint.APPLE_LINK_KEY]: {
    fetcher: async (requestConfig?: AxiosRequestConfig) => {
      const result = await useAxios().get(
        `/${ServiceBase.value}/${ServiceEndpoint.APPLE_LINK}`,
        requestConfig
      );

      return result.data.data;
    },
    extractError: (result: TServiceResponse): TServiceResponse => result,
  },
  [ServiceEndpoint.JWT_CALLBACK_KEY]: {
    fetcher: async (requestConfig?: AxiosRequestConfig) => {
      const result = await useAxios().post(
        `/${ServiceBase.value}/${ServiceEndpoint.JWT_CALLBACK}`,
        requestConfig
      );

      return result.data;
    },
    extractError: (result: TServiceResponse): TServiceResponse => result,
  },
};
