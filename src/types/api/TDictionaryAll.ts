import type { TCurrencyResource } from "@/types/api/TCurrencyResource";

import type {
  TAccountTypes,
  TCardStatuses,
  TCardSystem,
  TContentTypes,
  TFees,
  TOauth,
  TTariffTypes,
  TTransactionStatuses,
  TVerificationStatuses,
} from "@/types/dictionary/dictionary.types";

import type { TXUR } from "@/types/api/TXUR";
import type { TypeExchangeRates } from "@/composable";
import type { TPaymentSystemResource } from "@/types/api/TPaymentSystemResource";
import type { TLanguageResource } from "@/types/api/TLanguageResource";
import type { TCountryResource } from "@/types/api/TCountryResource";
import type { TNotifyServiceResource } from "@/types/api/TNotifyServiceResource";
import type { TNotifyTypeResource } from "@/types/api/TNotifyTypeResource";
import type { TUtmSourceResource } from "@/types/api/TUtmSourceResource";
import type { TVerificationStepResource } from "@/types/api/TVerificationStepResource";

export type TDictionaryAll = {
  currencies: TCurrencyResource[];
  account_types: TAccountTypes;
  verification_statuses: TVerificationStatuses;
  tariff_types: TTariffTypes;
  card_statuses: TCardStatuses;
  card_systems: TCardSystem[];
  content_types: TContentTypes;
  exchange_rates: TypeExchangeRates | null;
  payment_systems: TPaymentSystemResource[];
  default_currency: TCurrencyResource;
  transaction_statuses: TTransactionStatuses;
  languages: TLanguageResource[];
  countries: TCountryResource[];
  fees: TFees;
  notification_services: TNotifyServiceResource[];
  notification_types: TNotifyTypeResource[];
  auto_buy_statuses: string;
  oauth: TOauth;
  utm_source: TUtmSourceResource[];
  money_request_statuses: [];
  webhook_events: string[];
  verification_steps: TVerificationStepResource[];
  user_api_key_abilities: string[];
  notification_priorities: string[];
};
