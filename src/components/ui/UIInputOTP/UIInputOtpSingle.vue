<template>
  <input
    ref="inputRef"
    :value="model"
    class="border-greyscale-300 min-w-14 h-14 font-extrabold text-h4 text-center"
    :type="inputType"
    inputmode="numeric"
    autocomplete="one-time-code"
    pattern="\d{6}"
    @input="handleOnChange" />
</template>

<script setup lang="ts">
import { useVModel } from "@vueuse/core";
import { type InputTypeHTMLAttribute, onMounted, ref, watch } from "vue";

const props = withDefaults(
  defineProps<{
    value?: string;
    focus?: boolean;
    shouldAutoFocus?: boolean;
    inputType: InputTypeHTMLAttribute;
  }>(),
  {
    value: "",
    inputType: "text",
  }
);

const model = useVModel(props, "value");

const emit = defineEmits<{
  (e: "change", value: string): void;
  (e: "update:value", value: string): void;
}>();

const inputRef = ref<HTMLInputElement>();

watch(
  () => props.focus,
  () => {
    if (props.focus) {
      inputRef.value?.focus();
      inputRef.value?.select();
    }
  }
);

onMounted(() => {
  if (props.focus && props.shouldAutoFocus) {
    inputRef.value?.focus();
  }
});

const handleOnChange = (event: Event) => {
  const inputEvent = event as InputEvent;
  const target = inputEvent.target as HTMLInputElement;
  let newValue = target.value;

  if (model.value.length > 1) {
    newValue = newValue.slice(0, 1);
  }

  emit("change", newValue);
};
</script>
