import { ref } from "vue";
import {
  defaultState,
  type TransferFormState,
  type TransferStrategy,
  type TransferStrategyInitParams,
} from "@/components/TransferModal/types";
import { useStrategyFactory } from "@/components/TransferModal/useTransferContext/factory/useStrategyFactory";

/*
 * Strategy for transfer from user accounts to user account.
 */
export const userAccountsToUserAccountStrategy: TransferStrategy = (
  params: TransferStrategyInitParams
) => {
  const state = ref<TransferFormState>({
    ...defaultState,
  });

  const { initialize, searchFrom, searchTo, loadMoreFrom, loadMoreTo } =
    useStrategyFactory(
      {
        fromWhom: "user",
        fromWhat: "accounts",
        toWhom: "user",
        toWhat: "account",
      },
      state,
      params
    );

  initialize();

  return {
    state,
    searchFrom,
    searchTo,
    loadMoreFrom,
    loadMoreTo,
  };
};
