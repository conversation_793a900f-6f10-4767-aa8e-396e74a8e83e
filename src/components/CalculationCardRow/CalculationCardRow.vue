<template>
  <div class="flex flex-row items-center justify-between space-x-2">
    <div class="flex flex-none text-fg-secondary text-4 leading-5">
      {{ $t("payments.detail.card") }}
    </div>
    <div
      class="flex flex-row flex-auto justify-end space-x-2 text-4 text-fg-primary leading-5 overflow-hidden">
      <div
        class="overflow-hidden truncate"
        :title="description">
        {{ description }}
      </div>
      <div
        class="flex flex-none bg-bg-level-0 rounded w-8 h-5 items-center justify-center shrink-0">
        <DynamicIcon
          class="h-3 w-5"
          path="gateway"
          :name="paymentSystemIcon" />
      </div>
      <div>{{ cardMaskDigits }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useUserTariff } from "@/composable";
import { computed } from "vue";

const { getUserTariffNameByTariffId } = useUserTariff();
const props = defineProps<{
  cardMask: string;
  cardDescription?: string;
  cardTariffId: number;
}>();

const description = computed(() => {
  return (
    props.cardDescription ?? getUserTariffNameByTariffId(props.cardTariffId)
  );
});

const paymentSystemIcon = computed(() => {
  return props.cardMask.startsWith("5") ? "mastercard" : "visa-v2";
});

const cardMaskDigits = computed(() => {
  return props.cardMask.slice(-4);
});
</script>
