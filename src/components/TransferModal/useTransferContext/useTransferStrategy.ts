import { STRATEGIES, type StrategyName } from "./strategies";
import type { TransferStrategyInitParams } from "@/components/TransferModal/types";

export const useTransferStrategy = (
  transferStrategyName: StrategyName,
  params: TransferStrategyInitParams
) => {
  const strategy = STRATEGIES[transferStrategyName];

  const { state, searchFrom, searchTo, loadMoreFrom, loadMoreTo } =
    strategy(params);

  return {
    state,
    searchFrom,
    searchTo,
    loadMoreFrom,
    loadMoreTo,
  };
};
