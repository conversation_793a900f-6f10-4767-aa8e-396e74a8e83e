<template>
  <div
    v-if="backdrop && showDropdown"
    class="fixed top-0 bottom-0 left-0 right-0 z-[1001]"
    :class="{ 'bg-white bg-opacity-80 backdrop-blur-sm': backdrop }"
    @click="closeDropdown" />
  <div
    v-click-outside="closeDropdown"
    class="flex flex-col h-full"
    :class="wrapperClass">
    <div
      v-if="label && labelType === 'outside'"
      :class="{
        [`${labelClass}`]: true,
        ['z-[1002]']: backdrop && showDropdown,
        ['text-neutral-900']: showDropdown && !error,
        ['text-neutral-500']: !showDropdown && !error,
        ['text-error-base']: error,
      }">
      {{ label }}
    </div>
    <div>
      <div
        tabindex="0"
        class="SelectBase bg-white flex"
        :class="{
          [`${selectClass}`]: !(searchable && showDropdown),
          ['border-neutral-900 ring-1 ring-neutral-900']:
            showDropdown && !error,
          ['border-neutral-200']: !showDropdown && !error,
          ['border-error-base']: error,
          ['z-[1002]']: backdrop && showDropdown,
          'UiSelect--disabled': disabled,
          'p-0': searchable && showDropdown,
        }"
        :style="`height:${height}px`"
        @blur="$emit('blur')"
        @click="toggleDropdown">
        <div
          v-if="label && labelType === 'inside'"
          class="absolute left-0 top-0 text-sm text-neutral-500 p-3">
          {{ label }}
        </div>

        <div
          class="selected-option flex justify-between items-center w-full"
          :class="{ hidden: searchable && showDropdown }">
          <div
            v-if="placeholder"
            class="option-text capitalize w-fit"
            :class="showDropdown ? '' : 'text-greyscale-500'">
            <template v-if="!selected">
              {{ placeholder }}
            </template>
            <slot
              v-if="selected && !searchable"
              :option="selected" />
          </div>
          <div
            v-if="searchable && !showDropdown"
            class="w-full pr-4">
            <slot
              v-if="selected"
              name="withIcon"
              :option="selected" />
          </div>
          <div
            v-if="!searchable && selected"
            class="w-full pr-4">
            <slot
              v-if="selected"
              name="withIcon"
              :option="selected" />
          </div>
          <div
            v-if="withArrow && searchable && !showDropdown"
            :class="{
              'absolute right-0 top-0 mt-1 mr-[2px]':
                labelType === 'inside' && $slots.withIcon,
            }">
            <ChevronDown
              class="w-6 h-6 transform transition-all"
              :class="{ 'rotate-180': showDropdown }" />
          </div>
          <div
            v-if="withArrow && !searchable"
            :class="{
              'absolute right-0 top-0 mt-1 mr-[2px]':
                labelType === 'inside' && $slots.withIcon,
            }">
            <ChevronDown
              class="w-6 h-6 transform transition-all"
              :class="{ 'rotate-180': showDropdown }" />
          </div>
        </div>

        <div
          v-if="searchable && showDropdown"
          class="w-full h-full">
          <UiInputSearch
            :value="search"
            should-auto-focus
            is-select
            @input="onSearch" />
        </div>

        <transition name="fade-slide-down">
          <div
            v-if="showDropdown"
            class="dropdown-options-container"
            :class="{ ['z-[1002]']: backdrop && showDropdown }"
            data-cy="dropdown_container"
            @click.stop="(event) => event.preventDefault()">
            <template v-if="!searchable">
              <div
                v-for="(option, index) in filteredOptions"
                :key="index"
                class="dropdown-options transition-all hover:bg-greyscale-100"
                :class="{ selected: option[fieldKey] === value }"
                @click="toggleDropdown">
                <div
                  class="dropdown-options--cell"
                  @click="
                    selectOption(
                      option[fieldKey] !== undefined ? option[fieldKey] : option
                    )
                  ">
                  <div class="font-extrabold text-sm">
                    <slot :option="option" />
                  </div>
                  <slot
                    name="withIcon"
                    :option="option" />
                </div>
                <UiDivider
                  v-if="divider && index !== options.length - 1"
                  class="mx-auto w-[90%]" />
              </div>
            </template>
            <template v-if="searchable && filteredOptions.length > 0">
              <div
                v-for="(option, index) in filteredOptions"
                :key="index"
                class="dropdown-options transition-all hover:bg-greyscale-100"
                :class="{ selected: option[fieldKey] === value }"
                @click="toggleDropdown">
                <div
                  class="dropdown-options--cell"
                  @click="
                    selectOption(
                      option[fieldKey] !== undefined ? option[fieldKey] : option
                    )
                  ">
                  <div class="font-extrabold text-sm">
                    <slot :option="option" />
                  </div>
                  <slot
                    name="withIcon"
                    :option="option" />
                </div>
                <UiDivider
                  v-if="divider && index !== options.length - 1"
                  class="mx-auto w-[90%]" />
              </div>
            </template>
            <template v-else>
              <slot name="404" />
            </template>

            <Loader v-if="loading" />

            <div
              class="w-5 h-5 absolute bottom-0"
              ref="intersectingTarget" />
          </div>
        </transition>
      </div>
    </div>
    <div
      v-if="error"
      class="bottom">
      <span
        v-if="error.length > 0"
        class="text-error-base font-semibold">
        {{ error }}
      </span>
    </div>
  </div>
</template>

<script>
import UiDivider from "~/components/ui/Divider/Divider";
import ChevronDown from "~/assets/svg/icon/chevron-down.svg";
import Loader from "@/components/ui/Loader/Loader";
import UiInputSearch from "../InputSearch/InputSearch";

export default {
  name: "UiSelect",
  emits: ["change", "focus", "blur", "is-intersecting", "input", "search"],
  components: { UiInputSearch, UiDivider, ChevronDown, Loader },
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    searchedOptions: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "",
    },
    label: {
      type: String,
      default: "",
    },
    labelType: {
      type: String,
      default: "outside",
    },
    labelClass: {
      type: String,
      default: "text-sm pb-1",
    },
    selectClass: {
      type: String,
      default: "py-4 pl-4",
    },
    divider: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [String, Number],
      default: "",
    },
    fieldKey: {
      type: String,
      default: "id",
    },
    withIntersecting: {
      type: Boolean,
      default: false,
    },
    withArrow: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    error: {
      type: String,
      default: null,
    },
    defaultValue: {
      type: [String, Number, Object],
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    backdrop: {
      type: Boolean,
      default: false,
    },
    searchable: {
      type: Boolean,
      default: false,
    },
    height: {
      type: [Number, String],
      default: "60",
    },
    searchValue: {
      type: [String, Number, Object],
      default: "",
    },
    wrapperClass: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showDropdown: false,
      isFocused: false,
      search: "",
    };
  },
  computed: {
    filteredOptions() {
      if (this.searchable) {
        return this.searchedOptions;
      }
      return this.options.filter((item) => item[this.fieldKey] !== this.value);
    },
    selected() {
      return this.options.find((item) => item[this.fieldKey] === this.value);
    },
  },
  watch: {
    showDropdown(isShow) {
      if (isShow && this.withIntersecting) {
        // do not repeat next tick. please
        this.$nextTick(this.initIntersecting);
      }
    },
  },
  methods: {
    initIntersecting() {
      const options = {
        root: document,
        rootMargin: "0px",
        threshold: 0.25,
      };
      const obs = new IntersectionObserver((entries) => {
        const isIntersecting = entries[0]?.isIntersecting;

        if (isIntersecting) {
          this.$emit("is-intersecting");
        }
      }, options);

      obs.observe(this.$refs.intersectingTarget);
    },
    toggleDropdown() {
      if (this.disabled && !this.showDropdown) return;

      this.showDropdown = !this.showDropdown;
      this.isFocused = !this.isFocused;
      this.$emit("search", "");
    },
    closeDropdown() {
      this.showDropdown = false;
    },
    selectOption(id) {
      this.$emit("change", id);
      this.isFocused = false;
    },
    onSearch(value) {
      this.search = value;
      this.$emit("search", value);
    },
  },
};
</script>

<style lang="scss" scoped>
.SelectBase {
  @apply relative font-granate cursor-pointer w-full rounded-base transition outline-neutral-900 border pr-4;
  .selected-option {
    @apply relative;
  }

  &__error {
    @apply absolute top-0 left-0 ml-3 -mt-3 p-1 text-sm text-error-light bg-white rounded-full;
  }

  &--disabled {
    @apply cursor-default bg-gray-50;
  }
}

.chevron-up {
  & svg {
    @apply mt-2 mb-auto;
  }
}

.dropdown-options-container {
  @apply absolute left-0 top-full bg-white w-full overflow-y-auto rounded-[10px] mt-2 max-h-[20rem] z-[1];
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 8px 16px rgba(0, 0, 0, 0.08);
}

.dropdown-options--cell {
  @apply p-4;

  &:hover {
    //@apply bg-greyscale-50;
  }
}

.dropdown-options.selected {
  .dropdown-options--cell {
    //@apply bg-greyscale-100;
  }
}

.bottom {
  @apply py-1 flex flex-col w-fit text-left text-[10px];

  & > span {
    & > a {
      @apply font-extrabold text-primary-base;
    }
  }
}
</style>
