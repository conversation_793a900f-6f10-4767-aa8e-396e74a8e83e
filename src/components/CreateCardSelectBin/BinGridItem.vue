<script lang="ts" setup>
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { Tooltip } from "floating-vue";
import type { TCardTariff } from "@/types/user/user.types";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import type { TBinInfo, TSelectedBin } from "@/components/CreateCardV2/types";
import type { TCardTariffSlug } from "@/composable";

const props = defineProps<{
  bin: TBinInfo;
  tariff: TCardTariff;
  subscription: boolean;
  cashback: number;
  subscriptionLevel?: string;
}>();

const emit = defineEmits<{
  selectBin: [v: TSelectedBin];
}>();

const { t } = useI18n();

const onSelectHandler = async () => {
  emit("selectBin", {
    bin: props.bin.bin,
    slug: props.bin.slug as TCardTariffSlug,
  });
};

const isAuto3ds = computed(() => {
  return ["542723", "517746"].includes(props.bin.bin);
});

const issueButtonTitle = computed(() => {
  if (Number(props.tariff.card_price) > 0) {
    return t("card.create.selectBin.info.btnIssueByPrice", {
      f: `${Number(props.tariff.card_price)} $`,
    });
  }

  return t("cards.free-issue");
});
</script>

<template>
  <div class="bin-grid-item">
    <template v-if="props.bin.under_maintenance">
      <Tooltip
        placement="bottom-start"
        :triggers="['hover']">
        <a>
          <div class="bin">
            <span>BIN:</span>
            <div
              v-if="props.bin.featured"
              class="flex flex-none h-full items-center">
              <DynamicIcon
                name="fire"
                class="h-4 w-4" />
            </div>
            <span>{{ props.bin.bin }}</span>
            <div class="flex flex-none h-5">
              <DynamicIcon
                name="warning_circle"
                class="h-4 w-4 flex m-auto text-fg-orange" />
            </div>
          </div>
        </a>
        <template #popper>
          <div class="max-w-[260px]">
            <span class="text-4 text-white">
              {{ $t("common.technical-work.title") }}
            </span>
            <p class="text-3.5 text-fg-tertiary">
              {{ $t("common.technical-work.descriptions") }}
            </p>
          </div>
        </template>
      </Tooltip>
    </template>
    <div
      v-else
      class="bin">
      <span>BIN:</span>
      <div
        v-if="props.bin.featured"
        class="flex flex-none h-full items-center">
        <DynamicIcon
          name="fire"
          class="h-4 w-4" />
      </div>
      <span>{{ props.bin.bin }}</span>
    </div>

    <div class="flex flex-none gap-1 mt-2">
      <div class="flex flex-none">
        <span class="bg-white rounded px-1.5 py-1 w-fit">
          <DynamicIcon
            :name="props.bin.bin.startsWith('4') ? 'visa' : 'mastercard'"
            path="gateway"
            class="w-5 h-3" />
        </span>
      </div>
      <div
        v-if="props.bin.secure || isAuto3ds"
        v-tooltip="{
          content: '3D-S Secure',
        }"
        class="badge-3ds">
        3D-S
        <span
          v-if="bin?.autoSecure || isAuto3ds"
          class="ml-1">
          Auto
        </span>
      </div>
      <div
        v-for="(icon, index) of bin?.recommended"
        :key="index"
        class="flex flex-none">
        <DynamicIcon
          v-tooltip="{
            content: icon,
          }"
          class="w-5 h-5"
          :name="`social-circle-${icon}`" />
      </div>
    </div>

    <div class="grid-cols-3 grid w-full gap-2 mt-6">
      <div class="info">
        <div class="text-lg">
          <span
            v-if="props.bin.default?.transferFee"
            class="mr-2 text-fg-tertiary line-through">
            {{ props.bin.default.transferFee }}
          </span>
          <span> {{ Number(props.tariff.fee_transaction_amount) }} $ </span>
        </div>
        <div class="text-base leading-4 text-fg-secondary">
          {{ $t("card.create.selectBin.info.transfer") }}
        </div>
      </div>
      <div class="info">
        <div class="text-lg">
          <span
            v-if="props.bin.default?.depositFee"
            class="mr-2 text-fg-tertiary line-through">
            {{ Number(props.bin.default.depositFee) }} %
          </span>
          {{ Number(props.tariff.fee_topup) }} %
        </div>
        <div class="text-base leading-4 text-fg-secondary">
          {{ $t("card.create.selectBin.info.deposit") }}
        </div>
      </div>
      <div class="info">
        <div class="text-lg">
          <span
            v-if="props.bin.blackListed"
            class="mr-2 text-fg-tertiary line-through">
            0 %
          </span>

          {{ cashback }} %
        </div>
        <div class="text-base leading-4 text-fg-secondary">
          {{ $t("card.create.selectBin.info.cashback") }}
        </div>
      </div>
    </div>
    <div
      class="flex mt-7 w-full"
      @click="onSelectHandler">
      <UIButton
        v-if="!props.bin.blackListed"
        class="w-full"
        size="s"
        color="grey-solid">
        {{ issueButtonTitle }}
      </UIButton>
      <UIButton
        v-else
        class="w-full"
        size="s"
        color="black">
        {{
          props.bin.blackListedLabel ||
          $t("card.create.selectBin.info.freeInPrivate")
        }}
      </UIButton>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bin-grid-item {
  @apply flex flex-none flex-col items-start flex-grow rounded bg-bg-level-1 p-4;

  .bin {
    @apply flex flex-none items-center rounded bg-bg-level-2 px-2 py-1 text-xl leading-6 font-medium;
  }

  .badge-3ds {
    @apply flex flex-none items-center px-2 text-sm;
  }

  .info {
    @apply flex flex-auto flex-col;
  }
}
</style>
