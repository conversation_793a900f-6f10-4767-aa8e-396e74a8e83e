/**
 * Calculates resulting amount applying commission and conversion.
 *
 * @category Helpers
 * @param {number} amount - The original amount before conversion.
 * @param {number} conversionRate - The rate at which the amount is converted.
 * @param {number} commissionPercent - The percentage of commission applied before conversion.
 * @param {number} minCommission - Minimum commission amount (same currency as amount).
 * @returns {number} The resulting amount after conversion and commission deduction.
 */
export function calcAmountWithCommissionAndConversion(
  amount: number,
  conversionRate: number,
  commissionPercent: number = 0,
  minCommission: number = 0
) {
  const amountConverted = amount * conversionRate;
  let commissionAmount = amountConverted * (commissionPercent / 100);
  if (commissionAmount < minCommission) {
    commissionAmount = minCommission;
  }
  return amountConverted - commissionAmount;
}
