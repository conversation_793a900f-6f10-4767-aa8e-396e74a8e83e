span.toogle:after {
  @apply w-6 h-6;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3E%3Cpath stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m16.95 7.05-9.9 9.9m0-9.9 9.9 9.9'/%3E%3C/svg%3E%0A");
}

.custom-ui-select {
  .UiSelect {
    margin: 0;
    padding: 3px 14px 0 14px;
    border-radius: 6px;
    .option-text {
      color: rgb(26 32 44 / var(--tw-text-opacity));
    }
  }
}

.v-select {
  .vs__dropdown-toggle {
    @apply rounded-[10px] h-10 border-bg-level-1 pr-2 pl-1 transition-all w-full bg-white;
  }
  input::placeholder {
    @apply text-fg-tertiary;
  }
  .vs__dropdown-menu {
    @apply rounded-[10px] shadow-md border-none mt-2 overflow-hidden;
    li {
      @apply hover:bg-neutral-100 py-2 px-3 hover:text-neutral-900;
    }
  }

  .vs__dropdown-option--highlight {
    @apply bg-neutral-100 text-neutral-900;
  }
}

.v-select.vs--open {
  .vs__dropdown-toggle {
    @apply ring-2 ring-neutral-900 rounded-[10px];
  }
}

.v-select_v2 {
  .v-select {
    .vs__dropdown-toggle {
      @apply rounded-[6px] h-[34px] bg-white border-[1px] border-bg-level-2;
    }
  }

  .v-select.vs--disabled {
    .vs__open-indicator {
      @apply bg-bg-level-1;
    }
    .vs__dropdown-toggle {
      @apply bg-bg-level-1;
      .vs__selected-options {
        @apply opacity-30;
        .vs__search:active, .vs__search {
          @apply bg-bg-level-1;
        }
      }
    }
  }
}
