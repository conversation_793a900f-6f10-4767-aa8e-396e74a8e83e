<script setup lang="ts">
import { ref, computed } from "vue";
import UITableV2 from "@/components/ui/UITableV2/UITableV2.vue";
import { useUserStore } from "@/stores/user";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import CreateApiKeyModal from "@/components/CreateApiKeyModal.vue";
import SavingCreatedApiKeyModal from "@/components/SavingCreatedApiKeyModal.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useUserApiKeyGet } from "@/composable/API/useUserApiKeyGet";
import type { TUserApiKeyListResource } from "@/types/api/TUserApiKeyListResource";
import type { TUITableColumn } from "@/components/ui/UITableV2/types";
import { useI18n } from "vue-i18n";
import { useDateFormattedWithLocale } from "@/composable/useDateFormattedWithLocale";
import PreviewApiKeyModal from "@/components/PreviewApiKeyModal.vue";
import ConfigureApiKeyModal from "@/components/ConfigureApiKeyModal.vue";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import UIDialog from "@/components/ui/UIDialog/UIDialog.vue";
import { useUserApiKeyDelete } from "@/composable/API/useUserApiKeyDelete";
import FormApiKeyRequest from "@/components/FormApiKeyRequest.vue";
import { useSessionStorage } from "@vueuse/core";
import { LocalStorageKey } from "@/constants/local_storage_key";
import { useRouter } from "vue-router";
import { RouteName } from "@/constants/route_name";

const userStore = useUserStore();

type TApiState =
  | "access-denied"
  | "access-allowed"
  | "pending-application"
  | "form-application"
  | undefined;

const apiKeys = ref<TUserApiKeyListResource[]>([]);
const isOpenCreateApiKeyModal = ref<boolean>(false);

const redirectToIntegration = () => {
  window.location.href = "https://api.pst.net/integration";
};

const { t } = useI18n();
const tableColumns: TUITableColumn[] = [
  {
    label: t("api-page.key-name"),
  },
  {
    label: t("api-page.access"),
  },
  {
    label: t("api-page.created-at"),
  },
];

const router = useRouter();
const close = () => router.push({ name: RouteName.SETTINGS });

const convertDateToFormat = (date: string) => {
  const formatDate = useDateFormattedWithLocale(date, "DD MMMM YYYY");
  return formatDate.value;
};

const isLoadingApiKeys = ref<boolean>(false);
const loadApiKeys = async () => {
  isLoadingApiKeys.value = true;
  apiKeys.value = [];

  const { data } = await useUserApiKeyGet();
  if (data.value?.data) {
    apiKeys.value = data.value?.data.filter(
      (item: TUserApiKeyListResource) => item.deleted_at == null
    );
  }

  isLoadingApiKeys.value = false;
};

const createApiKey = () => {
  isOpenCreateApiKeyModal.value = true;
};

const isOpenSavingCreatedApiKeyModal = ref<boolean>(false);
const isOpenPreviewApiKeyModal = ref<boolean>(false);
const isOpenConfigureApiKeyModal = ref<boolean>(false);

const createdApiKey = ref<string>("");
const selectedApiKey = ref<TUserApiKeyListResource | undefined>();

const closeCreateApiKeyModal = () => {
  isOpenCreateApiKeyModal.value = false;
};

const openPreviewApiKey = (apiKey: TUserApiKeyListResource) => {
  selectedApiKey.value = apiKey;
  isOpenPreviewApiKeyModal.value = true;
};

const closeSavingCreatedApiKeyModal = () => {
  isOpenSavingCreatedApiKeyModal.value = false;
};
const openSavingCreatedApiKeyModal = () => {
  isOpenSavingCreatedApiKeyModal.value = true;
};
const closePreviewApiKeyModal = () => {
  isOpenPreviewApiKeyModal.value = false;
};

const closeConfigureApiKeyModal = () => {
  isOpenConfigureApiKeyModal.value = false;
};
const openConfigureAPiKeyModal = () => {
  isOpenConfigureApiKeyModal.value = true;
};
const keySuccessfullyCreated = (apiKey: string): void => {
  createdApiKey.value = apiKey;
  openSavingCreatedApiKeyModal();
  loadApiKeys();
};

const successConfigureApiKeyModal = () => {
  useCallToast({ title: t("api-page.changes-was-saved") });
  loadApiKeys();
};

const isOpenDeleteApiKeyModal = ref<boolean>(false);
const openDialogDeleteApiKey = () => {
  closeConfigureApiKeyModal();
  isOpenDeleteApiKeyModal.value = true;
};
const closeDeleteApiKeyModal = () => {
  isOpenDeleteApiKeyModal.value = false;
};

const confirmDeleteApiKey = async () => {
  const { data } = await useUserApiKeyDelete(Number(selectedApiKey.value?.id));
  if (data.value?.success) {
    closeDeleteApiKeyModal();
    useCallToast({ title: t("api-page.key-deleted") });
    await loadApiKeys();
  } else {
    useCallToast({
      title: t("errors.universal-request-error"),
      options: {
        type: TOAST_TYPE.ERROR,
        id: "configure-api-key-modal",
      },
    });
  }
};

const apiAccessWasSent = useSessionStorage(
  LocalStorageKey.API_ACCESS_WAS_SENT,
  ""
);

const successFormApiKeyReq = async () => {
  apiAccessWasSent.value = "1";
};

const state = computed<TApiState>(() => {
  if (userStore?.user?.api_forbidden) {
    return "access-denied";
  }
  if (userStore?.user?.has_api_access) {
    return "access-allowed";
  } else {
    if (apiAccessWasSent.value === "1") {
      return "pending-application";
    } else {
      return "form-application";
    }
  }
});

if (userStore?.user?.has_api_access) {
  loadApiKeys();
}
</script>

<template>
  <div>
    <div class="flex flex-col">
      <div
        class="flex flex-none text-fg-primary text-7 font-bold leading-8 mb-4">
        {{ $t("api-page.api-integration") }}
      </div>
      <div class="flex flex-none text-fg-primary text-4 leading-5 mb-10">
        {{ $t("api-page.api-description") }}
      </div>

      <!-- Access denied -->
      <div
        v-if="state === 'access-denied'"
        class="text-6 text-fg-primary font-medium leading-7 text-center">
        {{ $t("api-page.not-have-access-api") }}
      </div>
      <!-- Access allowed -->
      <div
        v-else-if="state === 'access-allowed'"
        class="w-full flex flex-col">
        <!-- Actions Docs, Support -->
        <div class="flex flex-row gap-2 mb-10">
          <UIButton
            class="font-medium"
            size="m"
            color="grey-solid"
            @click="redirectToIntegration">
            {{ $t("api-page.documentation-api") }}
          </UIButton>
        </div>

        <!-- Keys not found-->
        <div
          v-if="!isLoadingApiKeys && !apiKeys.length"
          class="flex flex-col text-center">
          <div
            class="flex text-fg-primary text-7 font-bold leading-8 m-auto mb-2">
            {{ $t("api-page.creating-api-key") }}
          </div>
          <div class="flex text-fg-secondary text-4.5 leading-6 m-auto mb-7">
            {{ $t("api-page.safe-reliable-comfortable") }}
          </div>
          <div class="flex m-auto">
            <UIButton
              class="font-medium"
              size="m"
              color="black"
              @click="createApiKey">
              {{ $t("api-page.create-api-keys") }}
            </UIButton>
          </div>
        </div>
        <div
          v-else
          class="flex flex-col">
          <!-- Actions -->
          <div
            v-if="!isLoadingApiKeys"
            class="flex flex-none flex-row justify-between items-center mb-4">
            <div class="flex text-5 font-medium leading-6">
              {{ $t("api-page.api-keys") }}
            </div>
            <div class="flex">
              <UIButton
                class="w-full font-medium"
                size="s"
                color="black"
                @click="createApiKey">
                <template #left>
                  <DynamicIcon name="plus" />
                </template>
                {{ $t("api-page.add-api-key") }}
              </UIButton>
            </div>
          </div>
          <!-- Table -->
          <div class="flex flex-none">
            <UITableV2
              grid-class="grid-cols-[1fr_1fr_1fr]"
              :columns="tableColumns"
              :items="apiKeys as TUserApiKeyListResource[]"
              :is-loading="isLoadingApiKeys"
              class="w-full"
              @click-table-item="openPreviewApiKey">
              <template #item="{ item }">
                <div class="px-2 leading-10">{{ item.title }}</div>
                <div class="leading-10">
                  <span v-if="item.abilities[0] === 'read_only'">
                    {{ $t("api-page.access-reading") }}
                  </span>
                  <span v-if="item.abilities[0] === 'full_access'">
                    {{ $t("api-page.access-reading-writing") }}
                  </span>
                </div>
                <div class="leading-10">
                  {{ convertDateToFormat(item.created_at) }}
                </div>
              </template>
            </UITableV2>
          </div>
        </div>
      </div>
      <!-- Pending application -->
      <div v-else-if="state === 'pending-application'">
        <div class="flex flex-col bg-bg-level-1 p-4 rounded gap-4 w-full">
          <div class="flex text-fg-primary text-4.5 font-medium leading-6">
            {{ $t("api-page.application-accepted") }}
          </div>
          <div class="flex text-4 leading-5 text-fg-primary">
            {{ $t("api-page.application-accepted-info") }}
          </div>
        </div>
      </div>
      <!-- Form Application-->
      <div
        v-else-if="state === 'form-application'"
        class="max-w-[27.5rem]">
        <FormApiKeyRequest @success="successFormApiKeyReq" />
      </div>

      <CreateApiKeyModal
        v-if="isOpenCreateApiKeyModal"
        :is-open="isOpenCreateApiKeyModal"
        @success="keySuccessfullyCreated"
        @close="closeCreateApiKeyModal" />

      <SavingCreatedApiKeyModal
        v-if="isOpenSavingCreatedApiKeyModal"
        :is-open="isOpenSavingCreatedApiKeyModal"
        :api-key="createdApiKey"
        @close="closeSavingCreatedApiKeyModal" />

      <PreviewApiKeyModal
        v-if="isOpenPreviewApiKeyModal"
        :is-open="isOpenPreviewApiKeyModal"
        :api-key="selectedApiKey"
        @close="closePreviewApiKeyModal"
        @configure="openConfigureAPiKeyModal" />

      <ConfigureApiKeyModal
        v-if="isOpenConfigureApiKeyModal"
        :is-open="isOpenConfigureApiKeyModal"
        :api-key="selectedApiKey"
        @close="closeConfigureApiKeyModal"
        @success="successConfigureApiKeyModal"
        @delete="openDialogDeleteApiKey" />

      <UIDialog
        v-if="isOpenDeleteApiKeyModal"
        :callback-confirm="confirmDeleteApiKey"
        :callback-cancel="closeDeleteApiKeyModal"
        :is-open="isOpenDeleteApiKeyModal"
        :text="$t('api-page.action-is-irreversible')"
        :title="$t('api-page.question-really-delete-key')"
        @close="closeDeleteApiKeyModal" />
    </div>
  </div>
</template>
