<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useQuery, useMutation, useQueryClient } from "vue-query";
//KEYS FOR QUERY LIB
import {
  userTags<PERSON>ey,
  userCards<PERSON>ey,
  getUser<PERSON>ard<PERSON>ey,
} from "@/config/queryConfig";
import { callApiFunction } from "@/api";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import NewTag from "@/components/ui/Tags/NewTag.vue";
import type { ITag } from "@/types/ITag";
//i18 TRANSLATE FUNC
const { t } = useI18n();
//PROPS
const props = defineProps<{ cardId: number }>();
//DROPDOWN STATE
const dropdownActive = ref(false);
//TAG INPUT VALUE
const newTag = ref("");
//TAG INPUT ELEMENT
const newTagElem = ref();
//TAGS MAX VALUE
const tasgMaxValue = ref(4);
//QUERY LIB
const queryClient = useQueryClient();

//ERRORS
const errors = reactive({
  addTagToCardError: {
    value: false,
    message: t("tags.error.addError"),
  },
  delTagError: {
    value: false,
    message: t("tags.error.delError"),
  },
  clearErros() {
    this.addTagToCardError.value = false;
    this.delTagError.value = false;
  },
});

//GET CARD
const {
  data: card,
  isFetching: isFetchingCard,
  isSuccess: isSuccessCard,
} = useQuery({
  queryKey: [getUserCardKey],
  queryFn: () => callApiFunction("getUserCard", props.cardId!),
});
//GET USER TAGS
const { data: userTags, isSuccess: isSuccessTags } = useQuery({
  queryKey: [userTagsKey],
  queryFn: () => callApiFunction("getUserTags"),
});

//ADD TAG TO CARD
const { mutate: addTagToCard, isLoading: isLoadingAddTag } = useMutation({
  mutationFn: (params: { cardId: number; tagId: number }) => {
    return callApiFunction("putTagToCard", params.cardId, params.tagId);
  },
  onSuccess: () => {
    queryClient.invalidateQueries([userCardsKey]);
    queryClient.invalidateQueries([getUserCardKey]);
    newTag.value = "";
    errors.clearErros();
  },
  onError: () => {
    errors.addTagToCardError.value = true;
  },
});

//ADD TAG TO USER AND ADD TO CARD IF SUCCESS
const { mutate: addTagToUser } = useMutation({
  mutationFn: (name: string = "") => {
    return callApiFunction("putUserTag", name);
  },
  onSuccess: (data) => {
    queryClient.invalidateQueries([userTagsKey]);
    addTagToCard({ tagId: data.data.data.id, cardId: props.cardId! });
    dropdownActive.value = false;
    newTag.value = "";
  },
});

//SEARCH TAGS
const filterTags = computed<ITag[]>(() => {
  if (!isSuccessTags.value) {
    return;
  }
  return userTags.value.data?.data.filter(
    (item: ITag) => item.name === newTag.value
  );
});

//DELITE TAG FROM CARD
const { mutate: deleteTagFromCard, isLoading: isLoadingDeleteTag } =
  useMutation({
    mutationKey: ["deleteTag"],
    mutationFn: (params: { cardId: number; tagId: number }) => {
      return callApiFunction("deleteTagFromCard", params.cardId, params.tagId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries([userCardsKey]);
      queryClient.invalidateQueries([getUserCardKey]);
      errors.clearErros();
    },
    onError: () => {
      errors.delTagError.value = true;
    },
  });

//ENTER PRESS ON INPUT HANDLER
const addTagToUserOrCard = () => {
  if (filterTags.value.length > 0) {
    addTagToCard({
      cardId: card.value.data?.data?.id,
      tagId: filterTags.value[0].id,
    });
    newTagElem.value.blur();
  } else {
    if (newTag.value.length > 0) {
      addTagToUser(newTag.value);
      newTagElem.value.blur();
    }
  }
};
//CLOSE DROPDOWN
const closeDropdown = () => {
  if (dropdownActive.value) {
    dropdownActive.value = false;
  } else {
    return;
  }
};

//TAGS COUNT

const userTagsCount = computed(() => {
  return (
    card.value?.data?.data?.tags?.filter((item: ITag) => !item.is_system)
      .length || 0
  );
});
</script>
<template>
  <div
    class="bg-neutral-100 p-5 rounded-xl border-[1px] border-neutral-150 mt-3">
    <div class="flex ites-center justify-between">
      <h3 class="auto-refill-title text-xl font-semibold">
        {{ $t("cardDetailFull.tagsTitle") }}
      </h3>
      <span
        v-if="isSuccessCard"
        class="text-neutral-500"
        >{{ userTagsCount }} / {{ tasgMaxValue }}</span
      >
    </div>

    <div
      v-if="isSuccessCard"
      v-click-outside="closeDropdown"
      class="m mt-5 relative z-10 flex items-center">
      <input
        ref="newTagElem"
        v-model="newTag"
        type="text"
        class="h-10 pl-2 border-[1px] relative z-30 focus:ring-1 focus:ring-secondary-base focus:border-secondary-base"
        :class="{
          error: errors.addTagToCardError.value || errors.delTagError.value,
        }"
        :disabled="userTagsCount === tasgMaxValue"
        :placeholder="$t('tags.addTagNewTagPlaceholder')"
        @keyup.enter="addTagToUserOrCard"
        @focus="userTags.data?.data?.length ? (dropdownActive = true) : null"
        @blur="dropdownActive = false" />
      <DynamicIcon
        v-if="userTagsCount != tasgMaxValue"
        name="chevron-down"
        class="w-6 h-auto absolute right-2 top-2 transition-all cursor-pointer z-40"
        :class="{ 'rotate-180': dropdownActive }"
        @click="dropdownActive = !dropdownActive" />
      <Transition name="tags-dropdown">
        <div
          v-show="dropdownActive"
          class="absolute top-0 pt-11 w-full z-20">
          <ul
            class="border-[1px] border-neutral-150 bg-white rounded-sm overflow-y-scroll max-h-[245px]">
            <li
              v-if="newTag && !filterTags.length"
              class="tag-dropdown-item"
              @click="addTagToUser(newTag)">
              Add <span class="font-medium">"{{ newTag }}"</span>
            </li>
            <template v-if="!filterTags?.length">
              <li
                v-for="(item, index) in userTags.data.data"
                :key="index"
                class="tag-dropdown-item"
                @click="
                  addTagToCard({
                    cardId: props.cardId!,
                    tagId: item.id,
                  })
                ">
                {{ item.name }}
              </li>
            </template>

            <template v-if="filterTags.length">
              <li
                v-for="(item, index) in filterTags"
                :key="index"
                class="tag-dropdown-item"
                @click="
                  addTagToCard({
                    cardId: props.cardId!,
                    tagId: item.id,
                  })
                ">
                {{ item.name }}
              </li>
            </template>
          </ul>
        </div>
      </Transition>
    </div>
    <small
      v-if="errors.delTagError.value"
      class="block text-red-500 mt-2 font-medium"
      >{{ errors.delTagError.message }}</small
    >
    <small
      v-if="errors.addTagToCardError.value"
      class="block text-red-500 mt-2 font-medium"
      >{{ errors.addTagToCardError.message }}</small
    >
    <div
      v-show="!isLoadingAddTag && !isLoadingDeleteTag && !isFetchingCard"
      class="flex flex-wrap mt-5 gap-2">
      <NewTag
        v-for="tag in card?.data.data.tags"
        :key="tag.id"
        :title="tag.name"
        :with-delete-icon="!tag.is_system"
        @click="
          deleteTagFromCard({
            cardId: props.cardId!,
            tagId: tag.id,
          })
        " />
    </div>
    <div v-show="isLoadingAddTag || isLoadingDeleteTag || isFetchingCard">
      <Loader />
    </div>
  </div>
</template>
<style lang="scss">
.tag-dropdown-item {
  @apply p-3 hover:bg-neutral-150 transition-colors cursor-pointer;
}
.single-tag-item {
  @apply flex items-center px-4 py-2 gap-2 border-[1px] border-neutral-200 rounded-[34px];
}
.tags-dropdown-enter-active,
.tags-dropdown-leave-active {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.tags-dropdown-enter-from,
.tags-dropdown-leave-to {
  opacity: 0;
  transform: translateY(-5%);
}
input.error {
  @apply border-red-500 border-2;
}
</style>
