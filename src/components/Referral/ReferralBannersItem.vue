<template>
  <div :class="$style.ReferralBannersItem">
    <div :class="$style.ReferralBannersItem__title">
      <slot name="title" />
    </div>
    <div :class="$style.ReferralBannersItem__description">
      <slot name="description" />
    </div>
    <slot name="illustration" />
  </div>
</template>

<script>
export default {
  name: "ReferralBannersItem",
};
</script>

<style lang="scss" module>
.ReferralBannersItem {
  @apply relative overflow-hidden flex flex-col gap-2 p-5 rounded-2xl bg-secondary-base;

  &__title {
    @apply text-h6 font-extrabold text-greyscale-500;
  }

  &__description {
    @apply text-base text-white text-opacity-50 pr-4;
  }
}
</style>
