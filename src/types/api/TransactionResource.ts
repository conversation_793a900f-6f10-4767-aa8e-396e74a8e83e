export type TransactionResource = {
  account_iban: string;
  amount: string;
  amount_default: string;
  amount_original?: string;
  currency_code_original?: string;
  amount_total: string;
  card_description: string | null;
  card_id: number | null;
  card_mask: string | null;
  card_tariff?: any;
  comment: string | number | null;
  country: string | number | null;
  created_at: string;
  currency_id: number;
  custom_status: string;
  deleted_at: null | string;
  description: string;
  id: number;
  merchant: any | null;
  money_request_id: number | null;
  new_balance: string;
  old_balance: string;
  processed_at: string;
  status: number;
  status_text: string | null;
  subtransactions: Array<TransactionResource>;
  target_card_id: string | null;
  tax: string;
  type: string;
  type_enum: string | null;
  user_account_id: number;
  user_id?: number;
  penalty_amount: string;
  cashback_amount: string | null;
  cashback_status: string;
};
