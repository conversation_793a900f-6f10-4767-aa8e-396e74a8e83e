type TButtonType =
  | "primary"
  | "secondary"
  | "tertiary"
  | "approve"
  | "white"
  | "reject"
  | "quaternary"
  | "gray"
  | "gray-outline"
  | "social"
  | "kyc"
  | "orange"
  | "blue"
  | "transparent"
  | "light"
  | "normal";

type TButtonSize = "medium" | "large" | "small" | "xs" | "normal";

interface IButtonProps {
  title?: string;
  iconTitle?: string;
  type?: TButtonType;
  titleClass?: string;
  size?: TButtonSize;
  disabled?: boolean;
  icon?: string;
  iconPosition?: "left" | "right";
  padding?: string;
  loading?: boolean;
}

interface IButtonCopyProps extends IButtonProps {
  value: string | number;
}

interface IButtonTimerProps extends IButtonProps {
  timeStart: Date;
  timeDuration: number;
  initPosition: "start" | "end";
}

export type { IButtonProps, IButtonCopyProps, IButtonTimerProps };
