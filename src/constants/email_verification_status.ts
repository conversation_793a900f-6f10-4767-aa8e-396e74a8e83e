export enum EmailVerificationStatus {
  WAIT = "wait",
  UNCONFIRMED = "unconfirmed",
  CONFIRMED = "confirmed",
}

interface ITooltip {
  title: string;
  subTitle?: string;
}

interface IButton {
  color: "grey-solid" | "orange-solid";
  text: string;
}

interface IEmailVerificationStatusDetails {
  tooltip: ITooltip;
  icon: string;
  button?: IButton;
}

type EmailVerificationStatusMap = {
  [key in EmailVerificationStatus]: IEmailVerificationStatusDetails;
};

export const EMAIL_VERIFICATION_STATUS: EmailVerificationStatusMap = {
  wait: {
    tooltip: {
      title: "2fa-settings.email-verification-wait.tooltip.title",
      subTitle: "2fa-settings.email-verification-wait.tooltip.subTitle",
    },
    icon: "time-in-circle",
  },
  unconfirmed: {
    tooltip: {
      title: "2fa-settings.email-verification-unconfirmed.tooltip.title",
      subTitle: "2fa-settings.email-verification-unconfirmed.tooltip.subTitle",
    },
    icon: "warning_circle_filled",
    button: {
      color: "orange-solid",
      text: "2fa-settings.email-verification-unconfirmed.button.text",
    },
  },
  confirmed: {
    tooltip: {
      title: "2fa-settings.email-verification-confirmed.tooltip.title",
    },
    icon: "check-in-circle",
    button: {
      color: "grey-solid",
      text: "2fa-settings.email-verification-confirmed.button.text",
    },
  },
};
