<script setup lang="ts">
import { computed, ref, watch } from "vue";
import Logo2 from "@/components/icons/Logo2.vue";
import Calculation from "@/components/Calculation/Calculation.vue";
import CalculationValueRow from "@/components/CalculationValueRow/CalculationValueRow.vue";
import AccountsAndCardsSelect from "@/components/AccountsAndCardsSelect/AccountsAndCardsSelect.vue";
import CalculationDivider from "@/components/CalculationDivider/CalculationDivider.vue";
import CalculationTotalRow from "@/components/CalculationTotalRow/CalculationTotalRow.vue";
import type { TXUR } from "@/types/api/TXUR";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { PayStatus } from "@/constants/pay_status";
import UITransition from "@/components/ui/UITransition.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import SelectLanguage from "@/components/ui/Select/SelectLanguage.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { RouteName } from "@/constants/route_name";
import { isMobile } from "@/helpers";
import LanguagesToogle from "@/components/Layout/LanguagesToogle.vue";
import { usePayViewDemoStubs } from "@/components/PayViewDemo/usePayViewDemoStubs";

const {
  order,
  arrCards,
  isFetching,
  exchangeRates: rates,
} = usePayViewDemoStubs();

const selectedCardId = ref(arrCards.value?.[0]?.user_account_id);
const isRequestProcess = ref(false);
const localOrderStatus = ref<PayStatus>(PayStatus.NEW);
const loadingFlag = ref(true);

const currentCard = computed(() =>
  arrCards.value?.find((card) => card?.user_account_id === selectedCardId.value)
);

const orderProducts = computed(() => {
  return order.value?.data?.products ?? [];
});

const orderTotalPriceNum = computed(() =>
  Number(order.value?.data?.total_price ?? "0")
);

const orderMerchantSuccessUrl = computed(
  () => order.value?.data?.merchant?.success_url
);

const orderTotalPrice = computed(() => {
  return `${orderTotalPriceNum.value.toFixed(2)} $`;
});

const orderMerchantCashback = computed(() => {
  const value = Number(
    order.value?.data?.merchant?.payer_cashback_percent ?? "0"
  );
  return `${value.toFixed(0)}%`;
});

const currentCardBalance = computed(() =>
  Number(currentCard.value?.account?.balance ?? "0")
);

const isErrorAccountNotEnoughMoney = computed(() =>
  currentCard.value
    ? orderTotalPriceNum.value > currentCardBalance.value
    : false
);

const hasActiveCards = computed(() => arrCards.value.length > 0);

const submitPay = async () => {
  isRequestProcess.value = true;

  await new Promise((resolve) => setTimeout(resolve, 3000));
  localOrderStatus.value = PayStatus.APPROVED;

  isRequestProcess.value = false;
};

const isBtnDisabled = computed(
  () => orderTotalPriceNum.value > currentCardBalance.value
);
const redirectToSuccessUrl = () => {
  const url = orderMerchantSuccessUrl.value;
  if (url) {
    window.location.href = `${url}`;
  }
};

watch(isFetching, () => {
  if (loadingFlag.value) {
    loadingFlag.value = false;
  }
});
</script>

<template>
  <div class="flex flex-col w-full h-full">
    <UITransition name="fade-slide-up">
      <Loader
        v-if="isFetching || loadingFlag"
        class="h-full" />
      <div
        v-else-if="localOrderStatus === PayStatus.NEW"
        class="h-full">
        <div class="w-full h-full flex flex-col bg-white">
          <div class="w-full pay-view__header">
            <RouterLink
              :to="{ name: RouteName.DASHBOARD }"
              class="pay-view__logo">
              <DynamicIcon
                v-if="isMobile"
                name="pst-logo"
                path="./"
                class="w-28" />
              <Logo2 v-else />
            </RouterLink>
            <div class="pay-view__user">
              <LanguagesToogle class="locale-toggler" />
            </div>
          </div>

          <div
            v-if="order?.data"
            class="order-wrapper">
            <div class="flex flex-col items-center text-center mb-8">
              <div class="size-[4.625rem] rounded-full overflow-hidden mb-3">
                <img
                  class="size-full"
                  :src="order.data.merchant.logo"
                  :alt="order.data.merchant.name"
                  :title="order.data.merchant.name" />
              </div>

              <span class="text-5 leading-6 mb-1">
                {{ order.data.merchant.name }}
              </span>

              <span class="text-6 leading-7 font-medium">
                {{ order.data.description }}
              </span>
            </div>

            <AccountsAndCardsSelect
              v-if="hasActiveCards"
              v-model="selectedCardId"
              class="w-full mb-8"
              :label="$t('pay.select-card')"
              :cards="arrCards"
              :tariffs="[]"
              :rates="(rates?.data as unknown) as TXUR"
              :error="
                isErrorAccountNotEnoughMoney
                  ? $t('errors.not-enough-money')
                  : ''
              " />

            <Calculation class="w-full mb-8 !pb-7">
              <CalculationValueRow
                v-for="product of orderProducts"
                :key="product.index"
                :title="`${product.name} x${product.quantity}`">
                <span
                  v-if="product.price !== product.price_without_discount"
                  class="line-through text-fg-secondary text-4 leading-5 mr-2">
                  {{
                    Number(
                      product.price_without_discount * product.quantity
                    ).toFixed(2)
                  }}
                </span>
                <span class="text-fg-primary text-4 leading-5">{{
                  Number(product.price * product.quantity).toFixed(2)
                }}</span>
              </CalculationValueRow>

              <CalculationValueRow
                v-if="orderMerchantCashback"
                :title="$t('Cashback')">
                <span
                  class="py-0.5 px-2 text-4 rounded leading-5 bg-fg-green text-white text-center"
                  >{{ orderMerchantCashback }}</span
                >
              </CalculationValueRow>

              <CalculationDivider class="!mt-7" />

              <CalculationTotalRow
                class="!mt-7"
                :title="$t('Total')"
                :value="orderTotalPrice" />
            </Calculation>

            <UIButton
              v-if="hasActiveCards"
              :is-loading="isRequestProcess"
              :disabled="isBtnDisabled"
              size="m"
              color="black"
              class="w-full mb-8"
              @click="submitPay">
              {{ $t("Confirm") }}
            </UIButton>

            <SelectLanguage class="locale-select" />
          </div>
        </div>
      </div>
      <div
        v-else-if="localOrderStatus === PayStatus.APPROVED"
        class="flex flex-col w-full h-full justify-center">
        <div
          class="flex m-auto flex-col space-y-10 justify-center text-center items-center max-w-[27.5rem] w-full">
          <div
            class="flex flex-none bg-bg-green-solid rounded-full p-4 w-14 h-14">
            <DynamicIcon
              name="check"
              class="text-white w-6 h-6" />
          </div>
          <div class="flex text-fg-primary text-7 font-semibold leading-8">
            {{ $t("pay.payment-complete") }}
          </div>
          <div class="flex w-full">
            <UIButton
              size="m"
              color="black"
              class="w-full"
              @click="redirectToSuccessUrl"
              >{{ $t("pay.btn-back-to-store") }}</UIButton
            >
          </div>
        </div>
      </div>
    </UITransition>
  </div>
</template>

<style lang="scss" scoped>
.pay-view {
  &__header {
    @apply flex justify-between items-center h-17 md:h-24 mb-19 md:grid;

    grid-template-columns: 2.75rem 1fr 2.75rem;

    grid-template-areas: ". Logo User";
  }

  &__logo {
    @apply w-[10.563rem] md:m-auto;

    grid-area: Logo;
  }

  &__user {
    grid-area: User;

    @apply flex space-x-2;
  }
}

.order-wrapper {
  @apply flex flex-col items-center max-w-[27.5rem] w-full mx-auto;
}

.locale-select {
  @apply w-full hidden md:flex;
}

.locale-toggler {
  @apply z-[1] md:hidden;

  &:deep(img) {
    width: 100%;
    height: 100%;
  }
}
</style>
