<script lang="ts" setup>
import { computed, ref } from "vue";
import ModalStep from "@/components/ui/Modal/ModalStep.vue";
import CreateCardSelectType from "@/components/CreateCardSelectType/CreateCardSelectType.vue";
import CreateCardSelectTariff from "@/components/CreateCardSelectTariff/CreateCardSelectTariff.vue";
import CreateCardSelectBin from "@/components/CreateCardSelectBin/CreateCardSelectBin.vue";
import CreateCardIssue from "@/components/CreateCardIssue/CreateCardIssue.vue";
import CreateCardUltimaIssue from "@/components/CreateCardUltimaIssue/CreateCardUltimaIssue.vue";
import CreateCardApprove from "@/components/CreateCardApprove/CreateCardApprove.vue";
import CreateCardAutoBuyPayment from "@/components/CreateCardAutoBuyPayment/CreateCardAutoBuyPayment.vue";
import CreateCardSuccess from "@/components/CreateCardSuccess/CreateCardSuccess.vue";
import UITransition from "@/components/ui/UITransition.vue";
import { RouteName } from "@/constants/route_name";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import {
  CardCreateMachineState,
  useCreateCardMachine,
} from "@/components/CreateCardV2/useCreateCardMachine";
import Loader from "@/components/ui/Loader/Loader.vue";
import eventEmitter from "@/services/EventEmitter";
import { EmitEventsNames } from "@/constants/emit_events_names";
import { TARIFF_SLUGS_NAMES_MAP } from "@/constants/tariff_slugs_names_map";
import SupportTrigger from "@/components/SupportTrigger.vue";
import { getTariffTitleBySlug } from "@/helpers/getTariffTitleBySlug";
import type { TCardType } from "@/components/CreateCardV2/types";
import {
  useCardAutoRefillPost,
  useCountrySets,
  useSubscriptionsInfo,
  useUserTariff,
} from "@/composable";
// import SecurityWarningVerification from "../SecurityWarning/SecurityWarningVerification.vue";
import SecurityWarningModalContent from "@/components/SecurityWarningModalContent/SecurityWarningModalContent.vue";
import { useUserStore } from "@/stores/user";
import { useGlobalUniversalDialog } from "../GlobalUniversalDialog/useGlobalUniversalDialog";
import type { TCardAutoRefillResource } from "@/types/api/TCardAutoRefillResource";
import type { TCardResource } from "@/types/api/TCardResource";
import { useVerificationProcess } from "@/components/VerificationProcess/useVerificationProcess";
import ScaleVerification from "@/components/VerificationProcess/process/ScaleVerification.vue";

const { t } = useI18n();
const router = useRouter();
const { subscriptionsStatus } = useSubscriptionsInfo();
const { isTeamMember } = useUserStore();
const { send, step, context, snapshot } = useCreateCardMachine();
const { updateUserTariff } = useUserTariff();
updateUserTariff();
const { openDialog, closeDialog } = useGlobalUniversalDialog();
const { isActive: haveNeedVerificationAction, issueCardGardaVerification } =
  useCountrySets();
const { nextVerificationTier } = useVerificationProcess();

const isOpen = ref<boolean>(true);

const title = computed(() => {
  if (
    [
      CardCreateMachineState.INITIALIZATION,
      CardCreateMachineState.SUCCESS_STEP,
    ].includes(step.value)
  ) {
    return "";
  }

  const tariffTitle = context.value.cardTariff
    ? getTariffTitleBySlug(context.value.cardTariff)
    : "Advertisement";

  return t(`create-card.${step.value}.title`, {
    cardType: context.value.cardType === "forAdv" ? tariffTitle : "Ultima",
  });
});

const tariffName = computed(() => {
  if (
    step.value === CardCreateMachineState.SUCCESS_STEP ||
    !context.value.cardTariff
  ) {
    return undefined;
  }
  return TARIFF_SLUGS_NAMES_MAP[context.value.cardTariff];
});

const canGoBack = computed(() => {
  return snapshot.value.can({
    type: "BACK",
  });
});

const hideScaleVerificationHeader = computed(() => {
  return (
    context.value.cardType === "ultima" &&
    issueCardGardaVerification.value &&
    nextVerificationTier.value === "scale"
  );
});

const closeHandle = () => {
  send({ type: "RESET" });
  router.push({ name: RouteName.DASHBOARD });
};

const selectCardType = (cardType: TCardType) => {
  if (!subscriptionsStatus.value && isTeamMember && cardType === "forAdv") {
    onOpenRequestPrivateFromMasterDialog();
  } else if (
    !subscriptionsStatus.value &&
    cardType === "forAdv" &&
    context.value.isAdvWithSubActive &&
    context.value.isAutoBuy
  ) {
    send({ type: "SELECT_CARD_TYPE", value: cardType });
  } else if (cardType === "forAdv") {
    router.push({
      name: RouteName.SUBSCRIPTION_TARIFF,
    });
  } else {
    send({ type: "SELECT_CARD_TYPE", value: cardType });
  }
};

const onOpenRequestPrivateFromMasterDialog = () => {
  openDialog({
    title: t("pst-private.required-private-dialog-title"),
    text: t("pst-private.required-private-dialog-text"),
    showBtnConfirm: true,
    btnConfirmText: t("OK"),
    showBtnCancel: false,
    withCloseIcon: true,
    callbackConfirm: closeDialog,
  });
};

const autoRefillInfo = ref<TCardAutoRefillResource | null>(null);

const enableAutoRefill = async () => {
  if (context.value.resultCard) {
    const { data: autoRefillData } = await useCardAutoRefillPost({
      card_id: context.value.resultCard.id,
      minimum_balance: "50",
      amount_refill: "50",
    });
    if (autoRefillData.value?.data) {
      autoRefillInfo.value = autoRefillData.value.data;
    }
  }
};

const handleSingleBuySuccess = (resultCard: TCardResource) => {
  send({ type: "SINGLE_BUY_SUCCESS", value: resultCard });
  enableAutoRefill();
};

const confirmHandle = () => {
  isOpen.value = false;

  if (context.value.resultCard) {
    eventEmitter.emit(EmitEventsNames.MODALS_CARD_SETTING, {
      cardDetail: {
        ...context.value.resultCard,
        auto_refill: {
          id: autoRefillInfo.value?.id,
          active: true,
          minimum_balance: autoRefillInfo.value?.minimum_balance,
          amount_refill: autoRefillInfo.value?.amount_refill,
        },
      },
      onClose: closeHandle,
    });
  } else {
    closeHandle();
  }
};
</script>

<template>
  <ModalStep
    v-if="isOpen"
    :num-steps="context.numSteps"
    :show-back-button="canGoBack"
    :step="context.step"
    :subtitle="tariffName"
    :title="title"
    @close="closeHandle"
    @prev-step="send({ type: 'BACK' })">
    <template #support-widget>
      <SupportTrigger />
    </template>
    <div class="pt-0 max-w-[73.75rem] mx-auto">
      <pre class="text-sm">
        step: {{ step }}
        context: {{ context }}
      </pre>
      <UITransition :name="'fade-slide-down'">
        <div v-if="step === CardCreateMachineState.INITIALIZATION">
          <Loader />
        </div>
        <div v-if="step === CardCreateMachineState.SELECT_CARD_TYPE_STEP">
          <CreateCardSelectType @select-card-type="selectCardType" />
        </div>
        <div v-if="step === CardCreateMachineState.VERIFICATION_STEP">
          <SecurityWarningModalContent
            v-if="!hideScaleVerificationHeader"
            :custom-cases="
              context.cardType === 'forAdv' && haveNeedVerificationAction
            "
            @completed="send({ type: 'VERIFICATION_COMPLETED' })" />
          <ScaleVerification
            v-if="hideScaleVerificationHeader"
            hide-header
            @success="send({ type: 'VERIFICATION_COMPLETED' })" />
          <!-- <SecurityWarningVerification -->
          <!--   v-if="isExtendedVerificationCountry || is47Active" -->
          <!--   @completed="send({ type: 'VERIFICATION_COMPLETED' })" /> -->
        </div>
        <div v-if="step === CardCreateMachineState.SELECT_CARD_TARIFF_STEP">
          <CreateCardSelectTariff
            :issue-mode="true"
            :show-ultima="context.cardType === null"
            class="pb-10"
            @select-card-tariff="
              (tariff) => send({ type: 'SELECT_CARD_TARIFF', value: tariff })
            " />
        </div>
        <div
          v-if="
            step === CardCreateMachineState.SELECT_BIN_STEP &&
            context.cardTariff
          ">
          <CreateCardSelectBin
            :slug="context.cardTariff"
            :is-adv-with-sub-active="context.isAdvWithSubActive"
            :is-autobuy="context.isAutoBuy"
            @select-bin="
              (bin) => send({ type: 'SELECT_CARD_BIN', value: bin })
            " />
        </div>
        <div
          v-if="
            // prettier-ignore
            step === CardCreateMachineState.ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE_STEP &&
            context.cardTariff
          ">
          <CreateCardUltimaIssue
            :card-tariff-slug="context.cardTariff"
            :is-auto-buy="context.isAutoBuy"
            @select-card-tariff="
              (tariff) => send({ type: 'SELECT_CARD_TARIFF', value: tariff })
            "
            @set-promo-code="
              (promoCodeData) =>
                send({ type: 'SET_PROMO_CODE', value: promoCodeData ?? null })
            "
            @issue-card="
              (cardForIssue) =>
                send({
                  type: 'ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE',
                  value: cardForIssue,
                })
            " />
        </div>
        <div
          v-if="
            step === CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP &&
            context.cardTariff &&
            context.selectedBin
          ">
          <CreateCardIssue
            :bin="context.selectedBin"
            :card-tariff-slug="context.cardTariff"
            :is-auto-buy="context.isAutoBuy"
            @set-promo-code="
              (promoCodeData) =>
                send({ type: 'SET_PROMO_CODE', value: promoCodeData ?? null })
            "
            @issue-card="
              (cardForIssue) =>
                send({ type: 'DEFAULT_CARD_ISSUE', value: cardForIssue })
            " />
        </div>
        <div
          v-if="
            step === CardCreateMachineState.APPROVE_CARD_STEP &&
            context.cardForIssue
          ">
          <CreateCardApprove
            :card-for-issue="context.cardForIssue"
            :promo-code-data="context.promoCodeData"
            @single-buy-success="handleSingleBuySuccess"
            @multi-buy-success="send({ type: 'MULTI_BUY_SUCCESS' })" />
        </div>
        <div
          v-if="
            step === CardCreateMachineState.AUTO_BUY_PAYMENT_STEP &&
            context.cardForIssue
          ">
          <CreateCardAutoBuyPayment
            :card-for-issue="context.cardForIssue"
            :promo-code-data="context.promoCodeData"
            @auto-buy-success="send({ type: 'AUTO_BUY_SUCCESS' })" />
        </div>
        <div v-if="step === CardCreateMachineState.SUCCESS_STEP">
          <CreateCardSuccess @confirm="confirmHandle" />
        </div>
      </UITransition>
    </div>
  </ModalStep>
</template>
