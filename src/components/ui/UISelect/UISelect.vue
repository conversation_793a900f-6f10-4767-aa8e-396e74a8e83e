<script generic="T" lang="ts" setup>
import { computed, ref, toRef, watch } from "vue";
import { Skeletor } from "vue-skeletor";
import {
  createReusableTemplate,
  useIntersectionObserver,
  useScrollLock,
  watchIgnorable,
} from "@vueuse/core";
import { useAutoAnimate } from "@formkit/auto-animate/vue";
import type { TUISelectOption, TUISelectProps } from "./types";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import Simplebar from "simplebar-vue";
import UIPopperDropdown from "@/components/ui/UIPopperDropdown/UIPopperDropdown.vue";
import UISearchInput from "@/components/ui/UISearchInput/UISearchInput.vue";
import UiLoader from "@/components/ui/Loader/Loader.vue";
import UISideModalContent from "@/components/ui/UISideModalContent/UISideModalContent.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { clone } from "lodash";
import type { PopperWrapper } from "floating-vue";
import { useI18n } from "vue-i18n";

const props = withDefaults(defineProps<TUISelectProps<T>>(), {
  size: "s",
  cleared: true,
  searchDebounce: 0,
  fitDropdown: true,
  fullscreenTitle: "",
});

const emit = defineEmits<{
  optionsEndIntersection: [value: boolean];
  clearSelectedValue: [];
}>();

defineSlots<{
  selectedOption(slotProps: {
    option?: TUISelectOption<T>;
    deleteOption?: (v: string) => any;
  }): any;
  selectedValueContainer: (props: any) => any;
  label: () => any;
  option: (slotProps: {
    option: TUISelectOption<T>;
    handleSelectOption: (v: TUISelectOption<T>) => void;
    selected: boolean;
  }) => any;
  group: (slotProps: { label: string; options: TUISelectOption<T>[] }) => any;
  reset: (props: any) => any;
  "modal-footer": () => any;
}>();

const { t } = useI18n();

const optionsRef = toRef(props, "options");

const popperDropdownRef = ref<InstanceType<typeof PopperWrapper> | null>();

const selectedValueModel = defineModel<
  Array<string | number> | string | number | null
>({
  default: [],
});
const selectedValue = ref<Array<number | string> | number | string | null>(
  clone(selectedValueModel.value)
);

const selectedOption = ref<
  TUISelectOption<T>[] | TUISelectOption<T> | undefined
>();

const isShowOptionsList = ref(false);
const optionsEndTarget = ref(null);
const isOptionsEndIntersection = ref(false);

const inputSearch = ref<string>("");
const inputSearchRemote = ref<string>("");
const [optionsList] = useAutoAnimate({ duration: 200 });
const [errorText] = useAutoAnimate({ duration: 200 });

const isLockedBodyScroll = useScrollLock(document.body);

const [DefinePopperTemplate, PopperTemplate] = createReusableTemplate();

const isDisabled = computed(() => {
  return props.disabled || props.isLoading || props.readonly;
});

const optionsSimple = computed(() => {
  if (inputSearch.value) {
    return optionsListSearch.value;
  } else {
    return props.options;
  }
});

const optionsGrouped = computed<Record<string, TUISelectOption<T>[]>>(() => {
  if (inputSearch.value) {
    return optionsListGroupedSearch.value;
  } else {
    return optionsListGrouped.value;
  }
});

const isNotFoundOptions = computed(() => {
  return Boolean(
    (inputSearch.value && Object.keys(optionsListGroupedSearch).length === 0) ||
      (inputSearch.value && optionsListSearch.value?.length === 0) ||
      (inputSearchRemote.value && props.options?.length === 0)
  );
});

const optionsListSearch = computed(() => {
  const reg = new RegExp(`${inputSearch.value}`, "i");

  return props.options.filter((item: TUISelectOption) => {
    return reg.test(item?.label);
  });
});

const optionsListGrouped = computed(() => {
  return groupBy(props.options, "groupName");
});

const isShowPlaceholder = computed(() => {
  if (Array.isArray(selectedValue.value)) {
    return Boolean(selectedValue.value.length);
  } else {
    return Boolean(selectedValue.value);
  }
});

const optionsListGroupedSearch = computed(() => {
  return groupBy(optionsListSearch.value, "groupName");
});

const hasSelectedOptions = computed(() => {
  if (Array.isArray(selectedValue.value)) {
    return Boolean(selectedValue.value.length);
  } else {
    return Boolean(selectedValue.value);
  }
});

const isOptionsEmpty = computed(() => {
  if (Array.isArray(props.options)) {
    return props.options.length === 0;
  } else {
    return !props.options;
  }
});

const selectedValueEqual = (value: number | string): boolean => {
  if (Array.isArray(selectedValue.value)) {
    return selectedValue.value.some((item) => item == value);
  } else {
    return selectedValue.value == value;
  }
};

const findOption = (v: string | number | null) => {
  return props.options?.find((el) => el.value == v);
};

const findOptions = (v: (string | number)[]) => {
  return props.options.filter((el) =>
    v.map((el) => String(el)).includes(String(el.value))
  );
};

const addSelectedValue = (value: string | number) => {
  if (Array.isArray(selectedValue.value) && selectedValue.value) {
    (selectedValue.value as Array<string | number>).push(value);
  } else {
    selectedValue.value = value;
  }
};

const displayOptionLabel = (v: string | number) => {
  const opt = props.options.find((el) => el.value == v);

  if (!opt) {
    return v;
  } else {
    return opt?.label;
  }
};

const handleSelectOption = (option: TUISelectOption<T>, hide: () => void) => {
  if (option.disabled) {
    return;
  }

  if (
    !Array.isArray(selectedValue.value) &&
    String(option.value) === String(selectedValue.value)
  ) {
    hide();

    return;
  }

  if (props.multiple && Array.isArray(selectedValue.value)) {
    if (selectedValueEqual(option.value)) {
      if (props.checkedOption) {
        selectedValue.value = selectedValue.value?.filter(
          (el) => el != option.value
        );
      } else {
        hide();
      }
    } else {
      addSelectedValue(option.value);

      if (!props.checkedOption) {
        hide();
        inputSearch.value = "";
        inputSearchRemote.value = "";
      }
    }
  } else {
    hide();
    if (selectedValue.value != option?.value) {
      inputSearch.value = "";
      inputSearchRemote.value = "";
      addSelectedValue(option.value);
    }
  }
  if (!props.isFullscreen) {
    ignoreModelUpdate(() => {
      selectedValueModel.value = clone(selectedValue.value);
    });
  }
};

const { ignoreUpdates: ignoreModelUpdate } = watchIgnorable(
  selectedValueModel,
  (value) => {
    selectedValue.value = clone(value);
  },
  { deep: true }
);

const deleteOptionHandler = (value: string | number) => {
  if (props.multiple) {
    selectedValue.value = (
      selectedValue.value as Array<string | number>
    )?.filter((el) => el != value);
  }
};

const groupBy = (items: any, key: "groupName") => {
  return items.reduce((result: any, item: any) => {
    return {
      ...result,
      [item[key]]: [...(result[item[key]] || []), item],
    };
  }, {});
};

const clearSelectHandler = () => {
  if (props.multiple) {
    selectedValue.value = [];
  } else {
    selectedValue.value = null;
  }
  isShowOptionsList.value = false;
  emit("clearSelectedValue");
};

const showDropdownHandle = () => {
  isShowOptionsList.value = true;
};

const hideDropdownHandle = () => {
  isShowOptionsList.value = false;
  inputSearchRemote.value = "";
  inputSearch.value = "";
};

const hideFullscreenHandle = () => {
  hideDropdownHandle();
  selectedValue.value = clone(selectedValueModel.value);
  popperDropdownRef.value?.hide();
};

const resetFullscreenHandle = () => {
  selectedValueModel.value = props.multiple ? [] : null;
  isShowOptionsList.value = false;
  popperDropdownRef.value?.hide();
  emit("clearSelectedValue");
};

const confirmFullscreenHandle = () => {
  selectedValueModel.value = clone(selectedValue.value);
  popperDropdownRef.value?.hide();
  isShowOptionsList.value = false;
};

useIntersectionObserver(optionsEndTarget, ([{ isIntersecting }]) => {
  isOptionsEndIntersection.value = isIntersecting;
  emit("optionsEndIntersection", isOptionsEndIntersection.value);
});

watch(
  [isShowOptionsList, () => props.isFullscreen],
  ([showList, isFullscreen]) => {
    if (isFullscreen) {
      isLockedBodyScroll.value = showList;
    } else if (isLockedBodyScroll.value) {
      isLockedBodyScroll.value = false;
    }
  }
);

watch(() => props.isFullscreen, hideFullscreenHandle);

watch(
  selectedValue,
  (newValue) => {
    if (Array.isArray(newValue)) {
      selectedOption.value = findOptions(newValue);
    } else {
      selectedOption.value = findOption(newValue);
    }
  },
  { immediate: true, deep: true }
);

watch(
  optionsRef,
  () => {
    if (Array.isArray(selectedValue.value)) {
      const v = findOptions(selectedValue.value);
      if (v.length) {
        selectedOption.value = v;
      }
    } else {
      const v = findOption(selectedValue.value);
      if (v) {
        selectedOption.value = v;
      }
    }
  },
  { immediate: true, deep: true }
);

watch(inputSearchRemote, (newValue) => {
  if (props.searchHandler) {
    props.searchHandler(newValue);
  }
});

defineExpose({
  hideDropdownHandle,
});
</script>

<template>
  <div>
    <!--  Define reusable popper content  -->
    <DefinePopperTemplate v-slot="{ hide }">
      <div
        :class="{ 'p-1.5': !props.fitDropdown && !props.isFullscreen }"
        class="ui-select__list">
        <div
          v-if="props.searchable"
          :class="[props.fitDropdown ? 'p-1.5' : 'pb-1.5']">
          <UISearchInput
            v-if="props.searchable && !props.searchHandler"
            v-model="inputSearch"
            :debounce="props.searchDebounce"
            :placeholder="$t('uiSelectSearchValue')" />
          <UISearchInput
            v-if="props.searchable && props.searchHandler"
            v-model="inputSearchRemote"
            :debounce="props.searchDebounce"
            :placeholder="$t('uiSelectSearchValue')" />
        </div>

        <div
          :class="[
            {
              'pt-1.5': isFullscreen,
            },
            fitDropdown || isFullscreen ? 'w-full' : 'min-w-[14.25rem]',
          ]">
          <Simplebar :class="{ 'max-h-[16.25rem]': !isFullscreen }">
            <div v-if="props.isLoading">
              <Skeletor
                v-for="(_, idx) in 4"
                :key="idx"
                class="rounded mb-2 mx-2"
                height="25"></Skeletor>
            </div>

            <div v-else-if="!props.grouping">
              <slot
                :hide="hide"
                name="reset" />
              <ul
                ref="optionsList"
                :class="{
                  'w-max': Boolean(!fitDropdown && !isFullscreen),
                  'is-fullscreen': isFullscreen,
                }"
                class="flex flex-col items-stretch ui-select__options-list">
                <li
                  v-for="item in optionsSimple"
                  :key="item.value"
                  :class="{
                    'min-w-[14.25rem]': Boolean(!fitDropdown && !isFullscreen),
                  }"
                  class="ui-select__options-list__item"
                  @click="handleSelectOption(item, hide)">
                  <slot
                    :handle-select-option="
                      (option: TUISelectOption<T>) => handleSelectOption(option, hide)
                    "
                    :option="item"
                    :selected="selectedValueEqual(item.value)"
                    name="option">
                    <div
                      :class="{
                        'bg-bg-level-1': selectedValueEqual(item.value),
                      }"
                      class="ui-select__list__item">
                      <span class="truncate">{{ item?.label }}</span>
                    </div>
                  </slot>
                </li>
              </ul>
            </div>

            <div
              v-else
              ref="optionsList"
              :class="{
                'p-1.5': props.fitDropdown,
                'w-max': !props.fitDropdown,
              }">
              <slot
                :hide="hide"
                name="reset" />
              <div
                v-for="(value, key) in optionsGrouped"
                :key="key"
                class="flex flex-col items-stretch">
                <slot
                  :label="key"
                  :options="value"
                  name="group">
                  <div
                    class="flex items-center justify-start bg-bg-level-1 px-2 rounded h-8">
                    <p class="font-medium leading-none">{{ key }}</p>
                  </div>
                </slot>
                <ul
                  :class="{ 'is-fullscreen': isFullscreen }"
                  class="flex flex-col items-stretch ui-select__options-list">
                  <li
                    v-for="(item, idx) in value"
                    :key="idx"
                    :class="{
                      'min-w-[14.25rem]': !props.fitDropdown,
                    }"
                    class="ui-select__options-list__item"
                    @click="handleSelectOption(item, hide)">
                    <slot
                      :handle-select-option="
                        (option: TUISelectOption<T>) => handleSelectOption(option, hide)
                      "
                      :option="item"
                      :selected="selectedValueEqual(item.value)"
                      name="option">
                      <div
                        :class="{
                          'bg-bg-level-1': selectedValueEqual(item.value),
                        }"
                        class="ui-select__list__item rounded">
                        {{ item?.label }}
                      </div>
                    </slot>
                  </li>
                </ul>
              </div>
            </div>

            <div
              v-if="
                (isNotFoundOptions || isOptionsEmpty) && !props.isLoadingMore
              "
              class="p-3 text-center w-full text-fg-secondary">
              {{ $t("uiSelectnotFoundResult") }}
            </div>
            <UiLoader v-if="props.isLoadingMore" />
            <div ref="optionsEndTarget"></div>
          </Simplebar>
        </div>
      </div>
    </DefinePopperTemplate>

    <slot name="label">
      <p
        v-if="props.label"
        :class="[props.error ? 'text-fg-red' : 'text-fg-secondary']"
        class="mb-1">
        {{ props.label }}
      </p>
    </slot>

    <UIPopperDropdown
      ref="popperDropdownRef"
      :auto-size="props.fitDropdown"
      :positioning-disabled="isFullscreen"
      :theme="
        isFullscreen ? 'ui-popper-fade-slide-right' : 'ui-popper-dropdown'
      "
      class="ui-select__popper"
      placement="bottom-start"
      popper-class="ui-select-popper__floating"
      @hide="hideDropdownHandle"
      @show="showDropdownHandle">
      <div
        :class="{ 'pointer-events-none': isDisabled }"
        class="w-full relative">
        <div
          :class="{
            'ui-select': true,
            error: !!props.error,
            'ui-select--s': props.size === 's',
            'ui-select--m': props.size === 'm',
            'ui-select--l': props.size === 'l',
            'is-disabled': isDisabled,
            'is-fullscreen': isFullscreen,
          }"
          tabindex="0">
          <Skeletor
            v-if="isLoading && !isShowOptionsList"
            :class="{
              '!h-3': props.size === 's',
              '!h-5': props.size === 'm',
              '!h-7': props.size === 'l',
            }"
            class="!rounded" />
          <template v-else>
            <!-- selected value  -->
            <div class="overflow-hidden flex-grow">
              <slot name="selectedValueContainer">
                <span
                  v-if="!isShowPlaceholder"
                  :class="isDisabled ? 'text-fg-primary' : 'text-fg-tertiary'"
                  >{{ props.placeholder ?? $t("select-value") }}</span
                >
                <!-- multiple  -->
                <div
                  v-if="
                    props.multiple &&
                    Array.isArray(selectedOption) &&
                    selectedOption.length > 0 &&
                    props.options
                  "
                  class="flex items-center flex-wrap gap-2">
                  <div
                    v-for="item in selectedOption"
                    :key="item.value">
                    <slot
                      :delete-option="deleteOptionHandler"
                      :option="item"
                      name="selectedOption">
                      <div
                        class="bg-bg-tether px-2 rounded gap-0.5 flex items-center ui-select__selected-multiple-option">
                        {{ displayOptionLabel(item.value) }}
                        <DynamicIcon
                          class="w-4 cursor-pointer text-fg-primary"
                          name="close"
                          @click.stop="deleteOptionHandler(item.value)" />
                      </div>
                    </slot>
                  </div>
                </div>

                <!-- single  -->
                <div
                  v-if="
                    !props.multiple &&
                    !Array.isArray(selectedOption) &&
                    selectedOption &&
                    props.options
                  "
                  class="overflow-hidden">
                  <slot
                    :option="selectedOption"
                    name="selectedOption">
                    <div class="truncate">
                      {{ displayOptionLabel(selectedOption.value) }}
                    </div>
                  </slot>
                </div>
              </slot>
            </div>

            <!-- icons  -->
            <div
              v-if="!props.readonly"
              class="flex gap-1 items-center ml-2">
              <DynamicIcon
                v-if="props.cleared && hasSelectedOptions"
                class="w-5 text-fg-secondary"
                name="close"
                @click.stop="clearSelectHandler" />

              <DynamicIcon
                :class="{ 'rotate-180': isShowOptionsList }"
                class="w-4 h-auto transition-transform ui-select__chevron"
                name="chevron-down" />
            </div>
          </template>
        </div>
      </div>

      <!-- options list  -->
      <template #popper="{ hide }">
        <UISideModalContent
          v-if="isFullscreen"
          :title="fullscreenTitle"
          class="ui-select__side-modal-content"
          @close="hideFullscreenHandle">
          <template #content>
            <PopperTemplate v-bind="{ hide }" />
          </template>
          <template #footer>
            <slot
              :hide="hide"
              name="modal-footer">
              <div
                class="ui-select__side-modal-content__footer grid grid-flow-row gap-2 p-4">
                <UIButton
                  color="black"
                  @click="confirmFullscreenHandle"
                  >{{ $t("common.apply") }}
                </UIButton>
                <UIButton @click="resetFullscreenHandle">
                  {{ $t("common.reset") }}
                </UIButton>
              </div>
            </slot>
          </template>
        </UISideModalContent>
        <PopperTemplate
          v-else
          v-bind="{ hide }" />
      </template>
    </UIPopperDropdown>
    <div ref="errorText">
      <p
        v-if="props.error || props.helperText"
        :class="[props.error ? 'text-core-red-500 ' : 'text-fg-primary']"
        class="text-sm mt-1">
        {{ props.error ?? props.helperText }}
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ui-select {
  @apply relative box-border border border-fg-tertiary px-2 rounded
  cursor-pointer bg-bg-level-0 transition-all focus:border-fg-primary
  focus-visible:border-fg-primary hover:border-fg-secondary flex items-center justify-between;
}

.ui-select__options-list {
  &.is-fullscreen {
    .ui-select__options-list__item {
      &:not(:first-of-type) {
        @apply mt-1.5;
      }
    }
  }
}

.ui-select__side-modal-content {
  @apply static s:w-full;

  &__footer {
    @apply grid grid-flow-row gap-2 p-4 absolute z-6 bottom-0 w-full;
  }

  .ui-select__side-modal-content__footer {
    @apply static;
  }
}

.ui-select.is-disabled {
  @apply bg-bg-level-1;
}

.ui-select--l {
  @apply min-h-15 px-3;
}

.ui-select--m {
  @apply min-h-11;
}

.ui-select--s {
  @apply min-h-8;
}

.ui-select__chevron {
  @apply text-fg-primary;
}

.ui-select__list__item {
  @apply flex items-center flex-grow transition-colors cursor-pointer
  text-fg-primary hover:bg-bg-none-hover px-2 min-h-8;
}

.ui-select.error {
  @apply border-fg-red;
}
</style>

<style lang="scss">
.ui-select-popper__floating.v-popper__popper--no-positioning.v-popper__popper {
  background-color: transparent;

  .v-popper__wrapper {
    @apply absolute s:w-[29.5rem] right-0 w-full;
  }
}
</style>
