<script setup lang="ts">
import { openPrivateLanding } from "@/helpers/events";
import { useWindowSize } from "@vueuse/core";
const { height } = useWindowSize();
</script>

<template>
  <!--  {{ user }}-->
  <div :class="`${$style.root} ${height <= 767 ? 'lg:invisible' : ''}`">
    <div :class="$style.root__message">
      {{ $t("promo.banners.getFreeCardAside.message_1") }}
      <br class="lg:hidden" />
      {{ $t("promo.banners.getFreeCardAside.message_2") }}
    </div>
    <div
      :class="`${$style.root__hand}
      ${
        height <= 844
          ? '-mt-[32px] w-[100px] md:w-[180px] lg:max-w-[140px] mr-1 sm:mr-2 lg:mx-auto lg:-mt-[40px]'
          : '-mt-[32px] w-[100px] md:-w-[180px] lg:w-full mr-1 sm:mr-2 lg:-mr-2 lg:-mt-[26px]'
      }`">
      <img
        :src="
          $i18n.locale === 'ru'
            ? '/img/promo-get-free-card-hand-ru.png'
            : '/img/promo-get-free-card-hand.png'
        " />
    </div>
    <button @click="openPrivateLanding">
      {{ $t("promo.banners.getFreeCardAside.button") }}
    </button>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply flex flex-col bg-greyscale-200 rounded-[14px] px-6 relative min-h-[186px] justify-between overflow-hidden gap-4 py-[18px];
  @apply lg:bg-transparent lg:px-0 lg:min-h-[0px] lg:overflow-visible transition-all lg:py-0;

  &__message {
    @apply min-h-[58px] relative z-10 w-2/3 leading-snug;
    @apply text-h5 font-extrabold;
    @apply lg:w-full lg:flex lg:items-center lg:justify-center lg:py-3 lg:px-4;
    @apply lg:text-sm lg:font-semibold lg:bg-[#E7E4F3] lg:bg-contain lg:bg-no-repeat lg:rounded-base;
    animation-name: toBottom;
    animation-duration: 1s;
    animation-fill-mode: both;
    background-position: -16px 0;

    &:before {
      @apply hidden absolute top-0 left-0 -ml-4 mt-1 w-4 h-10;
      @apply lg:block;
      content: "";
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='40' fill='none' viewBox='0 0 17 42'%3E%3Cpath fill='%23E7E4F3' d='M17 7v35-.556c0-2.384-.853-4.684-2.329-6.556C12.09 31.61 7.906 25.986 5 20.5 2.103 15.03.862 6.6.347 1.325.24.244 1.789-.198 2.37.719 4.29 3.746 7.212 7.539 10.5 9 15 11 17 7.538 17 5v2Z'/%3E%3C/svg%3E%0A");
    }
  }

  &__hand {
    @apply mb-[10px] absolute right-0;
    @apply lg:relative;
    animation-name: forHand;
    animation-duration: 7s;
    animation-fill-mode: both;
    animation-delay: 1s;
  }

  button {
    @apply bg-[#536EFC] text-white text-base font-extrabold flex items-center justify-center w-fit rounded-base py-3 px-6 transition-all;
    @apply hover:bg-opacity-90;
    @apply lg:w-full;
    animation-name: toBottom;
    animation-duration: 1s;
    animation-fill-mode: both;
    animation-delay: 1.5s;
  }
}

@keyframes toBottom {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes forHand {
  0% {
    opacity: 0;
    transform: translateY(-45px) translateX(45px) rotate(-45deg);
  }
  20% {
    opacity: 1;
    transform: translateY(0) translateX(0px) rotate(0deg);
  }
  60% {
    transform: translateY(0) translateX(0px) rotate(0deg);
  }
  80% {
    transform: translateY(4px) translateX(-4px) rotate(4deg);
  }
  100% {
    transform: translateY(0) translateX(0px) rotate(0deg);
  }
}
</style>
