<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useNavigatorLanguage } from "@vueuse/core";
import { openTelegramChannel, openTwitterChannel } from "@/helpers/events";
import { computed } from "vue";
import { isMobile } from "@/helpers";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
// state
const { language } = useNavigatorLanguage();

// computed
const locale = computed<string>(() => {
  return language.value?.split("-")[0] || "en";
});
</script>

<template>
  <div class="w-full justify-between grid grid-cols-2 gap-2 p-3 bg-bg-level-1">
    <UIButton
      class="items-center"
      color="grey-solid"
      size="m"
      @click="openTelegramChannel(locale)">
      <template #left>
        <div
          class="bg-bg-telegram w-full h-full flex m-auto p-1 text-center rounded-full text-white">
          <DynamicIcon
            name="telegramV2"
            class="w-3 h-3 flex m-auto" />
        </div>
      </template>
      <template #default>
        <div v-if="!isMobile">Telegram</div>
      </template>
    </UIButton>

    <UIButton
      class="items-center"
      color="grey-solid"
      size="m"
      @click="openTwitterChannel(locale)">
      <template #left>
        <div
          class="bg-bg-telegram w-full h-full flex m-auto p-1 text-center rounded-full text-white">
          <DynamicIcon
            class="w-3 h-3 flex m-auto"
            name="x" />
        </div>
      </template>
      <template #default>
        <div v-if="!isMobile">X.com</div>
      </template>
    </UIButton>
  </div>
</template>
