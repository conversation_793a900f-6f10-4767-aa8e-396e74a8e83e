import { defineService } from "@modules/services/base";
import type { TPayloads, TResponses } from "@modules/services/referral/types";
import { ServiceEndpoint } from "@modules/services/referral/types";

const useReferralService = defineService<TResponses, TPayloads>(
  "referralServiceConfig"
);

const users = () =>
  useReferralService<
    TResponses[ServiceEndpoint.USERS],
    TPayloads[ServiceEndpoint.USERS]
  >(ServiceEndpoint.USERS);

export const ReferralService = { users };
