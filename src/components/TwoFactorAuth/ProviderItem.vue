<script setup lang="ts">
import {
  type ProviderIcon<PERSON><PERSON>,
  type ProviderItemMode,
  type ProviderName,
  providerToIconNameMap,
  providerToNameMap,
  type TwoFactorAuthProvider,
  type TwoFactorAuthState,
  type TwoFactorAuthStep,
} from "@/components/TwoFactorAuth/types";
import { computed } from "vue";
import UiToggle from "@/components/ui/UIToggle/UIToggle.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UITooltip from "@/components/ui/UITooltip/UITooltip.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const props = defineProps<{
  twoFactorAuthState: TwoFactorAuthState;
  mode: ProviderItemMode;
  provider: TwoFactorAuthProvider;
}>();

const emit = defineEmits<{
  disconnect: [provider: TwoFactorAuthProvider];
  connect: [provider: TwoFactorAuthProvider];
  toggleProvider: [provider: TwoFactor<PERSON><PERSON><PERSON>rovider];
  disableProvider: [provider: TwoFactorAuthProvider];
  enableProvider: [provider: TwoFactorAuthProvider];
  changeStep: [step: TwoFactorAuthStep];
}>();

const enabledProvider = computed<boolean>(
  () => props.twoFactorAuthState.enabledProvider === props.provider
);

const isProviderConnected = computed<boolean>(() => {
  if (props.provider === "whatsapp" && props.twoFactorAuthState.user_whatsapp) {
    return true;
  } else if (
    props.provider === "telegram" &&
    props.twoFactorAuthState.user_telegram
  ) {
    return true;
  }
  return false;
});

const providerName = computed<ProviderName>(
  () => providerToNameMap[props.provider]
);

const providerIconName = computed<ProviderIconName>(
  () => providerToIconNameMap[props.provider]
);

const subTitle = computed<string>(() => {
  if (isProviderConnected.value && props.provider === "whatsapp") {
    return "+" + props.twoFactorAuthState.user_whatsapp;
  } else if (isProviderConnected.value && props.provider === "telegram") {
    return "@" + props.twoFactorAuthState.user_telegram;
  } else {
    return t("2fa-settings.provider-not-connected");
  }
});

const disconnectProvider = () => {
  emit("disconnect", props.provider);
};

const toggleProvider = (value: boolean) => {
  if (value) {
    emit("enableProvider", props.provider);
  } else {
    emit("disableProvider", props.provider);
  }
};

const connectProvider = () => {
  emit("connect", props.provider);
};

const showTooltip = computed<boolean>(() => {
  return (
    (props.twoFactorAuthState.enabledProvider !== props.provider &&
      props.twoFactorAuthState.enabledProvider !== null) ||
    isTelegramRegistrationMethod.value
  );
});

const isTelegramRegistrationMethod = computed<boolean>(() => {
  return props.twoFactorAuthState.registration_method === "telegram";
});

const isCurrentProvider = computed<boolean>(() => {
  return props.twoFactorAuthState.registration_method === props.provider;
});

const isMessengerRegistration = computed<boolean>(() => {
  return (
    props.twoFactorAuthState.registration_method === "telegram" ||
    props.twoFactorAuthState.registration_method === "whatsapp"
  );
});
</script>

<template>
  <div class="bg-bg-level-1 rounded my-3">
    <div v-if="props.mode === 'default'">
      <div
        v-if="!isProviderConnected"
        class="flex items-center justify-between cursor-pointer p-4"
        @click="connectProvider">
        <div class="">
          <DynamicIcon
            :name="providerIconName"
            class="w-8" />
        </div>
        <div class="flex-grow mx-4">
          <p class="font-normal text-fg-primary text-xl leading-6">
            {{ providerName }}
          </p>
          <p class="font-normal text-lg leading-5 text-fg-secondary">
            {{ subTitle }}
          </p>
        </div>
        <div>
          <DynamicIcon
            name="chevron-right"
            class="w-6 h-6 text-fg-secondary" />
        </div>
      </div>
      <div
        v-if="isProviderConnected"
        class="flex items-center justify-between p-4">
        <div class="">
          <DynamicIcon
            :name="providerIconName"
            class="w-8" />
        </div>
        <div class="flex-grow mx-4">
          <p class="font-normal text-fg-primary text-xl leading-6">
            {{ providerName }}
          </p>
          <p class="font-normal text-lg leading-5 text-fg-secondary">
            {{ subTitle }}
          </p>
        </div>
        <div>
          <div v-if="showTooltip">
            <UITooltip :placement="'bottom-end'">
              <UiToggle
                :disabled="isTelegramRegistrationMethod"
                :model-value="enabledProvider"
                @update:model-value="toggleProvider" />
              <template #popper>
                <div
                  v-if="!isTelegramRegistrationMethod"
                  class="p-2.5">
                  <div class="font-normal text-fg-contrast text-lg leading-5">
                    {{ $t("2fa-settings.2fa-single-provider-limit") }}
                  </div>
                  <div class="text-fg-tertiary text-normal leading-4 mt-2">
                    {{ $t("2fa-settings.2fa-provider-switch-instruction") }}
                  </div>
                </div>
                <div
                  v-if="isTelegramRegistrationMethod"
                  class="p-2.5">
                  <div class="font-normal text-fg-contrast text-lg leading-5">
                    {{ $t("2fa-settings.telegram-registration-method-title") }}
                  </div>
                  <div class="text-fg-tertiary text-normal leading-4 mt-2">
                    {{ $t("2fa-settings.telegram-registration-method-text") }}
                  </div>
                </div>
              </template>
            </UITooltip>
          </div>
          <div v-if="!showTooltip">
            <UiToggle
              :disabled="isMessengerRegistration"
              :model-value="enabledProvider"
              @update:model-value="toggleProvider" />
          </div>
        </div>
      </div>
    </div>
    <div v-if="props.mode === 'edit'">
      <div class="flex items-center justify-between p-4">
        <div class="">
          <DynamicIcon
            :name="providerIconName"
            class="w-8" />
        </div>
        <div class="flex-grow mx-4">
          <p class="font-normal text-fg-primary text-xl leading-6">
            {{ providerName }}
          </p>
          <p class="font-normal text-lg leading-5 text-fg-secondary">
            {{ subTitle }}
          </p>
        </div>
        <div>
          <div
            v-if="isProviderConnected && !isCurrentProvider"
            class="font-medium text-fg-red text-lg leading-5 cursor-pointer"
            @click="disconnectProvider">
            {{ $t("2fa-settings.disconnect-provider") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
