import Config from "@/config/env";

const CAPTCHA_ID = "captchaScriptId";

/**
 * Injects Google reCAPTCHA script into the document body.
 * Creates a script element with configured reCAPTCHA site key from environment config.
 * @TODO should not be in helpers, refactor to composable
 *
 * @category Helpers
 * @returns {void}
 */
export function addCaptcha() {
  const captcha = document.createElement("script");
  captcha.id = CAPTCHA_ID;
  captcha.src = `https://www.google.com/recaptcha/api.js?render=${Config.grecaptcha}`;
  document.body.appendChild(captcha);
}

/**
 * Removes Google reCAPTCHA script and badge from the document.
 * Cleans up both the script tag and the reCAPTCHA badge element.
 * @TODO should not be in helpers, refactor to composable
 *
 * @category Helpers
 * @returns {void}
 *
 */
export function delCaptcha() {
  const captcha = document.getElementById(CAPTCHA_ID);
  const badge = document.querySelector(".grecaptcha-badge");

  if (!(captcha && badge)) {
    console.error("Captcha->delCaptcha error handled: Captcha not found");
    return;
  }

  captcha.remove();
  badge.remove();
}
