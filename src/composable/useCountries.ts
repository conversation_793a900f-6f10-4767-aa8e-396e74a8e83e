import { computed } from "vue";
import { useDictionary } from "@/stores/dictionary";
import type { TCountryResource } from "@/types/api/TCountryResource";
/**
 * Countries composable
 * @return {Object}
 * @example
 * import { useCountries } from "@/composable/useCountries";
 * setup() {
 *  const { countries, availableCountries, checkIsCountryAvailable } = useCountries();
 *  return { countries, availableCountries, checkIsCountryAvailable };
 *  }
 *
 * @category Composables
 */
export function useCountries() {
  const { dictionary, blockedCountriesCodes } = useDictionary();
  /**
   * Countries
   * @return {Array<TCountry> | undefined}
   * @example
   * countries.value // [{code: "RU", name: "Russia", block: false}, ...]
   */
  const countries = computed(() => {
    return dictionary?.countries;
  });

  /**
   * Available countries
   * @return {Array<TCountry> | undefined}
   * @example
   * availableCountries.value // [{code: "RU", name: "Russia", block: false}, ...]
   */
  const availableCountries = computed(() => {
    return dictionary?.countries?.filter((item: TCountryResource) =>
      countryIsAvailable(item.iso2)
    );
  });

  /**
   * Check is country available
   * @param countryCode
   * @return {boolean}
   * @example
   * countryIsAvailable("RU") // true
   */
  const countryIsAvailable = (countryCode: string): boolean => {
    return !blockedCountriesCodes.has(countryCode);
  };

  return {
    countries,
    availableCountries,
    countryIsAvailable,
    blockedCountriesCodes,
  };
}
