<template>
  <button
    v-ripple
    class="flex gap-2 cursor-pointer"
    @click="$emit('add-like')">
    <DynamicIcon
      name="like-outlined"
      class="w-5 h-5" />
    <template v-if="likeValue > 0">{{ likeValue }}</template>
  </button>
</template>

<script>
import DynamicIcon from "@/components/icons/DynamicIcon";
export default {
  name: "FaqLike",
  components: { DynamicIcon },
  props: {
    value: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    likeValue() {
      return this.value;
    },
  },
};
</script>
