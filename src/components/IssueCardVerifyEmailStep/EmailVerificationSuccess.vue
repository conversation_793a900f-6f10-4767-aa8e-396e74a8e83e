<script setup lang="ts">
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

defineEmits<{
  "issue-card": [];
  "next-verification-step": [];
}>();
</script>

<template>
  <div class="email-verification-success">
    <div class="checkmark">
      <DynamicIcon
        name="check"
        class="w-6 h-6 text-fg-contrast" />
    </div>
    <div class="text">
      <div class="font-bold text-[1.75rem] leading-8 text-center">
        {{ $t("2fa-settings.email-verification-step.title") }}
      </div>
      <div class="font-normal text-xl leading-6 text-center">
        {{ $t("2fa-settings.email-verification-step.text") }}
      </div>
    </div>
    <div class="flex flex-col items-center gap-2 w-full">
      <UIButton
        class="w-full"
        size="m"
        color="black"
        @click="$emit('issue-card')">
        {{ $t("2fa-settings.email-verification-step.issue-card-label") }}
      </UIButton>
      <UIButton
        class="w-full"
        size="m"
        color="orange-solid"
        @click="$emit('next-verification-step')">
        {{
          $t("2fa-settings.email-verification-step.verification-level-label")
        }}
      </UIButton>
    </div>
  </div>
</template>

<style scoped lang="scss">
.email-verification-success {
  @apply flex flex-col items-center gap-10;
  .checkmark {
    @apply w-14 h-14 rounded-full bg-bg-green-solid flex items-center justify-center;
  }
}
</style>
