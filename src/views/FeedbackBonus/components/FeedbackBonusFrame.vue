<script setup lang="ts">
import { ref } from "vue";
import {
  TOAST_TYPE,
  useCallToast,
  useUserTrustPilotGetLinkPost,
} from "@/composable";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const url = ref<string>("");

const getUrl = async () => {
  const { data } = await useUserTrustPilotGetLinkPost();
  if (data.value?.data?.url && data.value?.data?.url?.length) {
    url.value = data.value?.data?.url;
  } else {
    useCallToast({
      title: t("errors.universal-request-error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });
  }
};

getUrl();
</script>

<template>
  <div
    class="feedback-bonus-frame"
    @click="getUrl">
    <div
      v-if="url"
      class="frame-container">
      <iframe
        :src="url"
        class="frame" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.frame {
  @apply w-full border-none overflow-hidden;
  height: calc(100vh - 200px);
}
</style>
