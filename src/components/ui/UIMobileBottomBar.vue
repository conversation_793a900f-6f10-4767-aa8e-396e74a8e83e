<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import SupportTrigger from "@/components/SupportTrigger.vue";

const nav = [
  {
    icon: "home",
    title: "team.aside-dashboard",
    path: "/app/dashboard",
  },
  {
    icon: "card",
    title: "newTopBar.cards",
    path: "/app/cards",
  },
  {
    icon: "payments",
    title: "personal.payments",
    path: "/app/payments",
  },
];
</script>
<template>
  <div class="ui-mobile-bottom-bar">
    <router-link
      v-for="(item, idx) in nav"
      :key="idx"
      :to="item.path">
      <div class="item">
        <DynamicIcon
          class="w-4 h-4"
          :name="item.icon"
          path="menu" />
        <p>{{ $t(`${item.title}`) }}</p>
      </div>
    </router-link>

    <SupportTrigger>
      <template #button>
        <div class="item support">
          <DynamicIcon
            class="w-4 h-4"
            name="headphone"
            path="menu" />
          <p>{{ $t("Support") }}</p>
        </div>
      </template>
    </SupportTrigger>
  </div>
</template>
<style lang="scss" scoped>
.ui-mobile-bottom-bar {
  @apply w-full h-17 grid grid-cols-4 bg-white p-2 sm:hidden;
  @apply border-t border-bg-level-2;

  .item {
    @apply rounded overflow-hidden flex flex-col py-2 items-center justify-center transition-colors relative;
    svg,
    p {
      @apply text-fg-secondary;
    }

    p {
      @apply text-3.5;
    }

    &.support {
      svg,
      p {
        @apply text-fg-blue;
      }
    }
  }

  .router-link-active {
    .item {
      @apply bg-bg-level-1;

      svg,
      p {
        @apply text-fg-primary;
      }
    }
  }
}
</style>
