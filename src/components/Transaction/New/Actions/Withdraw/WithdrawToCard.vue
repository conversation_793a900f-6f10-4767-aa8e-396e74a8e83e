<script setup lang="ts">
import UiSelect from "@/components/ui/Select/Select.vue";
import UiInputSearch from "../../ui/InputSearch/InputSearch.vue";
import CardInSelect from "../../ui/CardInSelect.vue";
import TransactionConfirm from "../TransactionConfirm.vue";
import TransactionAlert from "../TransactionAlert.vue";
import CardInfo from "../../ui/CardInfo.vue";
import NoCards from "../../ui/NoCards.vue";

import { transfer as transferHelper } from "../../helpers/withdraw";

import { useCardsStore } from "@/stores/cards";
import { ref, onBeforeUnmount, computed, reactive, watch } from "vue";
import AccountInfo from "@/components/Transaction/New/ui/AccountInfo.vue";
import TransferAmount from "@/components/Transaction/New/ui/TransferAmount.vue";
import { useI18n } from "vue-i18n";
import { prepareAccountBalance } from "@/helpers";
import { useQueryClient } from "vue-query";
import { userAccountsKey } from "@/config/queryConfig";
import { TrackerService } from "@/helpers/tracker/tracker.service";
import { TrackerEvent } from "@/helpers/tracker/tracker.types";
import { UserService } from "@modules/services/user";
import { useUserStore } from "@/stores/user";
import { useState } from "@/helpers/utilities";
import Warning from "@/components/ui/Warning/Warning.vue";

const { t } = useI18n();
const { state, getCards } = useCardsStore("cards-inside-withdraw");
const isBusy = ref(true);
const [transferLoading, setTransferLoading] = useState<boolean>(false);
const tariff = ref<Array<any>>([]);
const rates = ref();
const queryClient = useQueryClient();
const userStore = useUserStore();

// props
interface Props {
  activeAccount?: any;
  activeCard?: any;
}

const props = defineProps<Props>();

// data
const activeFocus = ref<string>("");
const search = ref<string>("");
const withdrawForm = reactive<any>({
  fromAmount: 0,
  toAmount: 0,
  isConfirm: false,
  isSubmit: false,
  status: "success",
  error: "",
});

// computed
const transfer_fee = computed(() => {
  const activeCardTariff: any = tariff.value.find(
    (v: any) => v.id === cardToComputed.value.tariff_id
  );
  if (activeCardTariff === undefined) {
    return undefined;
  }
  return Number(activeCardTariff.fee_topup);
});
// TODO
const cardToComputed = computed<any>(() => {
  return state.cards.find((card: any) => card.id === state.activeCardId);
});

// TODO
const needCurrencyConversion = computed(() => {
  return (
    cardToComputed.value?.account?._currency.id !==
    props.activeAccount.currency_id
  );
});

// TODO;
const currencyConversionRate = computed(() => {
  if (!rates.value) {
    return 1;
  }

  const rate = rates.value[props.activeAccount?._currency?.iso_code];

  if (!rate) {
    return 1;
  }

  return Number(rate[cardToComputed.value?.account?._currency?.iso_code] || 1);
});

// return active account from card or from account
const activeAccount = computed(() => ({
  isCard: props.activeCard !== undefined,
  value: props.activeCard ? props.activeCard.account : props.activeAccount,
}));
// client side search
const filteredCards = computed<any>(() => {
  if (search.value.length !== 0) {
    return state.cards.filter((card: any) => {
      const searchValue = search.value.toLowerCase();
      const mask = card.mask.toLowerCase();
      const tariff = card._tariff.toLowerCase();
      const code = card.account._currency.iso_code.toLowerCase();
      const balance = card.account.balance;
      const description = card.description?.toLowerCase() || "";

      return (
        (mask.includes(searchValue) ||
          tariff.includes(searchValue) ||
          code.includes(searchValue) ||
          description.includes(searchValue) ||
          balance.includes(searchValue)) &&
        card.id !== props.activeCard?.id &&
        card.status === 1
      );
    });
  } else {
    return state.cards.filter(
      (card: any) => card.id != props.activeCard?.id && card.status === 1
    );
  }
});

// methods
// @ts-ignore
const setWithdrawForm = (v: any) => (withdrawForm[v.field] = v.payload);
const setActiveFocus = (value: string) => (activeFocus.value = value);
const setSearchValue = (v: string) => {
  search.value = v;
};

const transfer = async () => {
  setTransferLoading(true);

  await transferHelper(
    withdrawForm,
    activeAccount.value.value.id,
    cardToComputed.value.account.iban,
    undefined,
    t
  );

  await queryClient.invalidateQueries([userAccountsKey]);
  TrackerService.logEvent(TrackerEvent.CARD_TOP_UP).catch((ex) =>
    console.warn("WithdrawToCard->transfer>logEvent error handled: ", ex)
  );

  setTransferLoading(false);
};

const loadCards = async () => {
  try {
    const { state, anotherCardsExist, getCards } = useCardsStore(
      "cards-inside-withdraw"
    );
    isBusy.value = false;
    if (anotherCardsExist) {
      state.page = state.page + 1;
      await getCards({
        params: {
          status: "",
        },
      });
      await loadCards();
    }
  } catch (ex) {
    console.warn(
      "WithdrawToCard->loadCards error handled: Can`t load cards: ",
      ex
    );
  } finally {
    isBusy.value = true;
  }
};

// setup
Promise.all([
  getCards({
    // selectFirstCard: true,
    params: {
      status: "",
    },
  }),
  UserService.tariff(),
  UserService.exchangeRates(),
  // eslint-disable-next-line no-unused-vars
]).then(([_, res, exchange]) => {
  if (!exchange.status) {
    console.error(
      "WithdrawToCard->Promise.all error handled: ",
      exchange.axiosResponse
    );
  }

  rates.value = exchange.data;
  tariff.value = res.data || [];
  isBusy.value = true;
  loadCards();
});

//
onBeforeUnmount(() => {
  const { setDefaultState } = useCardsStore("cards-inside-withdraw");
  setDefaultState();
});

watch(
  () => withdrawForm.fromAmount,
  (newValue) => {
    if (activeFocus.value === "from") {
      const val = Number(newValue);
      if (transfer_fee.value === undefined) {
        return;
      }
      setWithdrawForm({
        field: "toAmount",
        payload: (
          val *
          currencyConversionRate.value *
          (1 - transfer_fee.value / 100)
        ).toFixed(2),
      });
    }
  }
);

watch(
  () => withdrawForm.toAmount,
  (newValue) => {
    if (activeFocus.value === "to") {
      const val = Number(newValue);
      if (transfer_fee.value === undefined) {
        return;
      }
      withdrawForm.fromAmount = prepareAccountBalance(
        val / currencyConversionRate.value / (1 - transfer_fee.value / 100),
        props.activeAccount._currency.iso_code
      );
    }
  }
);

const currentError = computed(() => {
  if (Number(withdrawForm.fromAmount) > Number(props.activeAccount.balance)) {
    return (
      t("errors.amountExceedsBalance2") +
      ` ${props.activeAccount._currency.iso_code}`
    );
  }
  return null;
});
</script>

<template>
  <div
    class="flex flex-col gap-5"
    data-cy="cards-choose">
    <!--  Card list.  -->
    <Portal to="withdraw-card-search-result">
      <div
        class="py-5 flex flex-col gap-3"
        data-cy="cards-list">
        <template v-if="filteredCards?.length > 0">
          <div
            v-for="(card, index) in filteredCards"
            :key="index"
            :class="
              'cursor-pointer flex items-center gap-3 px-3 py-4 rounded-base border border-greyscale-200' +
              ' hover:border-secondary-base transition-all'
            "
            @click="state.activeCardId = card.id">
            <CardInSelect :card="card" />
          </div>
        </template>

        <NoCards v-else />
      </div>
    </Portal>

    <!--  if card not selected  -->
    <template v-if="!state.activeCardId">
      <h4>{{ $t("Choose the card") }}</h4>

      <UiInputSearch
        :value="search"
        :placeholder="$t('Search for card')"
        backdrop
        @input="setSearchValue">
        <PortalTarget name="withdraw-card-search-result" />
      </UiInputSearch>
    </template>

    <!--  if card selected  -->
    <template v-if="state.activeCardId">
      <UiSelect
        :label="$t('Recharge card')"
        :value="state.activeCardId"
        :options="state.cards"
        backdrop
        searchable
        height="76"
        :search-value="search"
        :searched-options="filteredCards"
        @search="setSearchValue"
        @change="(v: any) => (state.activeCardId = v)">
        <template #withIcon="slotProps">
          <CardInSelect :card="slotProps.option" />
        </template>
        <template #searchResult>
          <PortalTarget name="withdraw-card-search-result" />
        </template>
        <template #404>
          <NoCards />
        </template>
      </UiSelect>

      <!--      <SelectCard-->
      <!--        :label="$t('Recharge card')"-->
      <!--        :value="state.activeCardId"-->
      <!--        :options="state.cards"-->
      <!--        backdrop-->
      <!--        searchable-->
      <!--        with-intersecting-->
      <!--        @is-intersecting="console.log('end of list')"-->
      <!--        @change="(v) => (state.activeCardId = v)"-->
      <!--      />-->

      <TransferAmount
        :withdraw-form="withdrawForm"
        :currency-iso-code="{
          from: activeAccount.value._currency.iso_code,
          to: cardToComputed?.account?._currency?.iso_code || 'USD',
        }"
        :max="
          activeAccount.isCard
            ? activeAccount.value.balance - 1
            : userStore.userActualLimit?.getMinAvailableDepositAmount(
                Number(activeAccount.value.balance),
                Number(cardToComputed.account.balance)
              ) || 0
        "
        :errors="currentError"
        @set-withdraw-form="setWithdrawForm"
        @set-max="
          withdrawForm.fromAmount = activeAccount.isCard
            ? activeAccount.value.balance - 1
            : prepareAccountBalance(
                userStore.userActualLimit?.getMinAvailableDepositAmount(
                  Number(activeAccount.value.balance),
                  Number(cardToComputed.account.balance)
                ) || 0,
                activeAccount.value._currency.iso_code
              )
        "
        @set-active-focus="setActiveFocus">
        <div
          class="flex items-start justify-between flex-col md:flex-row gap-2 w-full">
          <div class="flex items-start gap-2 justify-between w-full">
            <span class="font-semibold text-greyscale-600">
              {{ $t("Transfer fee") }}
            </span>

            <span
              class="font-medium"
              data-cy="transfer-fee"
              >{{ transfer_fee?.toFixed(1) }}%</span
            >
          </div>

          <div class="flex items-start gap-2 justify-between w-full">
            <span
              v-if="needCurrencyConversion"
              class="font-semibold text-greyscale-600">
              {{ $t("Rate") }}
            </span>

            <span
              v-if="needCurrencyConversion"
              class="font-medium"
              data-cy="currency-conversion-rate">
              1 {{ activeAccount.value._currency.iso_code }} =
              {{ currencyConversionRate.toFixed(2) }}
              {{ cardToComputed.account._currency.iso_code }}
            </span>
          </div>
        </div>
      </TransferAmount>
    </template>

    <!--    trans confirm-->
    <TransactionConfirm
      v-if="withdrawForm.isConfirm"
      type="withdraw"
      :from-amount="withdrawForm.fromAmount"
      :from-currency="activeAccount.value._currency.iso_code"
      :to-amount="withdrawForm.toAmount"
      :to-currency="cardToComputed?.account?._currency?.iso_code || 'USD'"
      :from-account-i-d="activeAccount.value.id"
      :to-account-i-b-a-n="cardToComputed.account.iban"
      :rate="currencyConversionRate"
      @confirm="transfer"
      @back="withdrawForm.isConfirm = false">
      <template #from>
        <CardInfo
          v-if="props.activeCard"
          :card="props.activeCard" />
        <AccountInfo
          v-else
          :account="props.activeAccount" />
      </template>
      <template #to>
        <CardInfo :card="cardToComputed" />
      </template>
      <template #toAlert>
        <!--  message for blocked cards-->
        <Warning
          v-if="cardToComputed?.status === 10"
          error="deposit_to_closed_card"
          template="wrap"
          background="yellow" />
      </template>
    </TransactionConfirm>

    <!--    trans alert-->
    <TransactionAlert
      v-if="withdrawForm.isSubmit"
      :type="withdrawForm.status"
      :error="withdrawForm.error"
      :loading="transferLoading"
      @close="$emit('close')" />
  </div>
</template>
