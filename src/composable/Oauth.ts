import { useState } from "@/helpers/utilities";
import { parseClientIdFromGoogleLink } from "@/helpers/url";
import { OauthService } from "@modules/services/oauth";

import type { Ref } from "vue";

/**
 * Composable to manage the Google OAuth link.
 *
 * @category Composables
 */
export const useGoogleLink: () => {
  value: Ref<string>;
  clientId: Ref<string>;
  setValue: (value: string) => void;
  updateValue: () => Promise<void>;
} = () => {
  const [value, setValue] = useState<string>("");
  const [clientId, setClientId] = useState<string>("");
  const updateValue = async () => {
    const googleLinkResponse = await OauthService.googleLink();

    if (!googleLinkResponse.status) {
      console.error("Oauth->updateValue error handled: ", googleLinkResponse);
      return;
    }

    setClientId(
      parseClientIdFromGoogleLink(googleLinkResponse.data?.google_url || "") ||
        ""
    );
    setValue(googleLinkResponse.data?.google_url || "");
  };

  return { value, clientId, setValue, updateValue };
};

/**
 * Composable to manage the Apple OAuth link.
 *
 * @category Composables
 */
export const useAppleLink: () => {
  value: Ref<string>;
  setValue: (value: string) => void;
  updateValue: () => Promise<void>;
} = () => {
  const [value, setValue] = useState<string>("");
  const updateValue = async () => {
    const appleLinkResponse = await OauthService.appleLink();

    if (!appleLinkResponse.status) {
      console.error("Oauth->updateValue error handled: ", appleLinkResponse);
      return;
    }

    setValue(appleLinkResponse.data?.apple_url || "");
  };

  return { value, setValue, updateValue };
};
