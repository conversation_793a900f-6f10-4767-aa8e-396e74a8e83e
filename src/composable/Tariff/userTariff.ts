import { callApiFunction } from "@/api";
import { useQuery } from "vue-query";
import { userTariff } from "@/config/queryConfig";
import type { ICardTariff } from "./types";
import type { AxiosPromise } from "axios";

interface ITariffData {
  data: ICardTariff[];
}

/**
 * Fetches user tariff using vue-query
 *
 * @deprecated we don't use calling api using vue-query
 * @category Composables
 */
export function getUserTariff() {
  const query = useQuery({
    queryKey: [userTariff],
    queryFn: (): AxiosPromise<ITariffData> => callApiFunction("getUserTariff"),
  });
  return { ...query };
}
