<script setup lang="ts">
import { computed } from "vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UITableV2 from "@/components/ui/UITableV2/UITableV2.vue";
import type { TUITableColumn } from "@/components/ui/UITableV2/types";
import { useCardBinsGet } from "@/composable/API/useCardBinsGet";
import { useI18n } from "vue-i18n";
import { binConfig, type TBinConfigItem } from "@/config/cards";
import type { TCardTariffSlug } from "@/composable";
import { type TBinBlacklistResource } from "@/composable/API/useCardBinsBlacklistGet";

const props = withDefaults(
  defineProps<{
    slug: TCardTariffSlug;
    binsBlacklist?: TBinBlacklistResource[];
  }>(),
  {
    binsBlacklist: () => [],
  }
);

const { t } = useI18n();
const { data, isFetching } = useCardBinsGet(props.slug);

const binsData = computed(() => {
  return data.value?.data ?? [];
});

type TBinItem = Omit<TBinConfigItem, "tariffs"> & {
  tariffs?: TBinTariff[];
};

const bins = computed<TBinItem[]>(() => {
  if (binsData.value.length) {
    return binsData.value.reduce((acc: TBinItem[], binDataItem) => {
      const foundItem = binConfig.find((binConfigItem) => {
        return (
          binConfigItem.bin === binDataItem.bin &&
          binConfigItem.slug === props.slug
        );
      });

      if (foundItem) {
        acc.push({
          ...foundItem,
          tariffs: binTariffs(foundItem),
        });
      }

      return acc;
    }, []);
  }
  return [];
});

const columns = computed<TUITableColumn[]>(() => [
  {
    label: t("label.bin"),
  },
  {
    label: "",
  },
  {
    label: t("label.secure"),
  },
  {
    label: t("pst-private.label.private-tariff"),
  },
]);

const isNewBin = (item: TBinItem) => {
  return item.featured && item.bin === "542723";
};

type TTariffLabel = "Extra Small" | "Small" | "Medium" | "Large";

const TARIFF_MAP: Record<string, TTariffLabel> = {
  "Extra Small": "Extra Small",
  "Extra Small_B": "Extra Small",
  Small: "Small",
  Small_A: "Small",
  Medium: "Medium",
  Medium_A: "Medium",
  Large: "Large",
};

const DISPLAY_TARIFFS: TTariffLabel[] = [
  "Extra Small",
  "Small",
  "Medium",
  "Large",
];

type TBinTariff = {
  label: TTariffLabel;
  enabled: boolean;
};

const binTariffs = (binItem: TBinConfigItem): TBinTariff[] => {
  const binsBlacklistData = props.binsBlacklist ?? [];
  const matchingBlacklistItem = binsBlacklistData.find((blItem) =>
    binItem.bin.startsWith(blItem.bin)
  );
  const disabledTariffsSet = new Set<string>();

  if (matchingBlacklistItem) {
    matchingBlacklistItem.disabled_for.forEach((tariffName) => {
      const tariffLabel = TARIFF_MAP[tariffName];
      if (tariffLabel) {
        disabledTariffsSet.add(tariffLabel);
      }
    });
  }

  return DISPLAY_TARIFFS.map((tariffLabel) => {
    return {
      label: tariffLabel,
      enabled: !disabledTariffsSet.has(tariffLabel),
    };
  });
};
</script>

<template>
  <UITableV2
    class="bin-table create-card-select-tariff-bins-table"
    grid-class="grid-cols-[110px_60px_114px_1fr]"
    width-class="w-full"
    :columns="columns"
    :items="bins"
    :is-loading="isFetching">
    <template #item="{ item }">
      <!--  BIN  -->
      <div
        class="bin-table__cell"
        :class="{ 'new-bin': isNewBin(item) }">
        <span
          v-if="isNewBin(item)"
          class="cursor-pointer">
          <DynamicIcon
            v-if="isNewBin(item)"
            name="fire"
            class="h-5 w-5 text-core-red-500" />
        </span>
        <span class="font-medium ml-2.5"> {{ item.bin }} </span>
      </div>

      <!--  Payment System   -->
      <div class="bin-table__cell">
        <div class="h-5 w-8 flex items-center justify-center">
          <DynamicIcon
            :name="item.bin.startsWith('4') ? 'visa' : 'mastercard'"
            path="gateway"
            class="h-3.5 w-7" />
        </div>
      </div>

      <!--  Secure  -->
      <div class="bin-table__cell justify-center">
        <div
          v-if="(item as any).secure"
          class="h-4.5 w-4.5 rounded-full bg-bg-level-2 flex items-center justify-center">
          <DynamicIcon
            class="h-4 w-4"
            name="check" />
        </div>
        <div
          v-else
          class="flex items-center justify-center">
          <span>-</span>
        </div>
      </div>

      <!--  Tariff  -->
      <div class="bin-table__cell">
        <span
          v-for="tariff in item.tariffs"
          :key="tariff.label"
          :class="{ 'opacity-20': !tariff.enabled }"
          class="flex items-center ml-2 first:ml-0 rounded px-2 bg-bg-level-1-5">
          <span class="text-3.5 font-medium capitalize">
            {{ tariff.label }}
          </span>
        </span>
      </div>
    </template>
  </UITableV2>
</template>

<style lang="scss" scoped>
.bin-table {
  @apply w-full;

  .new-bin ~ div,
  .new-bin {
    @apply bg-bg-orange-light;
  }

  &__cell {
    @apply flex items-center flex-nowrap px-5 py-4 min-h-[60px];
  }

  :deep(.ui-table__header) {
    .ui-table-header-column {
      @apply px-5 py-4 font-semibold;
    }

    .ui-table-header-column:first-child {
      @apply border-r-0;
    }

    .ui-table-header-column:nth-child(3) {
      @apply text-center;
    }
  }
}
</style>
