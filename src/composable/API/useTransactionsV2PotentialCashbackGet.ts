import type { TTransactionResource } from "@/types/api/TTransactionResource";
import type { UseFetchOptions } from "@vueuse/core";
import { useGetRequest } from "@/composable/useGetRequest";
import type { MaybeRef } from "vue";
import type { TResponseMeta } from "@/types/api/TResponseMeta";

export type TTransactionsV2PotentialCashbackReq = {
  sort?: string;
  direction?: "desc" | "asc";
  per_page?: number;
  page?: number;
};
export const useTransactionsV2PotentialCashbackGet = (
  reqConfig: MaybeRef<TTransactionsV2PotentialCashbackReq> = {},
  useFetchOptions: UseFetchOptions = {}
) => {
  return useGetRequest<
    {
      potential_cashback: string;
      data: TTransactionResource[] | null;
      meta: TResponseMeta;
    },
    MaybeRef<TTransactionsV2PotentialCashbackReq>
  >("transactions-v2/potential-cashback", reqConfig, useFetchOptions);
};
