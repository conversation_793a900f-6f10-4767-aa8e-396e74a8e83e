<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import type { IBinInfo } from "@/components/Cards/StandardCreateCard/Stages/SelectBin/types";
import { TrackerService } from "@/helpers/tracker/tracker.service";
import { TrackerEvent } from "@/helpers/tracker/tracker.types";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import type { TCardTariff } from "@/types/user/user.types";
import { computed } from "vue";
import { Tooltip } from "floating-vue";

export interface Props {
  bin: IBinInfo;
  tariff: TCardTariff | any;
  subscription: boolean;
  cashback: number;
  subscriptionLevel?: string;
}

const router = useRouter();
const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(["onSelectedBin"]);

const onSelectHandler = async () => {
  if (!props.bin.private) {
    emit("onSelectedBin", props.bin.bin, props.bin.slug);
  } else {
    await router.push("/app/subscription/promo");
    await TrackerService.logEvent(TrackerEvent.BUTTON, {
      page: "/app/create",
      page_version: 0,
      name: "Private",
      version: 0,
      location: "table",
    });
  }
};
const isAuto3ds = computed(() => {
  return ["542723", "517746"].includes(props.bin.bin);
});
</script>
<template>
  <div
    class="flex flex-none flex-col"
    :class="$style['root']">
    <template v-if="bin.under_maintenance">
      <Tooltip
        placement="bottom-start"
        :triggers="['hover']">
        <a>
          <div
            class="flex flex-none flex-row bg-bg-orange-light items-center"
            :class="$style['root__bin']">
            <div class="flex flex-none">BIN:</div>
            <div
              v-if="bin?.featured"
              class="flex flex-none h-full items-center">
              <DynamicIcon
                name="fire"
                class="h-4 w-4" />
            </div>
            <div class="flex flex-none">
              {{ bin?.bin }}
            </div>
            <div class="flex flex-none h-5">
              <DynamicIcon
                name="warning_circle"
                class="h-4 w-4 flex m-auto text-fg-orange" />
            </div>
          </div>
        </a>
        <template #popper>
          <div class="max-w-[260px]">
            <span class="text-4 text-white">{{
              $t("common.technical-work.title")
            }}</span>
            <p class="text-3.5 text-fg-tertiary">
              {{ $t("common.technical-work.descriptions") }}
            </p>
          </div>
        </template>
      </Tooltip>
    </template>
    <div
      v-else
      class="flex flex-none flex-row bg-neutral-150"
      :class="$style['root__bin']">
      <div class="flex flex-none">BIN:</div>
      <div
        v-if="bin?.featured"
        class="flex flex-none h-full items-center">
        <DynamicIcon
          name="fire"
          class="h-4 w-4" />
      </div>
      <div class="flex flex-none">
        {{ bin?.bin }}
      </div>
    </div>

    <div class="flex flex-none flex-row gap-1 mt-3">
      <div class="flex flex-none">
        <span class="bg-neutral-150 rounded-[6px] px-[6px] py-1 w-fit">
          <DynamicIcon
            :name="bin?.bin?.startsWith('4') ? 'visa' : 'mastercard'"
            path="gateway"
            class="w-[24px] h-[12px]" />
        </span>
      </div>
      <div
        v-if="bin?.secure || isAuto3ds"
        v-tooltip="{
          content: '3D-S Secure',
        }"
        class="flex flex-none"
        :class="$style['root__badge-3ds']">
        3D-S
        <span
          v-if="bin?.autoSecure || isAuto3ds"
          class="ml-1"
          >Auto</span
        >
      </div>
      <div
        v-for="(icon, index) of bin?.recommended"
        :key="index"
        class="flex flex-none">
        <DynamicIcon
          v-tooltip="{
            content: icon,
          }"
          class="w-[20px] h-[20px]"
          :name="`social-circle-${icon}`" />
      </div>
    </div>
    <div class="flex flex-none grid-cols-3 grid w-full gap-2 mt-6">
      <div
        class="flex flex-auto flex-col"
        :class="$style['root__info']">
        <div
          class="flex flex-none"
          :class="$style['root__info__value']">
          <div class="inline">
            <span
              v-if="bin.default?.transferFee"
              :class="[$style['text-fill-right']]"
              class="mr-2 text-[13px] text-neutral-400 line-through">
              {{ bin.default.transferFee }}
            </span>
            <span :class="[$style.text, $style['text-fill-right']]">
              {{ Number(tariff?.fee_transaction_amount) }} $
            </span>
          </div>
        </div>
        <div
          class="flex flex-none"
          :class="$style['root__info__title']">
          {{ $t("card.create.selectBin.info.transfer") }}
        </div>
      </div>
      <div
        class="flex flex-auto flex-col"
        :class="$style['root__info']">
        <div
          class="flex flex-none"
          :class="$style['root__info__value']">
          <span
            v-if="bin?.default?.depositFee"
            :class="[$style['root__info__line-through']]"
            class="mr-2 text-[13px] text-neutral-400 line-through">
            {{ Number(bin?.default?.depositFee) }} %
          </span>
          {{ Number(tariff?.fee_topup) }} %
        </div>
        <div
          class="flex flex-none"
          :class="$style['root__info__title']">
          {{ $t("card.create.selectBin.info.deposit") }}
        </div>
      </div>
      <div
        class="flex flex-auto flex-col"
        :class="$style['root__info']">
        <div
          class="flex flex-none"
          :class="$style['root__info__value']">
          <span
            v-if="bin?.private"
            :class="[$style['root__info__line-through']]"
            class="mr-2 text-[13px] text-neutral-400 line-through">
            0 %
          </span>

          {{ cashback }} %
        </div>
        <div
          class="flex flex-none"
          :class="$style['root__info__title']">
          {{ $t("card.create.selectBin.info.cashback") }}
        </div>
      </div>
    </div>
    <div
      v-if="
        !!subscriptionLevel &&
        !!bin.tariffs &&
        !bin.tariffs.includes(subscriptionLevel)
      "
      class="flex flex-none mt-5 w-full">
      {{ t("createFirstCard.bins.startingFrom") }}
      {{ bin.tariffs[0].toUpperCase() }}
    </div>
    <div
      v-else
      class="flex flex-none mt-5 w-full"
      @click="onSelectHandler">
      <button
        v-if="!bin?.private"
        class="w-full"
        :class="$style['root__btn']">
        {{
          t("card.create.selectBin.info.btnIssueByPrice", {
            f: `${Number(tariff?.card_price)} $`,
          })
        }}
      </button>
      <button
        v-else
        class="w-full"
        :class="$style['root__btn-private']">
        {{ $t("card.create.selectBin.info.freeInPrivate") }}
        <span v-if="bin.tariffs && bin.tariffs?.length > 0">
          {{ t("createFirstCard.bins.startingFrom") }}
          {{ bin?.tariffs[0].toUpperCase() }}
        </span>
      </button>
    </div>
  </div>
</template>
<style lang="scss" module>
.root {
  display: flex;
  height: auto;
  padding: 16px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  flex: 1 0 0;

  border-radius: 6px;
  background: #f6f6f6;

  &__badge-3ds {
    display: flex;
    padding: 4px var(--core-6, 6px);
    justify-content: center;
    align-items: center;
    gap: var(--core-4, 0px);
    border-radius: var(--core-6, 6px);
    background: var(
      --sys-surface-bg-neutral-bg-dark-alpha,
      rgba(21, 25, 29, 0.1)
    );
    color: var(--sys-interactive-default-alpha-fg, #15191d);
    font-family: ALS Hauss VF;
    font-size: 12px;
    font-style: normal;
    font-weight: 550;
    line-height: 12px; /* 100% */
  }

  &__btn {
    display: flex;
    height: var(--32, 32px);
    padding: 0px var(--core-8, 8px);
    justify-content: center;
    align-items: center;
    gap: var(--core-8, 8px);
    flex-shrink: 0;
    align-self: stretch;

    border-radius: 6px;
    background: var(
      --sys-interactive-default-alpha-bg-static,
      rgba(21, 25, 29, 0.1)
    );

    &:hover {
      border-radius: var(--core-6, 6px);
      background: var(
        --sys-interactive-default-alpha-bg-hover,
        rgba(21, 25, 29, 0.17)
      );
    }

    &:active {
      border-radius: var(--core-6, 6px);
      background: var(--sys-interactive-default-alpha-bg-activated, #fff);
    }
  }

  &__btn-private {
    display: flex;
    height: var(--32, 32px);
    padding: 0px var(--core-8, 8px);
    justify-content: center;
    align-items: center;
    gap: var(--core-8, 8px);
    flex-shrink: 0;
    align-self: stretch;

    border-radius: 6px;
    background: var(--sys-interactive-black-bg-static, #15191d);

    color: var(--sys-interactive-black-fg, #fff);
    text-align: center;

    font-family: ALS Hauss VF;
    font-size: 15px;
    font-style: normal;
    font-weight: 550;
    line-height: 18px; /* 120% */

    &:hover {
      border-radius: var(--core-6, 6px);
      background: var(--sys-interactive-black-bg-hover, #313438);
    }

    &:active {
      border-radius: var(--core-6, 6px);
      background: var(--sys-interactive-black-bg-clicked, #4c4f53);
    }
  }

  &__info {
    &__line-through {
      color: var(--sys-surface-fg-neutral-fg-secondary, #838689);

      font-family: ALS Hauss VF;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
    }
    &__value {
      color: var(--sys-surface-fg-neutral-fg-primary, #313438);
      text-align: right;

      /* text/calculate */
      font-family: ALS Hauss VF;
      font-size: 15px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px; /* 120% */
    }

    &__title {
      color: var(--sys-surface-fg-neutral-fg-secondary, #838689);
      font-family: ALS Hauss VF;
      font-size: 13px;
      font-style: normal;
      font-weight: 420;
      line-height: 120%; /* 15.6px */
    }
  }

  &__bin {
    display: flex;
    padding: 4px 8px;
    justify-content: center;
    align-items: flex-start;
    gap: 10px;

    border-radius: 6px;

    color: #15191d;
    font-family: ALS Hauss VF;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
}
</style>
