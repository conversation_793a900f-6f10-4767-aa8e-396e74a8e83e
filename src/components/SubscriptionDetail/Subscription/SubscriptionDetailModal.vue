<template>
  <UISideModal
    :is-open="true"
    :title="$t('pst-private.modal.details.header.title')"
    @close="$emit('close')">
    <template #content>
      <div v-if="error">
        <p class="text-fg-red text-center">Server error</p>
      </div>

      <div
        v-else-if="!isFetching"
        class="flex flex-col gap-2.5">
        <div class="bg-bg-level-1 p-4 rounded">
          <ul class="flex flex-col gap-4">
            <li class="flex flex-start justify-between">
              <div>
                <p class="text-fg-secondary">
                  {{ $t("pst-private.subscription.tariff") }} Private
                  {{ currentMainTariff?.subscription_tariff.type_name }}・{{
                    currentMainTariff?.subscription_tariff.cards_limit
                  }}
                  cards
                </p>
              </div>

              <p>
                {{
                  currencyFormatter(
                    currentMainTariff?.subscription_tariff?.amount!,
                    "USD",
                    true
                  )
                }}
              </p>
            </li>

            <li
              v-for="(item, index) in extentions"
              :key="index"
              class="flex flex-start justify-between">
              <div>
                <p class="text-fg-secondary">
                  {{ item.tariff.subscription_tariff.name }}
                </p>
                <p
                  v-if="item.count > 1"
                  class="text-fg-secondary text-3.5 font-medium">
                  {{
                    currencyFormatter(
                      item.tariff.subscription_tariff.amount,
                      "USD",
                      true
                    )
                  }}
                  x {{ item.count }}
                </p>
              </div>

              <p v-if="item.count > 1">
                {{ currencyFormatter(item.totalAmount, "USD", true) }}
              </p>
              <p v-else>
                {{
                  currencyFormatter(
                    item.tariff.subscription_tariff.amount,
                    "USD",
                    true
                  )
                }}
              </p>
            </li>
          </ul>

          <Divider
            type="dashed"
            class="py-7" />

          <div class="flex items-center justify-between">
            <p class="text-fg-primary text-5 font-medium">
              {{ $t("pst-private.modal.details.table.summary") }}
            </p>
            <p class="text-fg-primary text-5 font-medium">
              {{ currencyFormatter(totalAmount!, "USD", true) }}
            </p>
          </div>
        </div>

        <UIButton
          color="orange-solid"
          class="w-full"
          @click="onImproveTariff"
          >{{ $t("pst-private.subscription.tariff.info.btn") }}</UIButton
        >
        <UIButton
          color="black"
          class="w-full"
          @click="openUpLimitHandler"
          >{{
            $t(
              "pst-private.modal.manage.section.extensions.button.upgrade-cards-limit"
            )
          }}</UIButton
        >
      </div>

      <div
        v-else
        class="flex flex-col gap-2.5">
        <Skeletor
          class="rounded"
          height="240"
          as="div"></Skeletor>

        <Skeletor
          class="rounded"
          height="44"
          as="div"></Skeletor>

        <Skeletor
          class="rounded"
          height="44"
          as="div"></Skeletor>
      </div>
    </template>
  </UISideModal>
</template>

<script setup lang="ts">
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import Divider from "@/components/ui/Divider/Divider.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { currencyFormatter } from "@/helpers/currencyFormatter";
import { Skeletor } from "vue-skeletor";
import type { TSubscriptionResource } from "@/types/api/TSubscriptionResource";
import { computed } from "vue";
import { useSubscriptionsList } from "@/composable/useSubscriptionsList";
import { useSubscriptionsModalFlow } from "@/components/Subscriptions/useSubscriptionsModalFlow";

const { setModalState } = useSubscriptionsModalFlow();
const { subscriptionsList, currentMainTariff, isFetching, error } =
  useSubscriptionsList();

const emit = defineEmits<{
  close: [];
  improveTariff: [];
}>();

const extentions = computed(() => {
  return subscriptionsList.value?.reduce(
    (
      acc: {
        tariff: TSubscriptionResource;
        count: number;
        totalAmount: number;
      }[],
      tariff: TSubscriptionResource
    ) => {
      const { type_name } = tariff.subscription_tariff;

      const tar = acc.find(
        (t) => t.tariff.subscription_tariff_id === tariff.subscription_tariff_id
      );

      if (tar) {
        tar.count = tar.count + 1;
        tar.totalAmount =
          tar.count * Number(tar.tariff?.subscription_tariff.amount);
      }

      if (!tar && type_name === "EXTENSION") {
        acc.push({
          tariff: tariff,
          count: 1,
          totalAmount: Number(tariff.subscription_tariff.amount),
        });
      }

      return acc;
    },
    []
  );
});

const totalAmount = computed(() => {
  return subscriptionsList.value?.reduce((acc, item) => {
    return acc + Number(item.subscription_tariff.amount);
  }, 0);
});

const openUpLimitHandler = () => {
  emit("close");
  setModalState(true, "subscriptionUpLimitModal");
};

const onImproveTariff = () => {
  emit("close");
  emit("improveTariff");
};
</script>
