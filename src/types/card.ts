import type { TAccount } from "@/types/TAccount";
import type { ITag } from "@/types/ITag";
import type { TCurrency } from "@/types/dictionary/currencies";

export type TCardPst = {
  [key: string]: any;
  account: TAccount;
  auto_refill: TCardRefill;
  created_at: string;
  description: string | null;
  favorite: number;
  holder_address: string | null;
  holder_name: string | null;
  id: number;
  in_subscription: boolean;
  limits_equals: boolean;
  mask: string;
  need_amount: string | null;
  ordered_at: string;
  ordered_until: string;
  status: number;
  tariff_id: number;
  topup_waiting: false;
  user_account_id: number;
  tags: ITag[];
  _tariff_slug: string;
  _tariff: string;
  _currency: TCurrency;
};

type TCardRefill = {
  active: boolean;
  amount_refill: string;
  card_id: number;
  created_at: string;
  deleted_at: string | null;
  id: number;
  minimum_balance: string;
  updated_at: string;
};

export interface IBin {
  bin: string;
  available_cards?: number;
  new?: boolean;
  slug?: string;
}
