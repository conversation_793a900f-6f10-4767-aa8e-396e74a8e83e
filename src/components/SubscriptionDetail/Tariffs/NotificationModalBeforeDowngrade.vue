<script setup lang="ts">
import UiTransition from "@/components/ui/UITransition.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";

interface Props {
  isOpen: boolean;
  title: string;
  text: string;
  confirmButtonText: string;
  success: () => void;
  close: () => void;
  newTariff: TSubscriptionTariffResource;
  currentTariff: TSubscriptionTariffResource;
}
const props = defineProps<Props>();
</script>

<template>
  <UiTransition name="ui-fade">
    <template v-if="props.isOpen">
      <div
        v-if="props.isOpen"
        class="flex flex-col fixed w-full h-full left-0 top-0 z-3 align-center items-center wrapper p-4">
        <div
          class="flex flex-col max-w-[28.625rem] w-full m-auto h-auto wrapper__container">
          <div class="flex flex-col gap-5">
            <div class="flex flex-row">
              <div
                class="flex flex-auto overflow-hidden overflow-y-auto text-fg-primary text-5 font-medium leading-6">
                {{ props.title }}
              </div>
              <div class="flex">
                <DynamicIcon
                  name="close"
                  class="w-6 h-6 cursor-pointer"
                  @click="props.close" />
              </div>
            </div>
            <div class="flex flex-auto text-4 leading-5 text-fg-primary w-full">
              {{ props.text }}
            </div>
            <div class="flex flex-none w-full mb-2">
              <slot name="content"></slot>
            </div>
            <div class="flex flex-none flex-col">
              <UiButton
                color="primary"
                class="w-full"
                :title="props.confirmButtonText"
                @click="props.success" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </UiTransition>
</template>
<style lang="scss" scoped>
.wrapper {
  background-color: rgba(0, 0, 0, 0.8);
  &__container {
    @apply p-4 bg-bg-level-0 rounded shadow-lg border-bg-level-1 gap-5;
  }
}
</style>
