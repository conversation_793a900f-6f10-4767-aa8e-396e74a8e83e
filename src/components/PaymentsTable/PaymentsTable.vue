<script setup lang="ts">
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UIToggle from "@/components/ui/UIToggle/UIToggle.vue";
import PaymentsDefaultView from "@/components/PaymentsTable/PaymentsDefaultView.vue";
import PaymentsGroupView from "@/components/PaymentsTable/PaymentsGroupView.vue";
import PaymentsFilters from "@/components/PaymentsTable/PaymentsFilters.vue";
import { useRoute } from "vue-router";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/user";
import { useTransactionsV2SummaryGet } from "@/composable/API/useTransactionsV2SummaryGet";
// eslint-disable-next-line max-len
import { useBusinessMembersTransactionsV2SummaryGet } from "@/composable/API/useBusinessMembersTransactionsV2SummaryGet";
import { useRemoteStorageStore } from "@/stores/remoteStorage";
import { usePaymentsExport } from "@/components/PaymentsTable/usePaymentsExport";
import { usePaymentFilters } from "./usePaymentFilters";
import { useSubscriptionsInfoGet } from "@/composable";
import PaymentsSummary from "@/components/PaymentsSummary/PaymentsSummary.vue";
import PaymentsMobileList from "@/components/PaymentsTable/PaymentsMobileList.vue";
import { isMobile } from "@/helpers";
import PaymentDetailModal from "@/components/PaymentsTable/PaymentDetailModal.vue";
import type { TTransactionResource } from "@/types/api/TTransactionResource";
import { RouteName } from "@/constants/route_name";

const { t } = useI18n();
const route = useRoute();

const userStore = useUserStore();

const tabs: any = {
  PaymentsDefaultView,
  PaymentsGroupView,
};

const {
  currentVisibleTable,
  isUserPaymentsView,
  initializeFilters,
  filtersState,
  defaultRequest,
  groupedByRequest,
  summaryRequest,
  resetFilters,
  updateFiltersState,
} = usePaymentFilters();

const requests = {
  PaymentsDefaultView: defaultRequest,
  PaymentsGroupView: groupedByRequest,
};

const selectedTransaction = ref<TTransactionResource | null>(null);

const { exportFunc } = usePaymentsExport();

const remoteStorageStore = useRemoteStorageStore();

const { data: subscriptionsInfo } = useSubscriptionsInfoGet({
  immediate: true,
});

onMounted(async () => {
  await userStore.getUser();
});

initializeFilters();

const setSelectedTransaction = (trx: TTransactionResource | null) => {
  selectedTransaction.value = trx;
};

const getPaymentsSummaryData = () => {
  return isUserPaymentsView
    ? useTransactionsV2SummaryGet(summaryRequest, {
        refetch: true,
        immediate: false,
      })
    : useBusinessMembersTransactionsV2SummaryGet(summaryRequest, {
        refetch: true,
        immediate: false,
      });
};
const paymentsSummaryData = getPaymentsSummaryData();

const {
  data: paymentsSummaryRes,
  isFetching: isFetchingPaymentsSummary,
  execute: executePaymentsSummaryReq,
} = paymentsSummaryData;

const isUnspent = computed(() =>
  Boolean(
    !isFetchingPaymentsSummary.value &&
      Object.values(paymentsSummaryRes.value?.data?.summary || {}).every(
        (v) => v === 0
      )
  )
);

const updateSummaryData = async () => {
  await executePaymentsSummaryReq();
};
updateSummaryData();
const tableTitle = computed(() => {
  return route.name === RouteName.TEAM_PAYMENTS
    ? t("common.team.payments-table.title")
    : t("payments.title");
});
</script>

<template>
  <div>
    <!-- <PaymentsTableNotPaymentView -->
    <!--   v-if="userStore.summary?.transactions_count! == 0" /> -->
    <!-- <template v-else> -->
    <div
      class="flex flex-wrap items-center justify-between mb-4 gap-x-3 md:gap-x-5">
      <h2 class="text-5 font-medium md:w-fit">
        {{ tableTitle }}
      </h2>
      <div
        v-if="!isMobile"
        class="flex items-center gap-3 ml-auto w-1/2 md:w-fit justify-end">
        <span>PRO mode</span>
        <UIToggle
          v-if="remoteStorageStore.isProMode !== null"
          :model-value="remoteStorageStore.isProMode"
          @update:model-value="remoteStorageStore.setProMode($event)" />
      </div>
      <div class="flex items-center gap-3">
        <span class="md:block">Export to</span>
        <UIButton
          size="s"
          color="grey-solid"
          @click="exportFunc('csv')">
          CSV
        </UIButton>
        <UIButton
          size="s"
          color="grey-solid"
          @click="exportFunc('xlsx')">
          XLSX
        </UIButton>
      </div>
      <PaymentsFilters
        class="mt-4"
        @refetch-summary="updateSummaryData"
        @reset-filters="resetFilters" />
    </div>

    <PaymentsSummary
      v-if="!isMobile || (isMobile && !isUnspent)"
      class="mb-4"
      :summary="paymentsSummaryRes?.data?.summary || null"
      :loading="isFetchingPaymentsSummary" />

    <PaymentsMobileList
      v-if="isMobile"
      :is-user-payments-view="isUserPaymentsView"
      :request="defaultRequest"
      @select-transaction="setSelectedTransaction" />

    <component
      :is="tabs[currentVisibleTable]"
      v-else
      :key="route.name"
      :request="requests[currentVisibleTable]"
      :model-value="filtersState"
      :is-user-payments-view="isUserPaymentsView"
      :subscription-status="subscriptionsInfo?.data?.status"
      @select-transaction="setSelectedTransaction"
      @update:model-value="updateFiltersState"></component>

    <PaymentDetailModal
      v-if="selectedTransaction"
      :transaction="selectedTransaction"
      @close="setSelectedTransaction(null)" />

    <!-- </template> -->
  </div>
</template>
