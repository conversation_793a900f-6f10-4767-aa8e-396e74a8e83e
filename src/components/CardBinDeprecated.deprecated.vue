<script setup lang="ts">
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIBlock from "@/components/ui/UIBlock/UIBlock.vue";
import type { TCardResource } from "@/types/api/TCardResource";
import { getFormattedDate } from "@/helpers/time";
import { getBinFromCardMask } from "@/helpers/getBinFromCardMask";

const props = defineProps<{
  card: TCardResource;
}>();

defineEmits<{
  openUpgradeCardModal: [];
}>();
</script>

<template>
  <UIBlock :background="'red'">
    <template #title>
      <div class="flex flex-row items-center space-x-2">
        <DynamicIcon
          name="alert-circle"
          class="text-fg-primary w-7 h-7" />
        <h3 class="text-2xl font-semibold text-nowrap">
          {{ $t("card.upgrade-card") }}
        </h3>
      </div>
    </template>
    <template #content>
      <p>
        <span>
          {{
            $t("cards.upgrade-card-description", {
              bin: getBinFromCardMask(props.card.mask),
            })
          }}
        </span>
        <span class="font-semibold ml-1">
          {{
            getFormattedDate(new Date(2024, 7, 29), {
              withYear: true,
            })
          }}
        </span>
      </p>
      <p class="mt-2">
        <span>
          {{ $t("cards.upgrade-card-description-2") }}
        </span>
        <span class="font-semibold ml-1">
          {{ $t("cards.upgrade-card-description-3") }}
        </span>
      </p>
      <UIButton
        size="m"
        color="black"
        class="w-full mt-4"
        @click="$emit('openUpgradeCardModal')">
        {{ $t("cards.upgrade-card") }}
      </UIButton>
    </template>
  </UIBlock>
</template>
