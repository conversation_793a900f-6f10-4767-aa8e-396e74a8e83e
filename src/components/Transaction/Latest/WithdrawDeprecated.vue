<script setup lang="ts">
import UiButton from "@/components/ui/Button/Button.vue";
import InputCurrency from "@/components/ui/Input/InputCurrency.vue";
import InputText from "@/components/ui/Input/InputText.vue";
import SelectAccount from "@/components/ui/Select/SelectAccount.deprecated.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import TextArea from "@/components/ui/TextArea/TextareaRefactor.vue";
import SelectExternal from "@/components/Transaction/Latest/SelectExternal.vue";
import Fee from "@/components/Transaction/Latest/Fee.vue";
import Message from "@/components/Transaction/Latest/Message.vue";
import Confirm from "@/components/Transaction/Latest/Confirm.vue";

import { differenceInSeconds } from "date-fns";
import { watch, onMounted, watchEffect, computed } from "vue";
import { useI18n } from "vue-i18n";

import { UserService } from "@modules/services/user";
import { prepareAccountBalance, useAxios } from "@/helpers";
import { useUserStore } from "@/stores/user";

import { TrackerService } from "@/helpers/tracker/tracker.service";
import { TrackerEvent } from "@/helpers/tracker/tracker.types";

import type {
  TTToDirection,
  TWithdrawProps,
  TWithdrawToDirection,
} from "@/components/Transaction/Latest/types";
import { useWithdraw } from "@/composable/Transaction";
import { useRoute, useRouter } from "vue-router";

//state
const minFeeAmount = 10;
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const { t } = useI18n();
const props = defineProps<TWithdrawProps>();
const emit = defineEmits(["on-change-step", "on-submit", "on-kyc-limit"]);
const {
  state,
  toEmail,
  toAddress,
  reason,
  accounts,
  activeAccount,
  usdtAccount,
  accountWithMaxUsd,
  fromAmountMax,
  fromAmountMin,
  rates,
  tariff,
  externalFee,
  exchangeFee,
  blockedState,
  setState,
  changeAmount,
  replaceQuery,
  setDirection,
} = useWithdraw({ minFeeAmount });

// setup
Promise.all([
  UserService.tariff(),
  UserService.exchangeRates(),
  userStore.getUserFees(),
]).then(([tariffRes, exchangeRes]) => {
  if (!exchangeRes.status) {
    console.error(
      "TransferFromAccountToAccount->Promise.all error handled: ",
      exchangeRes.axiosResponse
    );
  }
  tariff.value = tariffRes.data;
  rates.value = exchangeRes.data;
});

const directions = computed<Array<TWithdrawToDirection>>(() => {
  const toUser = {
    id: "user",
    title: t("withdraw.toUser"),
    icon: "user",
    iconPath: "",
  };
  const toMaster = {
    id: "user",
    title: t("withdraw.toMaster"),
    icon: "user",
    iconPath: "",
  };
  const toExternal = {
    id: "external",
    title: t("withdraw.toExternal", { wallet: "USDT" }),
    icon: "square-usdt",
    iconPath: "accounts",
  };
  if (userStore.user.master_email) return [toMaster];
  if (userStore.isSuspicious) return [toExternal];
  return [toUser, toExternal];
});

// methods
const onSetDirection = (
  id: TTToDirection | number | string,
  direction: "from" | "to"
) => {
  setDirection(id, direction);
  if (direction === "to") {
    emit("on-change-step", 2);
  }
};

const onSubmitTransaction = async (
  toDirection: TTToDirection | string | undefined
) => {
  state.value.loading = true;
  if (
    state.value.toDirection === "external" &&
    !userStore?.userFees?.withdraw_allowed
  ) {
    emit("on-kyc-limit", "other", t("errors.withdrawBlocked"));
  } else {
    try {
      state.value.isSubmit = true;
      state.value.isConfirm = false;

      if (toDirection === "user") {
        await useAxios().post("/transfer/transfer", {
          from_account_id: activeAccount.value?.id,
          to_account_iban: undefined,
          to_user_email: toEmail.value.value,
          amount: String(state.value.fromAmount),
          with_commission: "1",
        });
      }

      if (toDirection === "external") {
        await useAxios().post("/withdrawal", {
          user_account_id: activeAccount.value?.id,
          amount: String(state.value.fromAmount),
          wallet: toAddress.value.value,
          reason: reason.value.value,
        });
      }

      state.value.isConfirm = true;
      emit("on-submit", state.value.isConfirm);
      // TODO: add actual tracker
      TrackerService.logEvent(TrackerEvent.CARD_TOP_UP).catch((ex) =>
        console.warn(
          "TransactionWithdraw->TrackerService.logEvent error handled: ",
          ex
        )
      );
    } catch (ex: any) {
      const field = ex?.response?.data?.field;

      if (field) {
        if (field === "card_deposit_limit") {
          state.value.error.kyc = t("error.text.kyc_limit", {
            kyc_actual: userStore.actual,
            kyc_deposit_limit:
              userStore.userActualLimit?.userLimit.card_deposit_limit,
          });
        } else {
          state.value.error[field] = field;
        }
      } else {
        state.value.error.resultError = ex?.response?.data?.message;
      }

      // Blocked withdraw to PST user
      if (ex?.response?.data?.message === "Too many attempts.") {
        blockedState.value = {
          state: true,
          date: new Date(),
          duration: 3600, // 1 hour
        };
      }
      emit("on-submit", state.value.isConfirm, state.value.error.resultError);
    }
  }
  state.value.loading = false;
};

watch(
  () => props.step,
  (newStep) => {
    if (newStep === 1 && Object.keys(state.value.error).length) {
      state.value.fromAmount = "0";
      state.value.toAmount = "0";
    }
    if (
      newStep === 2 &&
      state.value.toDirection === "external" &&
      state.value.fromAmount
    ) {
      if (Number(state.value.fromAmount) >= fromAmountMin.value)
        changeAmount(Number(state.value.fromAmount), "fromAmount");
      else {
        delete state.value.error.inputFrom;
        state.value.fromAmount = "0";
        state.value.toAmount = "0";
      }
    }

    if (
      newStep === 2 &&
      state.value.toDirection === "user" &&
      state.value.error.inputFrom
    ) {
      delete state.value.error.inputFrom;
      changeAmount(Number(state.value.fromAmount), "fromAmount");
    }
    if (
      newStep === 2 &&
      state.value.toDirection === "user" &&
      Number(state.value.fromAmount) > fromAmountMax.value
    ) {
      delete state.value.error.inputFrom;
      changeAmount(fromAmountMax.value, "fromAmount");
    }
  }
);

const unblockWithdraw = () => {
  blockedState.value = {
    state: false,
    date: null,
    duration: 0,
  };
};

onMounted(() => {
  setState(props);
  if (props.toDirection) onSetDirection(props.toDirection, "to");
  if (props.fromId) onSetDirection(props.fromId, "from");
  if (userStore?.user?.master_email)
    toEmail.setValue(userStore.user.master_email);
  watchEffect(async () => {
    state.value.step = props.step;

    if (!state.value.loading) {
      if (
        accountWithMaxUsd.value?.id &&
        state.value.fromId === -1 &&
        state.value.toDirection === "user"
      ) {
        onSetDirection(String(accountWithMaxUsd.value.id), "from");
      }
      if (
        usdtAccount.value?.id &&
        state.value.toDirection === "external" &&
        state.value.fromId === -1
      ) {
        onSetDirection(String(usdtAccount.value?.id), "from");
      }
    }

    if (state.value || toEmail.value.value || toAddress.value.value) {
      if (props.step === 1) {
        await replaceQuery(route, router, true);
        state.value.error = {};
      } else {
        await replaceQuery(route, router);
      }
    }
  });

  if (blockedState.value.state && blockedState.value.date) {
    const difference = differenceInSeconds(
      new Date(),
      new Date(blockedState.value.date)
    );
    if (difference >= blockedState.value.duration) {
      unblockWithdraw();
    }
    return;
  }
});
</script>
<template>
  <div :class="withdraw.root">
    <template v-if="props.step === 1">
      <SelectExternal
        :directions="directions"
        @change="(v) => onSetDirection(v, 'to')" />
    </template>

    <template v-if="props.step === 2">
      <!--    From -->
      <SelectAccount
        :label="$t('transactions-from')"
        :value="state.fromId"
        :options="accounts"
        :rates="rates"
        backdrop
        searchable
        :disabled="state.toDirection === 'external'"
        :disabled-class="withdraw.disabledSelect"
        :hide-chevron="state.toDirection === 'external'"
        :loading="state.fromId === -1 || !activeAccount || state.loading"
        :error="state.error.selectFrom"
        @change="(v) => onSetDirection(v, 'from')" />
      <InputText
        v-if="state.toDirection === 'user' && !userStore.user.master_email"
        :label="`${t('transactions-to')} (${t('label.setRecipientEmail')})`"
        type="email"
        autocomplete="email"
        inputmode="email"
        :disabled="!!userStore.user.master_email"
        :autofocus="!userStore.user.master_email"
        :value="toEmail.value.value"
        :error="toEmail.errorMessage.value"
        @input="toEmail.setValue" />
      <InputText
        v-if="state.toDirection === 'external'"
        :label="`${t('transactions-to')} (${t('label.setRecipientWallet', {
          c: 'USDT TRC20',
        })})`"
        type="text"
        autocomplete="on"
        inputmode="text"
        name="external-usdt-address"
        autofocus
        :value="toAddress.value.value"
        :error="toAddress.errorMessage.value"
        @input="toAddress.setValue" />

      <div :class="withdraw.inputs">
        <InputCurrency
          :label="$t('Transfer amount')"
          :value="state.fromAmount"
          :currency="String(activeAccount?._currency?.symbol)"
          :class="[
            withdraw[`input-${state.toDirection === 'external' || false}`],
          ]"
          data-cy="withdraw-amount"
          :max="fromAmountMax"
          :error="state.error.inputFrom"
          :helper-text="
            state.toDirection === 'external' &&
            Number(state.fromAmount) < fromAmountMin
              ? t('errors.minimumAmount', {
                  a: fromAmountMin,
                  c: ` ${activeAccount?._currency?.iso_code}`,
                })
              : undefined
          "
          :autofocus="!!userStore?.user?.master_email"
          @change="(v) => changeAmount(v, 'fromAmount')" />
        <InputCurrency
          v-if="state.toDirection === 'external'"
          :label="$t('label.amountEnrolled')"
          :value="state.toAmount"
          :currency="String(activeAccount?._currency?.symbol)"
          :class="[
            withdraw[`input-${state.toDirection === 'external' || false}`],
          ]"
          data-cy="receive-amount"
          :error="state.error.inputTo"
          @change="(v) => changeAmount(v, 'toAmount')" />
        <Fee
          :fee-percent="
            state.toDirection === 'external' ? externalFee * 100 : 0
          "
          :fee-value="
            prepareAccountBalance(
              exchangeFee,
              String(activeAccount?._currency?.iso_code)
            )
          "
          :fee-currency="String(activeAccount?._currency?.symbol)"
          :fee-helper="
            t('label.atLeast', {
              v: `${minFeeAmount} ${activeAccount?._currency?.iso_code}`,
            })
          "
          hide-fee-value
          filled
          :class="withdraw.fee" />
      </div>

      <TextArea
        v-if="state.toDirection === 'external'"
        :label="t('label.transferReason')"
        :placeholder="t('label.setTransferReason')"
        :value="reason.value.value"
        :error="reason.errorMessage.value"
        :maxlength="200"
        counter
        @input="reason.setValue" />

      <!--        Messages & Alerts -->
      <div
        v-if="state.toDirection === 'external'"
        :class="withdraw.messages">
        <Message :class="withdraw.message">
          <ul>
            <li>
              <span :class="withdraw['message-row']">
                <span>
                  {{
                    t("withdraw.toExternalMessage.p1", {
                      n: "Tether (TRC20)",
                    })
                  }}
                </span>
                <DynamicIcon
                  name="usdt"
                  path="gateway" />
                <DynamicIcon
                  name="trx"
                  path="gateway" />
              </span>
            </li>
            <li>
              {{
                t("withdraw.toExternalMessage.p2", {
                  c: "USDT",
                  d: "14",
                })
              }}
            </li>
            <li
              v-if="
                ['welcome', 'scale'].includes(userStore.actual) ||
                !userStore.actual
              ">
              {{
                t("label.minAmountForWithdraw", {
                  a:
                    prepareAccountBalance(
                      String(fromAmountMin),
                      String(activeAccount?._currency?.iso_code)
                    ) +
                    " " +
                    activeAccount?._currency?.iso_code,
                })
              }}
            </li>
          </ul>
          <UiButton
            v-if="
              ['welcome', 'scale'].includes(userStore.actual) ||
              !userStore.actual
            "
            :title="t('btn.reduceMinWithdrawAmount')"
            size="normal"
            type="orange"
            :class="[withdraw.button, withdraw['message-button']]"
            @click="$router.push('/app/settings/verification')" />
        </Message>
        <Message
          v-if="state.toDirection === 'external'"
          type="alert"
          :class="withdraw.message">
          <ul>
            <li>
              {{ t("withdraw.toExternalMessage.p3", { c: "USDT" }) }}
            </li>
            <li>
              {{ t("withdraw.toExternalMessage.p4") }}
            </li>
          </ul>
        </Message>
      </div>
      <UiButton
        :title="$t('continue')"
        :disabled="
          (state.toDirection === 'external' &&
            (!toAddress.value.value || !!toAddress.errorMessage.value)) ||
          (state.toDirection === 'external' &&
            (!reason.value.value || !!reason.errorMessage.value)) ||
          (state.toDirection === 'user' &&
            (!toEmail.value.value || !!toEmail.errorMessage.value)) ||
          Object.keys(state.error).length !== 0 ||
          Number(state.fromAmount) <= 0 ||
          Number(state.toAmount) <= 0
        "
        size="normal"
        :class="withdraw.button"
        @click="emit('on-change-step', 3)" />
    </template>

    <!--    Confirm Transaction-->
    <template v-if="props.step === 3">
      <Confirm
        :from="activeAccount"
        :to="
          userStore.user.master_email
            ? ''
            : state.toDirection === 'user'
            ? toEmail.value.value
            : toAddress.value.value
        "
        :amount="Number(state.toAmount)"
        :amount-currency="
          state.toDirection === 'user' ? activeAccount?._currency?.symbol : '₮'
        "
        :fee-currency="String(activeAccount?._currency?.symbol)"
        :fee-percent="state.toDirection === 'external' ? externalFee * 100 : 0"
        :fee-value="
          prepareAccountBalance(
            exchangeFee,
            String(activeAccount?._currency?.iso_code)
          )
        "
        :fee-helper="
          t('label.atLeast', {
            v: `${minFeeAmount} ${activeAccount?._currency?.iso_code}`,
          })
        "
        :blocked="state.toDirection === 'user' ? blockedState : undefined"
        :loading="state.loading"
        @on-submit="onSubmitTransaction(state.toDirection)"
        @on-unblock="unblockWithdraw" />
    </template>
  </div>
</template>

<style lang="scss" module="withdraw">
.root {
  @apply flex flex-col gap-8 h-full;
}

.inputs {
  @apply grid grid-cols-2 gap-x-3 gap-y-2;
}

.input {
  &-true {
    @apply col-span-2 md:col-span-1;
  }

  &-false {
    @apply col-span-2;
  }
}

.fee,
.rate {
  @apply col-span-2;
}

.rate {
  @apply mt-3;
}

.button {
  @apply mt-auto md:mt-0;
}

.messages {
  @apply flex flex-col gap-3;
}

.message {
  & ul {
    @apply list-disc pl-4 flex flex-col gap-3 text-normal leading-[1.2];
  }

  &-row {
    @apply flex flex-wrap items-end gap-1;

    & svg {
      @apply w-[18px] h-[18px];
    }
  }

  &-button {
    @apply mt-4;
  }
}

.disabledSelect {
  @apply opacity-100 border-neutral-200;
}
</style>
