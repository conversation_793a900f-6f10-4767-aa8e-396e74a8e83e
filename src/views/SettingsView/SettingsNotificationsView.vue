<script lang="ts" setup>
import SettingsNotificationsServices from "@/components/SettingsNotifications/SettingsNotificationsServices.vue";
import SettingsNotifications from "@/components/SettingsNotifications/SettingsNotifications.vue";
import type { TUserSecuritySettings } from "@/types/api/TUserSecuritySettings";
import Simplebar from "simplebar-vue";

defineProps<{
  settings: TUserSecuritySettings;
}>();
</script>

<template>
  <Simplebar>
    <div class="flex flex-col gap-2 relative">
      <h1 class="text-5 font-semibold">
        {{ $t("settings-page.navigation.notifications") }}
      </h1>

      <div class="flex flex-col gap-6 min-w-min">
        <div class="flex flex-col gap-3">
          <p>
            {{ $t("settings-page.notifications.whereReceiveNotifications") }}
          </p>

          <SettingsNotificationsServices :settings="settings" />
        </div>

        <div class="mt-6">
          <SettingsNotifications :settings="settings" />
        </div>
      </div>
    </div>
  </Simplebar>
</template>
