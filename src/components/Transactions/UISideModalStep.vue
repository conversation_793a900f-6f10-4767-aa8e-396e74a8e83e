<script setup lang="ts">
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

const props = withDefaults(
  defineProps<{
    title?: string;
    isOpen: boolean;

    step: string | number;
    numSteps: number;
  }>(),
  {
    title: "",
  }
);

const emit = defineEmits([
  "prev-step",
  "next-step",
  "to-start",
  "to-finish",
  "to-step",
  "close",
]);
</script>

<template>
  <UISideModal
    :is-open="props.isOpen"
    @close="emit('close')">
    <template #title>
      <UIButton
        icon-only
        color="grey-free"
        size="s"
        :hidden="props.step === 1"
        @click="emit('prev-step')">
        <DynamicIcon
          name="arrow-left-thin"
          class="w-6 h-6 text-fg-secondary" />
      </UIButton>
      <slot name="title" />
    </template>
    <template #content>
      <slot name="content" />
    </template>
  </UISideModal>
</template>
