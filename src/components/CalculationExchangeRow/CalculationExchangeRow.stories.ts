import type { Meta, StoryObj } from "@storybook/vue3";
import type { ComponentProps } from "vue-component-type-helpers";
import CalculationExchangeRow from "@/components/CalculationExchangeRow/CalculationExchangeRow.vue";
import Calculation from "@/components/Calculation/Calculation.vue";

const meta: Meta<ComponentProps<typeof CalculationExchangeRow>> = {
  title: "CalculationExchangeRow",
  component: CalculationExchangeRow,
  tags: ["autodocs"],
  render(args: any) {
    return {
      components: {
        CalculationExchangeRow,
        Calculation,
      },
      setup() {
        return { args };
      },
      template: `<div class="flex w-full">
        
        <Calculation>
            <CalculationExchangeRow :value="args.value"/>
        </Calculation>
        
      </div>`,
    };
  },
  argTypes: {
    value: { control: "text" },
  },
  args: {
    value: "1 USDT = 0,99 $",
  },
};

export default meta;

type Story = StoryObj<typeof CalculationExchangeRow>;

export const Default: Story = {};
