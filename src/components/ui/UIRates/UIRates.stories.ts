import type { <PERSON>a, StoryObj } from "@storybook/vue3";
import { ref } from "vue";
import UIRates from "./UIRates.vue";
import type { ComponentProps } from "vue-component-type-helpers";

const meta: Meta<ComponentProps<typeof UIRates>> = {
  title: "UI/UIRates",
  component: UIRates,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<ComponentProps<typeof UIRates>>;

/*
 *👇 Render functions are a framework specific feature to allow you control on how the component renders.
 * See https://storybook.js.org/docs/api/csf
 * to learn how to use render functions.
 */
export const Primary: Story = {
  render: (args) => ({
    components: { UIRates },
    setup() {
      const rate = ref<number | null>(null);
      return { args, rate };
    },
    template: `
      <UIRates v-bind="args" v-model="rate" /><br>
      Rate {{rate}}
    `,
  }),
  args: {},
};
