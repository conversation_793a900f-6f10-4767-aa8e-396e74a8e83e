import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import type {
  TNotificationService,
  TNotificationType,
} from "@/types/dictionary/dictionary.types";
import { useDictionary } from "@/stores/dictionary";
import type { TNotifyServiceId } from "@/types/api/TUserSecuritySettings";
import { useUserSecuritySettings } from "@/composable";
import OneSignalService from "@/services/OneSignalService";

export const useNotificationSettings = () => {
  const { t } = useI18n();
  const { dictionary } = useDictionary();
  const { userSecuritySettings } = useUserSecuritySettings({
    immediate: false,
  });

  const isWebPushEnabled = ref(false);
  const isTelegramActivated = computed(
    () => userSecuritySettings.value?.telegram.activated
  );
  const isWhatsAppActivated = computed(
    () => userSecuritySettings.value?.whatsapp.activated
  );

  const servicesDescription = computed<
    Record<string, { name: string; description: string }>
  >(() => ({
    tg: {
      name: t("settings-page.notification.services.tg.name"),
      description: t("settings-page.notification.services.tg.description"),
    },
    email: {
      name: t("settings-page.notification.services.email.name"),
      description: t("settings-page.notification.services.email.description"),
    },
    database: {
      name: t("settings-page.notification.services.database.name"),
      description: t(
        "settings-page.notification.services.database.description"
      ),
    },
    "web-push": {
      name: t("settings-page.notification.services.web-push.name"),
      description: t(
        "settings-page.notification.services.web-push.description"
      ),
    },
    whatsapp: {
      name: t("settings-page.notification.services.whatsapp.name"),
      description: t(
        "settings-page.notification.services.whatsapp.description"
      ),
    },
  }));

  const availableServices = computed<
    (TNotificationService & { _id: TNotifyServiceId; disabled?: boolean })[]
  >(() => {
    return (
      dictionary?.notificationServices
        ?.filter((item: TNotificationService) => item.slug !== "database")
        ?.map((item: TNotificationService) => ({
          ...item,
          _id: `${item.id}` as TNotifyServiceId,
          ...servicesDescription.value[item.slug],
          ...(item.slug === "tg"
            ? { disabled: !isTelegramActivated.value }
            : {}),
          ...(item.slug === "web-push"
            ? { disabled: !isWebPushEnabled.value }
            : {}),
          ...(item.slug === "whatsapp"
            ? { disabled: !isWhatsAppActivated.value }
            : {}),
        })) ?? []
    );
  });

  const availableNotifications = computed(() => {
    return (
      dictionary?.notificationTypes?.filter(
        (item: TNotificationType) => item.slug !== "team-info"
      ) ?? []
    );
  });

  const checkPush = async () => {
    isWebPushEnabled.value = await OneSignalService.isSubscribed();
  };

  onMounted(async () => {
    await checkPush();
  });

  return {
    isWebPushEnabled,
    availableServices,
    availableNotifications,
  };
};
