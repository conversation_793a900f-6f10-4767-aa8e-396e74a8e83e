import { useUserStore } from "@/stores/user";
import { computed } from "vue";
import { EXTENDED_VERIFICATION_DOMAINS_SUFFIXES } from "@/constants/extended_verification_domains_suffixes";

/**
 * Check if the user email domain includes one of the extended verification domains suffixes
 *
 * @category Composables
 */
export function useExtendedVerificationEmail() {
  const { user } = useUserStore();

  const isExtendedVerificationEmail = computed(() => {
    if (!user.email) {
      return false;
    }

    const domain = user.email.split("@")[1];

    return EXTENDED_VERIFICATION_DOMAINS_SUFFIXES.some((suffix) =>
      domain.toLowerCase().includes(suffix)
    );
  });

  return {
    isExtendedVerificationEmail,
  };
}
