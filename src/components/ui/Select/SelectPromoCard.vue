<template>
  <div class="flex flex-col">
    <span
      v-if="label && labelType === 'outside'"
      :class="labelClass"
      class="text-greyscale-600">
      {{ label }}
    </span>
    <div
      class="UiSelect"
      :class="{
        [`${selectClass}`]: true,
        ['border-secondary-base']: showDropdown,
        ['border-greyscale-300']: !showDropdown,
      }"
      @click="toggleDropdown">
      <div
        v-if="label && labelType === 'inside'"
        class="absolute left-0 top-0 text-sm text-greyscale-600 p-3">
        {{ label }}
      </div>
      <span class="selected-option flex justify-between items-center">
        <span
          class="option-text capitalize"
          :class="showDropdown ? '' : 'text-greyscale-500'">
          <template v-if="!selected">
            {{ placeholder }}
          </template>
          <slot
            v-if="selected"
            :option="selected" />
        </span>
        <slot
          v-if="selected"
          name="withIcon"
          :option="selected" />
        <ChevronDown
          class="w-6 h-6"
          :class="{ 'rotate-180': showDropdown }" />
      </span>

      <transition name="fade-slide-down">
        <div
          v-if="showDropdown"
          class="dropdown-options-container z-10"
          data-cy="dropdown_container"
          v-click-outside="toggleDropdown">
          <div
            v-for="(option, index) in options"
            :key="index"
            class="dropdown-options"
            :class="{ selected: option === selected }">
            <div
              class="dropdown-options--cell"
              @click="selectOption(option)">
              <div class="font-extrabold text-sm">
                <slot :option="option" />
              </div>
              <slot
                name="withIcon"
                :option="option" />
            </div>
            <UiDivider
              v-if="divider && index !== options.length - 1"
              class="mx-auto w-[90%]" />
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import UiDivider from "~/components/ui/Divider/Divider";
import ChevronDown from "~/assets/svg/icon/chevron-down.svg";

export default {
  name: "UiSelect",
  components: { UiDivider, ChevronDown },
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "Select",
    },
    label: {
      type: String,
      default: "",
    },
    labelType: {
      type: String,
      default: "outside",
    },
    labelClass: {
      type: String,
      default: "text-sm font-medium pb-1",
    },
    selectClass: {
      type: String,
      default: "py-4 pl-4",
    },
    defaultValue: {
      type: [String, Number, Object],
      default: null,
    },
    divider: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultSelected: "",
      showDropdown: false,
    };
  },
  computed: {
    selected() {
      return this.defaultSelected || "";
    },
  },
  watch: {
    defaultSelected() {
      this.defaultSelected = this.selected ? this.selected : "";
    },
    defaultValue(newValue) {
      this.defaultSelected = newValue;
      this.$emit("change", this.defaultSelected);
    },
  },
  mounted() {
    this.defaultSelected = this.defaultValue ? this.defaultValue : "";
    if (this.defaultSelected != "") {
      this.$emit("change", this.defaultValue);
    }
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    selectOption(option) {
      this.defaultSelected = option;
      this.$emit("change", this.selected);
    },
  },
};
</script>

<style lang="scss">
.UiSelect {
  @apply relative cursor-pointer w-full rounded-base border transition outline-none pr-3;
  .selected-option {
    @apply relative;
  }
}

.chevron-up {
  & svg {
    @apply mt-2 mb-auto;
  }
}

.dropdown-options-container {
  @apply absolute left-0 top-full bg-white w-full overflow-y-auto rounded-[12px] mt-1 max-h-[20rem];
  box-shadow: 4px 12px 32px rgba(93, 106, 131, 0.1);
}

.dropdown-options:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(241 242 244 / var(--tw-bg-opacity));
}

.dropdown-options--cell {
  @apply p-4;

  &:hover {
    //@apply bg-greyscale-50;
  }
}

.dropdown-options.selected {
  .dropdown-options--cell {
    //@apply bg-greyscale-100;
  }
}
</style>
