<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import SecondModal from "@/components/ui/Modal/SecondModal.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import InputText from "@/components/ui/Input/InputText.vue";

import { UserService } from "@modules/services/user";
import { useState } from "@/helpers/utilities";
import { useEmail } from "@/helpers/validation";
import { computed } from "vue";
import Config from "@/config/env";

const [error, setError] = useState<string>("");
const [loading, setLoading] = useState<boolean>(false);
const [linkSent, setLinkSent] = useState<boolean>(false);
const email = useEmail();
defineEmits(["close"]);

const buttonDisabled = computed<boolean>(
  () =>
    email.value.value.length === 0 ||
    !!email.errorMessage.value ||
    linkSent.value ||
    loading.value ||
    !!error.value
);

const sendLink = async () => {
  if (buttonDisabled.value) {
    return;
  }

  setLoading(true);

  const recaptcha_token = await window.grecaptcha.execute(Config.grecaptcha, {
    action: "submit",
  });

  const result = await UserService.resetPassword({
    email: email.value.value,
    recaptcha_token,
  });

  if (!result.status) {
    setError(result.message || "Something went wrong");
  }

  setLinkSent(result.status);

  setLoading(false);
};
</script>

<template>
  <SecondModal
    :wrapper-class="$style.modalWrapper"
    :class="$style.modal">
    <div class="flex flex-col gap-8">
      <div class="flex flex-col gap-3">
        <div class="flex items-start justify-between gap-2.5 w-full">
          <h4>{{ $t("recovery.accountRecovery") }}</h4>

          <DynamicIcon
            name="close"
            class="w-6 h-6 cursor-pointer"
            @click="$emit('close')" />
        </div>

        <span class="text-xl text-neutral-900">
          {{ $t("recovery.text") }}
        </span>
      </div>

      <div class="flex flex-col gap-2">
        <InputText
          :label="$t('email')"
          :value="email.value.value"
          :error="email.errorMessage.value"
          @input="email.setValue"
          @focus="() => setError('')" />

        <span
          v-if="error"
          class="text-error-base">
          {{ error }}
        </span>

        <div
          v-if="linkSent"
          class="rounded-[10px] bg-neutral-200 px-4 py-5">
          {{ $t("recovery.sent") }}
        </div>
      </div>

      <UiButton
        v-if="!linkSent"
        :class="$style.button"
        :disabled="buttonDisabled"
        :title="$t('recovery.sendLink')"
        @click="sendLink" />

      <UiButton
        v-else
        type="gray"
        :class="$style.button"
        :title="$t('button.close')"
        @click="$emit('close')" />
    </div>
  </SecondModal>
</template>

<style lang="scss" module>
.modal {
  background: rgba(0, 0, 0, 0.72);
}
.modalWrapper {
  @apply p-5;
}
.button {
  @apply font-normal;
}
</style>
