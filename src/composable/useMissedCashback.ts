import { computed, ref } from "vue";
import { useUserTransactionsVueQueryService } from "@/composable/UserTransactions/useUserTransactionsVueQueryService";
import type { ITransactionSummaryResponse } from "@modules/services/transactions/types";
import { useUserStore } from "@/stores/user";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";

const paymentsSummary = ref<ITransactionSummaryResponse | null>(null);

const missedCashback = computed<number>(() => {
  const approvedSum = paymentsSummary.value?.approved_sum;
  const lostCashback = approvedSum ? Math.round((approvedSum * 3) / 100) : 0;

  if (lostCashback > 3000) {
    return 3000;
  }

  return lostCashback;
});

const missedCashbackString = computed<string>(
  () => `${missedCashback.value} $`
);

const transactionsService = useUserTransactionsVueQueryService();

/**
 * @deprecated - will be removed soon
 * Composable for getting missed cashback
 *
 * @example
 * const { missedCashback, missedCashbackString } = useMissedCashback();
 *
 * @category Composables
 */
export function useMissedCashback() {
  const { suspense, isLoading } = transactionsService.getSummary({});
  const userStore = useUserStore();
  const { subscriptionsStatus } = useSubscriptionsInfo();

  const isMissedCashbackVisible = computed(() => {
    return !userStore.isTeamMember && !subscriptionsStatus.value;
  });

  suspense().then((value) => {
    paymentsSummary.value = value.data!;
  });

  return {
    missedCashback,
    missedCashbackString,
    isLoading,
    isMissedCashbackVisible,
  };
}
