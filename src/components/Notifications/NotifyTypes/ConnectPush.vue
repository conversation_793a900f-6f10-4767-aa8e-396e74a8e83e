<template>
  <div :class="$style.root">
    <h4>
      {{ $t("notification.push.title") }}
    </h4>
    <p>
      {{ $t("notification.push.text") }}
    </p>
    <ui-button
      type="orange"
      size="small"
      :title="$t('notification.push.btnText')"
      :class="[$style.btnMobile]"
      @click="onClick" />
  </div>
</template>

<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import OneSignalService from "@/services/OneSignalService";
import type { TUserSettings } from "@/types/user/user.types";

interface Props {
  isMobile?: boolean;
  settings?: TUserSettings;
}

const props = defineProps<Props>();

const onClick = () => {
  OneSignalService.subscribe(props?.settings?.onesignal_hash);
};
</script>

<style lang="scss" module>
.root {
  @apply bg-neutral-100 px-5 py-[18px] rounded-[14px] flex flex-col;

  & h4 {
    @apply mb-1;
    color: var(--neutral-n-900, #15191d);
    /* 18/600 */
    font-family: ALS Granate VF;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px; /* 144.444% */
  }

  & p {
    @apply text-[color:var(--neutral-n-600,#686A6E)] text-[12px] not-italic font-normal leading-4;
    @apply mb-5;
    /* 12/400 */
    font-family: ALS Granate VF;
  }

  & button {
    @apply rounded-base font-semibold;
    height: 32px;

    & span {
      /* 12/600 */
      font-family: ALS Granate VF;
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 16px; /* 133.333% */
    }
  }
}

.btnMobile {
  font-size: 12px;
  padding: 8px;
}
</style>
