<script lang="ts" setup>
/* eslint-disable max-len */
import { CardService } from "@modules/services/card";
import BorderedTable from "@/components/ui/Table/BorderedTable.vue";
import Header from "@/components/Cards/StandardCreateCard/Stages/SelectBin/BinTable/Header.vue";
import Item from "@/components/Cards/StandardCreateCard/Stages/SelectBin/BinTable/Item.vue";
import Nothing from "@/components/Cards/StandardCreateCard/Stages/SelectBin/BinTable/NotFound.vue";
import UiLoader from "@/components/ui/Loader/Loader.vue";
import GridItems from "@/components/Cards/StandardCreateCard/Stages/SelectBin/GridItems/Index.vue";
import { computed, onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { ExceptionCause, ExceptionReason } from "@/config/Exception";
import { isMobile, isTablet } from "@/helpers";
import {
  binConfig,
  cardsWithoutCashback,
  feeTopupPrivate,
} from "@/config/cards";
import { UserService } from "@modules/services/user";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useSubscriptionsList } from "@/composable/useSubscriptionsList";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { loggerConsole } from "@/helpers/logger/Logger";
import UIModal from "@/components/ui/UIModal/UIModal.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { RouteName } from "@/constants/route_name";
import UIDialog from "@/components/ui/UIDialog/UIDialog.vue";
import type { IBinInfo } from "@/components/Cards/StandardCreateCard/Stages/SelectBin/types";
import type { IBin } from "@/types/card";

defineOptions({ name: "StandardCreateCardSelectBin" });

export interface Props {
  slug: string;
}

const emit = defineEmits(["onSelectBin"]);
const props = defineProps<Props>();
const { t } = useI18n();
const loading = ref<boolean>(false);
const logger = loggerConsole();
const userTariff = ref<Array<any>>([]);
const binsData = ref<any[]>([]);
const { subscriptionsInfo } = useSubscriptionsInfo();
const { currentMainTariff } = useSubscriptionsList();

const improveTariffModalState = reactive({
  isOpen: false,
  bin: "",
  startsWith: "",
  tariffNames: {
    s: "Small",
    m: "Medium",
    l: "Large",
  },
});

const openImproveTariffModal = (bin: string, tariffs: string[] = []) => {
  improveTariffModalState.isOpen = true;
  improveTariffModalState.bin = bin;
  improveTariffModalState.startsWith = tariffs[0];
};

const subscriptionStatus = computed(() => {
  return subscriptionsInfo.value?.status !== 0;
});

const cashbackPercent = computed<string>(() => {
  if (cardsWithoutCashback.includes(props.slug)) {
    return "0";
  }
  return subscriptionsInfo.value?.cashback_percent ?? "0";
});

const template = computed<string>(() => {
  const base = `gap-0 md:gap-3 `;
  /* eslint-disable */
  const templateUser =
    "grid-cols-1" +
    " md:grid-cols-[minmax(85px,_100px)_minmax(70px,_90px)_minmax(120px,_180px)_minmax(60px,_70px)_minmax(110px,_200px)_minmax(120px,_200px)_minmax(100px,_200px)_minmax(140px,_260px)_minmax(100px,_140px)]";
  /* eslint-enable */
  return base + templateUser;
});

const getCardCode = () => props.slug;

const loadUserTariff = async () => {
  const response = await UserService.tariff();

  if (!response.status) {
    useCallToast({
      title: t("toast.error"),
      body: response.message || t("toast.error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });

    logger.error(ExceptionCause.USER_TARIFF, ExceptionReason.FAILED_LOAD);
  }
  userTariff.value = response?.data || [];
};
const privateCashback = cardsWithoutCashback.includes(props.slug) ? "0" : "3";

const feeTopupPrivateForCard = computed(() =>
  cardsWithoutCashback.includes(props.slug)
    ? selectedTariff.value.fee_topup
    : feeTopupPrivate
);

const selectedTariff = computed(() =>
  userTariff.value.find((tariff: any) => tariff.slug === props.slug)
);

//Load Bins
const loadBin = async (type: string) => {
  binsData.value = [];
  let response;
  // PST-T-2806: need to fix when backend will be ready
  if (type === "ultima") {
    await Promise.all([
      CardService.bins({ params: { type } }),
      CardService.bins({ params: { type: "ultima-3ds" } }),
    ]).then(([ultimaBins, ultima3dsBins]) => {
      const status = ultimaBins.status && ultima3dsBins.status;

      let ultimaBinsData: IBin[] = [];
      if (Array.isArray(ultimaBins.data)) {
        ultimaBinsData = ultimaBins.data.map((item) => {
          item.slug = "ultima";
          return item;
        });
      }

      let ultima3dsBinsData: IBin[] = [];
      if (Array.isArray(ultima3dsBins.data)) {
        ultima3dsBinsData = ultima3dsBins.data.map((item) => {
          item.slug = "ultima-3ds";
          return item;
        });
      }
      const data = [...ultimaBinsData, ...ultima3dsBinsData];

      response = { status, data };
    });
  } else {
    response = await CardService.bins({ params: { type } });
  }

  if (!response?.status) {
    logger.error(ExceptionCause.CARD_BIN, ExceptionReason.FAILED_LOAD, {
      bin: type,
    });
  }

  binsData.value = response?.data || [];
};

const bins = computed(() => {
  if (binsData.value.length) {
    return binsData.value
      .map((item) => {
        const found = binConfig.find((i: any) => {
          return i.bin === item.bin && i.slug === props.slug;
        });
        const foundConfigItem: IBinInfo | null = found
          ? ({ ...found } as IBinInfo)
          : null;
        if (foundConfigItem) {
          foundConfigItem.under_maintenance = item.under_maintenance;

          if (subscriptionStatus.value) {
            foundConfigItem.private = false;
          }
          if (item.slug) {
            foundConfigItem.slug = item.slug;
          }
        }
        return foundConfigItem ?? item;
      })
      .sort((a) => (a.private ? 1 : -1));
  }
  return [];
});

const binsPrivate = computed(() => {
  if (bins.value.length) {
    if (!selectedTariff.value) return [];

    const { fee_topup: feeTopUp, card_price: cardPrice } = selectedTariff.value;
    const ret: any[] = [];
    bins.value.forEach((v) => {
      if (v.private) {
        v.default = {
          depositFee: feeTopUp,
          monthlyPayment: cardPrice,
        };
        return;
      }
      ret.push({
        ...v,
        private: true,
        default: {
          depositFee: feeTopUp,
          monthlyPayment: cardPrice,
        },
      });
    });
    return ret;
  }
  return [];
});

const selectedBin = ref();
const selectedSlug = ref();
const onSelectBin = (bin: string, slug: string) => {
  const binData = bins.value.find((item) => item.bin === bin);
  if (binData?.under_maintenance) {
    selectedBin.value = bin;
    selectedSlug.value = slug;
    isOpenSelectBinUnderMaintenanceModal.value = true;
  } else {
    emit("onSelectBin", bin, slug);
  }
};

const binsGrid = ref<IBinInfo[]>();

const parseGateway = (bin: string) => {
  if (!bin) return "";
  if (!bin.length) return "";
  return bin === "4" ? "visa" : "mastercard";
};
const initBinsGrid = () => {
  const {
    fee_transaction_amount: feeTransactionAmount,
    fee_topup: feeTopUp,
    card_price: cardPrice,
  } = selectedTariff.value;

  const resultBins: IBinInfo[] = bins.value.map((bin: IBinInfo) => {
    return {
      ...bin,

      gateway: parseGateway(bin?.bin),

      transferFee: feeTransactionAmount,
      depositFee: bin?.private ? String(feeTopupPrivateForCard) : feeTopUp,
      monthlyPayment: bin?.private ? "0" : cardPrice,

      cashback: cashbackPercent.value,

      private: bin?.private,
      featured: bin?.featured || false,
      secure: bin?.secure || false,
    };
  });

  let resultBinsPrivate: IBinInfo[] = [];

  if (!subscriptionStatus.value) {
    resultBinsPrivate = resultBins.map((bin: IBinInfo) => {
      return {
        ...bin,
        //private options
        cashback: privateCashback,

        monthlyPayment: String(0),
        depositFee: String(feeTopupPrivateForCard),

        private: true,
        default: {
          cashback: "0",
          depositFee: feeTopUp,
          monthlyPayment: cardPrice,
        },
      };
    });
  }

  const allBins: IBinInfo[] = [...resultBins, ...resultBinsPrivate];

  binsGrid.value = allBins;
};

onMounted(async () => {
  await Promise.allSettled([loadBin(getCardCode()), loadUserTariff()]);

  initBinsGrid();
  loading.value = false;
});

const isOpenSelectBinUnderMaintenanceModal = ref<boolean>(false);
const confirmSelectBinUnderMaintenance = () => {
  emit("onSelectBin", selectedBin.value, selectedSlug.value);
};
const cancelSelectBinUnderMaintenance = () => {
  isOpenSelectBinUnderMaintenanceModal.value = false;
};
</script>

<template>
  <div :class="stageSelectBin.root">
    <template v-if="!loading && bins?.length && selectedTariff">
      <template v-if="bins?.length">
        <BorderedTable
          v-if="!isMobile && !isTablet"
          :table-class="stageSelectBin.table"
          :item-class="stageSelectBin.item">
          <Header
            :template="template"
            :slug="slug" />

          <template v-if="bins.length">
            <Item
              v-for="bin in bins || []"
              :key="bin.bin"
              :template="template"
              :data="bin"
              :tariff="
                bin.private
                  ? {
                      ...selectedTariff,
                      card_price: 0,
                      fee_transaction_amount: 0,
                      fee_topup: feeTopupPrivateForCard,
                    }
                  : selectedTariff
              "
              :subscription="bin.private ? true : subscriptionStatus"
              :subscription-level="
                currentMainTariff?.subscription_tariff.name
                  .slice(0, 1)
                  .toLowerCase()
              "
              :current-main-tariff-name="
                currentMainTariff?.subscription_tariff.name
              "
              :cashback="bin.private ? privateCashback : cashbackPercent"
              @on-select-bin="(bin, slug) => onSelectBin(bin, slug)"
              @improve-tariff="openImproveTariffModal" />
            <template v-if="!subscriptionStatus">
              <Item
                v-for="bin in binsPrivate || []"
                :key="bin.bin"
                :template="template"
                :data="bin"
                :tariff="{
                  ...selectedTariff,
                  card_price: 0,
                  fee_transaction_amount: 0,
                  fee_topup: feeTopupPrivateForCard,
                }"
                :subscription="true"
                :cashback="privateCashback"
                @on-select-bin="(bin, slug) => onSelectBin(bin, slug)" />
            </template>
          </template>
        </BorderedTable>
        <template v-else>
          <GridItems
            :bins="bins"
            :bins-private="binsPrivate"
            :selected-tariff="selectedTariff"
            :private-cashback="privateCashback"
            :cashback-percent="cashbackPercent"
            :fee-top-up-private-for-card="feeTopupPrivateForCard"
            :subscription-status="subscriptionStatus"
            :subscription-level="
              currentMainTariff?.subscription_tariff.name
                .slice(0, 1)
                .toLowerCase()
            "
            @on-selected-bin="(bin, slug) => onSelectBin(bin, slug)" />
        </template>

        <div
          v-if="
            [
              'adv',
              'fb-prem',
              'advertisement-cards',
              'google-cards',
              'facebook-cards',
              'tik-tok-cards',
            ].includes(props.slug)
          "
          :class="stageSelectBin.message">
          <p>
            {{
              t("createCard.warnMessage.onlyAdv", {
                a: selectedTariff.name,
                b: "30%",
              })
            }}
          </p>
          <p
            v-if="props.slug !== 'adv' && props.slug !== 'advertisement-cards'">
            {{
              t("createCard.warnMessage.onlyAdvBySlug", {
                a: selectedTariff.name,
                b: "5%",
              })
            }}
          </p>
        </div>
      </template>
      <Nothing v-else />
    </template>
    <UiLoader v-else />
    <UIModal
      :is-open="improveTariffModalState.isOpen"
      :with-close-icon="true"
      @close="improveTariffModalState.isOpen = false">
      <template #title>
        <h2 class="text-2xl font-bold">
          {{ $t("improve-tariff-modal.title") }}
        </h2>
      </template>
      <template #content>
        <div class="flex flex-col">
          <p class="text-4.5 mt-6">
            {{ $t("improve-tariff-modal.your-current-tariff") }}
            <span class="font-bold">{{
              improveTariffModalState.tariffNames[
                currentMainTariff?.subscription_tariff.name
                  .slice(0, 1)
                  .toLowerCase() as "s" | "m" | "l"
              ]
            }}</span
            >.
          </p>
          <p
            class="text-4.5 leading-6 mt-4"
            v-html="$t('improve-tariff-modal.text', { bin:
          improveTariffModalState.bin, tariff:
          improveTariffModalState.tariffNames[
          improveTariffModalState.startsWith as 's' | 'm' | 'l' ], })" />
          <RouterLink :to="{ name: RouteName.SUBSCRIPTION_TARIFF }">
            <UIButton
              color="black"
              class="w-full mt-4">
              {{ $t("Improve tariff") }}
            </UIButton>
          </RouterLink>
          <UIButton
            class="w-full mt-4"
            @click="improveTariffModalState.isOpen = false">
            {{ $t("Cancel") }}
          </UIButton>
        </div>
      </template>
    </UIModal>
    <UIDialog
      v-if="isOpenSelectBinUnderMaintenanceModal"
      :callback-confirm="confirmSelectBinUnderMaintenance"
      :callback-cancel="cancelSelectBinUnderMaintenance"
      :is-open="isOpenSelectBinUnderMaintenanceModal"
      :btn-cancel-text="$t('cards.select-another-card')"
      :btn-submit-text="$t('cards.release-it-anyway')"
      :text="$t('cards.payment-from-card-not-possible-during-technical-works')"
      :title="$t('cards.technical-work-is-underway')"
      @close="cancelSelectBinUnderMaintenance" />
  </div>
</template>

<style lang="scss" module="stageSelectBin">
.root {
  & * {
    @apply font-hauss;
  }

  @apply flex flex-col gap-10;
}

.table {
  @apply rounded-[6px] border border-neutral-150;

  & > * {
    @apply border-b border-neutral-150;
  }

  & > *:first-child {
    @apply rounded-t-[6px];
  }

  & > *:last-child {
    @apply border-none rounded-b-[6px];
  }
}

.item {
  @apply px-2 py-3;
  @apply border-b border-neutral-150;
  &:first-child {
    @apply rounded-t-[6px];
  }

  &:last-child {
    @apply border-none rounded-b-[6px];
  }
}

.message {
  @apply bg-neutral-100 rounded-[6px] p-4 flex flex-col gap-4;
  @apply text-normal text-neutral-800;

  & p {
    @apply leading-[1.2];
  }
}
</style>
