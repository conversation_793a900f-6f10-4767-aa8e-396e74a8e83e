import type { TAxiosRequestConfig, TPaginatedApiResponse } from "@/types/api";
import type {
  TPromoCode,
  TPromoCodeStatisticItem,
  TPromoCodeSummary,
} from "@/types/promoCode";

export enum ServiceBase {
  value = "promocode",
}

interface IPromoCodeFailedRes {
  success?: boolean;
  message?: string;
}

export interface IPromoCodeModel {
  code: string;
  status: number;
  created_at: string;
  stop_at: string;
  fields: {
    card_bonus_amount: string;
    card_buy_discount_percent: string;
  };
  id: number;
}

interface IPromoCodeData {
  data?: IPromoCodeModel;
}

export interface IPromoCodeCheckResponse
  extends IPromoCodeData,
    IPromoCodeFailedRes {}

export enum ServiceEndpoint {
  ATTACH = "attach",
  GET_PERSONAL = "getPersonal",
  POST_PERSONAL = "postPersonal",
  PERSONAL_URL = "personal",
  EDIT = "edit",
  CHECK = "check",
  LIST = "list",
  UPDATE = "update",
  STATISTIC = "statistics",
  SUMMARY = "summary",
}

// TODO: Fix any
export type TResponses = {
  [ServiceEndpoint.LIST]: Array<TPromoCode>;
  [ServiceEndpoint.GET_PERSONAL]: Array<TPromoCode>;
  [ServiceEndpoint.CHECK]: IPromoCodeCheckResponse;
  [ServiceEndpoint.POST_PERSONAL]: any;
  [ServiceEndpoint.EDIT]: any;
  [ServiceEndpoint.STATISTIC]: TPaginatedApiResponse<
    Array<TPromoCodeStatisticItem>
  >;
  [ServiceEndpoint.SUMMARY]: TPromoCodeSummary;
  [ServiceEndpoint.ATTACH]: {
    success: boolean;
    message: string;
  };
};

export type TPayloads = {
  [ServiceEndpoint.LIST]: undefined;
  [ServiceEndpoint.GET_PERSONAL]: undefined;
  [ServiceEndpoint.CHECK]: { params: { code: string } };
  [ServiceEndpoint.POST_PERSONAL]: { code: string };
  [ServiceEndpoint.EDIT]: any;
  [ServiceEndpoint.STATISTIC]: TAxiosRequestConfig<
    any,
    {
      page: number;
      per_page: number;
      groupBy: string;
      sort: string;
      direction: string;
      start?: string;
      stop?: string;
      promocode: string;
      referral: string;
    }
  >;
  [ServiceEndpoint.SUMMARY]: TAxiosRequestConfig<
    any,
    {
      promocode: string;
      referral: string;
      start?: string;
      stop?: string;
    }
  >;
  [ServiceEndpoint.ATTACH]: {
    code: string;
  };
};
