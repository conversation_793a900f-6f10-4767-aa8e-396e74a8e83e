<script lang="ts" setup>
import { computed, onMounted, watch } from "vue";
import SquareCheckbox from "@/components/ui/Checkbox/SquareCheckbox.vue";
import SelectLanguage from "@/components/ui/Select/SelectLanguage.vue";
import InputText from "@/components/ui/Input/InputText.vue";
import InputPassword from "@/components/ui/Input/InputPassword.vue";
import UiButtonSocial from "@/components/ui/Button/ButtonSocial.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import SocialButtons from "@/components/Auth/SocialButtons.vue";
import Banner from "@/components/Auth/Banner.vue";
import ChangeAuthMethod from "@/components/Auth/ChangeAuthMethod.vue";
import ChangeAuthMethodWithError from "@/components/Auth/ChangeAuthMethodWithError.vue";
import BlockedCountriesAgreement from "@/components/Auth/BlockedCountriesAgreement.vue";
import PrivacyAndTos from "@/components/Auth/PrivacyAndTos.vue";
import Auth, { authWithGoogleJwt, getUtm, initGoogle } from "@/helpers/auth";

import type { TAuthMethod } from "@/components/Auth/types";
import { useState } from "@/helpers/utilities";
import type { RouteLocationRaw } from "vue-router";
import { useRouter } from "vue-router";
import { useConfirmation, useEmail, usePassword } from "@/helpers/validation";
import {
  getFromPrivate,
  getHash,
  setFromPrivate,
  setRefHash,
} from "@/helpers/url";
import { TrackerService } from "@/helpers/tracker/tracker.service";
import { TrackerEvent } from "@/helpers/tracker/tracker.types";
import { useAppleLink, useGoogleLink } from "@/composable/Oauth";
import { useDictionary } from "@/stores/dictionary";
import { useUserStore } from "@/stores/user";
import Config from "@/config/env";
import { addCaptcha } from "@/helpers/captcha";
import { useAuth } from "@/composable/Auth";
import { useI18n } from "vue-i18n";
import { useExperiments } from "@/composable/useExperiments";
import { RouteName } from "@/constants/route_name";
import { LocalStorageKey } from "@/constants/local_storage_key";
import type { TGoogleJwtResponse } from "@/types/TGoogleJwtResponse";
import { SessionStorageKey } from "@/constants/session_storage_key";
import { useUserTariff } from "@/composable";
import { useSubscriptionPlusCardAutobuy1Experiment } from "@/composable/useSubscriptionPlusCardAutobuy1Experiment";

const { t } = useI18n();
const { getUser, user } = useUserStore();
const dictionaryStore = useDictionary();
const router = useRouter();
const email = useEmail();
const password = usePassword();
const confirmation = useConfirmation<string>("", password.value);
const googleLink = useGoogleLink();
const appleLink = useAppleLink();
const [loading, setLoading] = useState<boolean>(false);
const [capthaLoading, setcapthaLoading] = useState<boolean>(true);
const [terms, setTerms] = useState<boolean>(false);
const [countries, setCountries] = useState<boolean>(false);
const [authMethod, setAuthMethod] = useState<TAuthMethod>("");
const [error, setError] = useState<string>("");
const { updateUserTariff } = useUserTariff();
const authLogic = useAuth();
const masterHash =
  sessionStorage.getItem(SessionStorageKey.MASTER_HASH) || undefined;

const buttonDisabled = computed<boolean>(
  () =>
    !!email.errorMessage.value ||
    !!password.errorMessage.value ||
    !!confirmation.errorMessage.value ||
    email.value.value.length === 0 ||
    password.value.value.length === 0 ||
    confirmation.value.value.length === 0 ||
    !terms.value ||
    !countries.value ||
    loading.value ||
    capthaLoading.value
);

const redirectToApp = async (to: RouteLocationRaw) => {
  await router.push(to);
  updateUserTariff();
};

const setLocalStorageHideUnavailableCountriesUntilMs = () => {
  if (!localStorage) return;
  const countDays = 7;
  const dateUntil = Date.now() + 1000 * 60 * 60 * 24 * countDays;
  localStorage.setItem(
    LocalStorageKey.HIDE_UNAVAILABLE_COUNTRIES_UNTIL_MSEC,
    String(dateUntil)
  );
};

// signup methods
const signUpWithGoogleJwt = async (data: TGoogleJwtResponse) => {
  setLoading(true);
  const success = await authWithGoogleJwt(data);

  if (success) {
    const fromPrivate = getFromPrivate();
    const pushArg = {
      path: "/app",
      query: {},
    };

    if (fromPrivate) {
      setLocalStorageHideUnavailableCountriesUntilMs();
      pushArg.query = {
        p: !!fromPrivate,
      };
    }

    const utm = getUtm();
    await getUser({ params: utm });

    await redirectToApp(pushArg);
  }

  setLoading(false);
};
const logEvent = async (
  social: "telegram" | "google" | "email" | "apple" | "whatsapp"
) =>
  await TrackerService.logEvent(TrackerEvent.ACCOUNT_INFO_SUBMITTED, {
    "account type": social,
  });

const signUpWithSocial = async (
  social: "telegram" | "google" | "apple" | "whatsapp"
) => {
  setRefHash();
  setFromPrivate();

  await logEvent(social);
};

const signUpWithGoogle = async () => {
  await signUpWithSocial("google");

  window.location.href = googleLink.value.value;
};

const signUpWithApple = async () => {
  await signUpWithSocial("apple");

  window.location.href = appleLink.value.value;
};

const signUpWithTelegram = async () => {
  await signUpWithSocial("telegram");
  const botName = dictionaryStore.dictionary?.oauth?.bot_name ?? "";
  window.location.href = `https://t.me/${botName}?start=login`;
};

const signUpWithWhatsapp = async () => {
  setRefHash();
  window.location.href =
    "https://wa.me/" +
    (dictionaryStore.dictionary?.oauth?.whatsapp_bot || "") +
    "?text=/start";
};

const { initGeoBlock, initTwoStepVerificationExperiment } = useExperiments();

const { init: initSubscriptionPlusCardAutobuy1Experiment } =
  useSubscriptionPlusCardAutobuy1Experiment();

const signUpWithEmail = async () => {
  if (buttonDisabled.value) {
    return;
  }

  setLoading(true);

  await logEvent("email");

  const referral_hash = getHash() || "";

  const recaptcha_token = await window.grecaptcha.execute(Config.grecaptcha, {
    action: "submit",
  });

  const data = {
    email: email.value.value,
    password: password.value.value,
    country_id: 1,
    type: 0,
    referral_hash,
    master_hash: masterHash,
    recaptcha_token,
  };

  const registrationResult = await Auth.register(data);

  if (!registrationResult.status) {
    setError(registrationResult.message || "Something went wrong");
    setLoading(false);
    return;
  }

  const utm = getUtm();
  await getUser({ params: utm });

  if (user.uuid) {
    await TrackerService.setUserId(user.uuid);
  }
  await TrackerService.logEvent(TrackerEvent.ACCOUNT_CREATED, {
    "account type": "email",
  });

  // fromPrivate exists - set flag to ls
  const fromPrivate = getFromPrivate();
  let pushArg: RouteLocationRaw = {
    path: "/app",
    query: {},
  };

  if (fromPrivate) {
    setLocalStorageHideUnavailableCountriesUntilMs();
    pushArg.query = {
      // @ts-expect-error LocationQueryRaw can't be boolean
      p: !!fromPrivate,
    };
  }

  // Initialization of experiments
  await Promise.all([
    initSubscriptionPlusCardAutobuy1Experiment(),
    initTwoStepVerificationExperiment(),
  ]);

  const geoBlockValue = await initGeoBlock();
  if (geoBlockValue) {
    pushArg = { name: RouteName.UNAVAILABLE_COUNTRIES };
  }

  await redirectToApp(pushArg);

  setLoading(false);

  localStorage.removeItem(LocalStorageKey.AUTH_METHOD);
  localStorage.removeItem(LocalStorageKey.AUTH_EMAIL);
};
// watch
watch(
  () => authMethod.value,
  async (method: TAuthMethod) => {
    localStorage.setItem(LocalStorageKey.AUTH_METHOD, method);
    if (method === "telegram") {
      await signUpWithTelegram();

      setAuthMethod("");
    }
    if (method === "google") {
      await signUpWithGoogle();

      setAuthMethod("");
    }

    if (method === "apple") {
      await signUpWithApple();

      setAuthMethod("");
    }

    if (method === "whatsapp") {
      await signUpWithWhatsapp();
      setAuthMethod("");
    }
  }
);
watch(
  () => email.value.value,
  (newEmail) => {
    localStorage.setItem(LocalStorageKey.AUTH_EMAIL, newEmail);
  }
);
onMounted(async () => {
  TrackerService.logEvent(TrackerEvent.CREATE_ACCOUNT_STARTED);

  addCaptcha();
  const t = setInterval(() => {
    if (!window.grecaptcha) {
      setcapthaLoading(true);
      return;
    }
    setcapthaLoading(false);
    clearInterval(t);
  }, 100);

  googleLink
    .updateValue()
    .then(() => initGoogle(googleLink.clientId.value, signUpWithGoogleJwt));
  appleLink.updateValue();
  const token = authLogic.getToken();
  if (token) {
    try {
      await getUser();

      const fromPrivate = getFromPrivate();
      const pushArg = {
        path: "/app",
        query: {},
      };

      if (fromPrivate) {
        setLocalStorageHideUnavailableCountriesUntilMs();
        pushArg.query = {
          p: !!fromPrivate,
        };
      }

      await redirectToApp(pushArg);
    } catch (e) {
      console.warn("Registration.new->OnMounted error handled: invalid token");
    }
  }

  const authMethod = localStorage.getItem(LocalStorageKey.AUTH_METHOD);
  if (authMethod) {
    setAuthMethod(authMethod as TAuthMethod);
  }
  const authEmail = localStorage.getItem(LocalStorageKey.AUTH_EMAIL);
  if (authEmail) {
    email.value.value = authEmail;
  }
});
</script>

<template>
  <div :class="$style.root">
    <Banner v-if="!masterHash" />
    <div
      v-else
      class="w-full p-5 gap-3 flex flex-col bg-fg-purple rounded-[16px]">
      <div class="flex flex-none text-4 font-medium text-white">
        {{ $t("pst-private.just-one-step") }}
      </div>
      <div class="flex flex-none text-8 leading-8 font-extralight text-white">
        {{ $t("pst-private.to-be-part-of-the-team") }}
      </div>
    </div>

    <div class="flex flex-col gap-5">
      <!--   Title & text   -->
      <div>
        <h3 class="text-gray-900 text-left">
          {{ $t("signUp.newAccount") }}
        </h3>

        <span class="text-neutral-600 text-base">
          {{ $t("signUp.options") }}
        </span>
      </div>

      <!--   Buttons   -->
      <SocialButtons
        :disabled="!googleLink.value.value || loading || !appleLink.value.value"
        @click="setAuthMethod" />
    </div>

    <!--  Or  -->
    <div class="flex items-center gap-3">
      <div class="w-full border-dashed border-t border-t-[#838689]" />

      <span class="text-sm text-neutral-600">
        {{ t("settings.security.divider") }}
      </span>

      <div class="w-full border-dashed border-t border-t-[#838689]" />
    </div>

    <transition
      mode="out-in"
      name="fade-slide-up">
      <!--  Sign up with email  -->
      <div
        v-if="authMethod !== 'email'"
        class="flex flex-col gap-4">
        <UiButtonSocial
          :disabled="loading || capthaLoading"
          :title="t('signIn.continueWith', { v: t('social.email') })"
          data-cy="continue-with-email"
          @click="() => setAuthMethod('email')" />

        <!--   Change method   -->
        <ChangeAuthMethod method="signIn" />
      </div>

      <!--  Sign up  -->
      <form
        v-else
        class="flex flex-col gap-8"
        @submit.prevent="signUpWithEmail">
        <div class="flex flex-col gap-5">
          <InputText
            :error="email.errorMessage.value"
            :label="$t('email')"
            :value="email.value.value"
            autofocus
            data-cy="input_register_identifier"
            type="email"
            @input="email.setValue" />

          <ChangeAuthMethodWithError
            v-if="!!error"
            :text="error" />

          <InputPassword
            :error="password.errorMessage.value"
            :value="password.value.value"
            data-cy="input_register_password"
            @input="password.setValue" />

          <InputPassword
            :error="confirmation.errorMessage.value"
            :label="$t('signUp.repeatPassword')"
            :value="confirmation.value.value"
            data-cy="input_register_confirm_password"
            @input="confirmation.setValue" />

          <SquareCheckbox
            :checked="terms"
            data-cy="agreement-terms"
            @keydown.enter="signUpWithEmail"
            @change="setTerms">
            <template #label>
              <PrivacyAndTos />
            </template>
          </SquareCheckbox>

          <SquareCheckbox
            :checked="countries"
            data-cy="agreement-country"
            @keydown.enter="signUpWithEmail"
            @change="setCountries">
            <template #label>
              <BlockedCountriesAgreement />
            </template>
          </SquareCheckbox>
        </div>

        <div class="flex flex-col gap-4">
          <UiButton
            :class="$style.button"
            :disabled="buttonDisabled"
            :title="$t('signUp')"
            data-cy="register_with_email_button" />

          <!--   Change method   -->
          <ChangeAuthMethod
            data-cy="already-have-account"
            method="signIn" />
        </div>
      </form>
    </transition>

    <div class="mt-auto flex flex-col gap-5">
      <PrivacyAndTos v-if="!authMethod" />

      <SelectLanguage />
    </div>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply flex flex-col gap-8 w-full h-full;
}

.button {
  @apply font-normal;
}
</style>
