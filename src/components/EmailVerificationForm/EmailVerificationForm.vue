<script setup lang="ts">
import UITextInput from "@/components/ui/UITextInput/UITextInput.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import {
  MIN_CODE_LENGTH,
  useEmailVerificationForm,
} from "@/components/EmailVerificationForm/useEmailVerificationForm";
import { useUserStore } from "@/stores/user";
import { onMounted } from "vue";

const {
  confirmationCode,
  resendButtonText,
  codeInputError,
  isSendingCode,
  isRequestCodeAvailable,
  sendCodeHandler,
  requestCodeHandler,
} = useEmailVerificationForm();

withDefaults(defineProps<{ showSkip?: boolean }>(), {
  showSkip: true,
});

const emit = defineEmits<{
  verified: [];
  skip: [];
}>();

const onSendClick = () => {
  sendCodeHandler(() => emit("verified"));
};

onMounted(() => {
  requestCodeHandler();
});

const { user } = useUserStore();
</script>

<template>
  <div class="email-verification-form">
    <div>
      <div class="text-4.5 leading-6">
        <span class="font-normal">
          {{ $t("2fa-settings.email-verification-dialog.email-send") }} &nbsp;
        </span>
        <span class="font-medium"> {{ user.email }} </span>
      </div>
    </div>
    <!-- CODE INPUT -->
    <div class="mt-4">
      <UITextInput
        v-model="confirmationCode"
        :error="codeInputError"
        :label="
          $t('2fa-settings.email-verification-dialog.verification-input.label')
        "
        autofocus
        size="m" />
    </div>
    <!-- SEND CODE BUTTON -->
    <div class="mt-4">
      <UIButton
        :disabled="confirmationCode.length < MIN_CODE_LENGTH"
        :is-loading="isSendingCode"
        class="w-full"
        color="black"
        @click="onSendClick">
        {{ $t("Confirm") }}
      </UIButton>
    </div>
    <!-- REQUEST CODE BUTTON -->
    <div class="flex mt-2">
      <UIButton
        class="w-full"
        :color="isRequestCodeAvailable ? 'grey-solid' : 'grey-free'"
        @click="requestCodeHandler">
        {{ resendButtonText }}
      </UIButton>
    </div>
    <!-- SKIP BUTTON -->
    <div
      v-if="showSkip"
      class="flex mt-2">
      <UIButton
        class="w-full"
        color="grey-free"
        @click="$emit('skip')">
        {{ $t("2fa-settings.email-verification-dialog.skip") }}
      </UIButton>
    </div>
  </div>
</template>
