import { useUserStore } from "@/stores/user";
import { computed } from "vue";
import { UserActions } from "@/types/user/user.types";
import { useGlobalUniversalDialog } from "@/components/GlobalUniversalDialog/useGlobalUniversalDialog";
import { useEventHooks } from "@/composable/useEventHooks";
import { storeToRefs } from "pinia";
import { useSecurityWarningModal } from "@/components/SecurityWarning/useSecurityWarningModal";
import { useI18nGlobal } from "@/composable/useI18nGlobal";

type TOnVerify = () => void | Promise<void>;
type TOnDeny = () => void | Promise<void>;

/**
 * Two-step verification experiment
 *
 * @example
 * const { isActive } =
 *   useCountrySets();
 *
 * @category Composables
 */
export function useCountrySets() {
  const { t } = useI18nGlobal();
  const userStore = useUserStore();
  const { isTeamMember, userCountry, user } = storeToRefs(userStore);
  const { openDialog, closeDialog } = useGlobalUniversalDialog();
  const { showSecurityWarning } = useSecurityWarningModal();

  const isActive = computed<boolean>(() => {
    return !!user.value?.actions?.includes(UserActions.NEED_VERIFICATION);
  });

  const issueCardGardaVerification = computed<boolean>(() => {
    return !!user.value?.actions?.includes(
      UserActions.ISSUE_CARD_GARD_VERIFICATION
    );
  });

  const openSuspiciousActivityDialog = (
    onVerify?: TOnVerify,
    onDeny?: TOnDeny,
    customCases: boolean = false
  ) => {
    openDialog({
      title: t("verification.suspiciousActivity-dialog.title"),
      text: t("verification.suspiciousActivity-dialog.text", {
        c: userCountry.value,
      }),
      showBtnConfirm: true,
      btnConfirmText: isTeamMember.value
        ? t("ok")
        : t("verification.suspiciousActivity-dialog.btn-confirm-text"),
      callbackConfirm: () => onConfirm(onVerify, customCases),
      showBtnCancel: false,
      showBtnDeny: true,
      btnDenyText: t("withdrawal.request-withdrawal"),
      callbackDeny: () => onRequestWithdrawal(onDeny),
      withCloseIcon: true,
    });
  };

  const onConfirm = async (
    onVerify?: TOnVerify,
    customCases: boolean = false
  ) => {
    closeDialog();
    onVerify && (await onVerify());
    !isTeamMember.value && showSecurityWarning(customCases);
  };

  const { openWithdrawModalHook } = useEventHooks();

  const onRequestWithdrawal = async (onDeny?: TOnDeny) => {
    closeDialog();
    onDeny && (await onDeny());
    openWithdrawModalHook.trigger();
  };

  /**
   * Guards a function execution based on user's flag needVerificationAction
   *
   * If the user doesn't need verification (isActive is false), executes the callback directly.
   * Otherwise, shows a suspicious activity dialog with verification options.
   *
   * @param {Function} callback - Function to execute if it doesn't have needVerificationAction flag
   * @param {TOnVerify} [onVerify] - Optional callback to execute after user passes verification
   * @param {TOnDeny} [onDeny] - Optional callback to execute if user opts out of verification
   * @param {boolean} [customCases=false] - Whether to show a support section in verification modal
   *
   * @example
   * countrySetGuard(
   *   () => {
   *     setModalState(true, "subscriptionUpLimitModal");
   *   }
   * );
   *
   * countrySetGuard(
   *   () => { performAction() },
   *   async () => { await router.push({ name: RouteName.DASHBOARD }) },
   *   () => { showCancelMessage() },
   *   true
   * );
   */
  const countrySetGuard = (
    callback: () => void,
    onVerify?: TOnVerify,
    onDeny?: TOnDeny,
    customCases: boolean = false
  ) => {
    if (!isActive.value) {
      callback();
    } else {
      openSuspiciousActivityDialog(onVerify, onDeny, customCases);
    }
  };

  return {
    isActive,
    countrySetGuard,
    issueCardGardaVerification,
  };
}
