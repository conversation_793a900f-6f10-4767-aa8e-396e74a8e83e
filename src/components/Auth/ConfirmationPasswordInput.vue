<template>
  <UiInputPassword
    :value="value"
    data-cy="input_register_confirm_password"
    :placeholder="$t('Confirm password')"
    class="col-span-2"
    autocomplete="new-password"
    @input="onInput"
    :error="confirmationError" />
</template>

<script>
import UiInputPassword from "@/components/ui/InputPassword/InputPassword";

export default {
  emits: ["input", "change"],
  name: "ConfirmationPasswordInput",
  components: { UiInputPassword },
  props: {
    value: {
      type: String,
      default: "",
    },
    confirmation: {
      type: String,
      default: "",
    },
  },
  computed: {
    confirmationError() {
      if (this.value.length === 0) return "";

      return this.confirmation !== this.value
        ? this.$t("validation.confirmationError")
        : "";
    },
  },
  methods: {
    onInput(value) {
      this.$emit("input", value);
      this.$emit("change", value);
    },
  },
};
</script>

<style scoped></style>
