<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import {
  type TUserCountrySetResource,
  useVerificationActualGet,
} from "@/composable";
import { currencyFormatter } from "@/helpers/currencyFormatter";

const { data: verificationActual } = useVerificationActualGet();

const remainingDeposit = computed<string>(() => {
  return currencyFormatter(
    Number(verificationActual.value?.data?.remaining_deposit) || 0,
    "USD",
    false,
    2
  );
});

const remainingCards = computed(() => {
  return verificationActual.value?.data?.remaining_cards;
});
const { t } = useI18n();

const props = defineProps<{
  isCurrent: boolean;
  verification: TUserCountrySetResource;
}>();

const depositLimit = computed<string>(() => {
  if (props.verification.slug === "unlimited") {
    return t("verification.verification-item.deposit-limit.unlimited");
  }
  return t("verification.verification-item.deposit-limit.limit", {
    l: props.verification.card_deposit_limit,
  });
});

const cardsLimit = computed<string>(() => {
  if (props.verification.slug === "welcome") {
    return t("verification.verification-item.cards-limit.welcome");
  }
  return t("verification.verification-item.cards-limit.limit", {
    l: props.verification.card_limit,
  });
});
</script>

<template>
  <div class="verification-limits">
    <div class="limits">
      <div class="limit-container">
        <div class="deposit-limit single-line">
          <div class="line">
            <DynamicIcon
              class="w-5 h-5 mr-1.5"
              name="blank-note-01" />
          </div>
          <div class="line">
            {{ depositLimit }}
          </div>
        </div>
        <div
          v-if="isCurrent"
          class="current-remaining">
          <div class="remaining">
            {{ $t("verification.verification-item.remaining-label") }}
          </div>
          <div class="remaining-value">{{ remainingDeposit }}</div>
        </div>
      </div>
      <div class="limit-container">
        <div class="cards-limit single-line">
          <div class="line">
            <DynamicIcon
              class="w-5 h-5 mr-1.5"
              name="credit-card-02" />
          </div>
          <div class="line">
            {{ cardsLimit }}
          </div>
        </div>
        <div
          v-if="isCurrent"
          class="current-remaining">
          <div class="remaining">
            {{ $t("verification.verification-item.remaining-label") }}
          </div>
          <div class="remaining-value">{{ remainingCards }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.limits {
  @apply flex flex-col gap-4 font-normal text-fg-secondary text-4 leading-6;
}
.single-line {
  @apply flex flex-row;
}
.line {
  @apply flex flex-col items-center justify-center;
}
.current-remaining {
  @apply flex justify-between p-2 rounded bg-bg-level-1-5;
}
.remaining {
  @apply font-normal text-4 leading-5 text-fg-secondary;
}
.remaining-value {
  @apply text-4 leading-5 text-fg-primary;
}
.limit-container {
  @apply flex flex-col gap-2;
}
</style>
