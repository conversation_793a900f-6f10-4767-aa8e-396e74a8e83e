import type { <PERSON><PERSON>, StoryObj } from "@storybook/vue3";
import Timer from "@/components/Timer/Timer.vue";
import type { ComponentProps } from "vue-component-type-helpers";
import { SUBSCRIPTION_LIMITED_OFFER_EXPIRES_AT } from "@/constants/subscribtion_limited_offer_expires_at";

const meta: Meta<ComponentProps<typeof Timer>> = {
  title: "Timer",
  component: Timer,
  tags: ["autodocs"],
  render(args: any) {
    return {
      components: { Timer },
      setup() {
        return { args };
      },
      template: `<div
        class="flex w-full p-4"
        :class="args.dark ? 'bg-bg-level-1' : 'bg-bg-contrast'">
        <Timer
          :size="args.size"
          :expired-at="new Date(args.expiredAt)"
          :dark="args.dark"/>
      </div>`,
    };
  },
  argTypes: {
    expiredAt: { control: "date" },
    size: {
      control: "inline-radio",
      options: ["s", "m"],
    },
    dark: {
      control: { type: "boolean" },
    },
  },
  args: {
    expiredAt: SUBSCRIPTION_LIMITED_OFFER_EXPIRES_AT,
    size: "m",
    dark: false,
  },
};

export default meta;

type Story = StoryObj<typeof Timer>;

export const Default: Story = {};
