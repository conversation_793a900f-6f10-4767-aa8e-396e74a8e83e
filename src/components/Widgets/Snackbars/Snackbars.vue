<script lang="ts" setup>
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UISnackbar from "@/components/ui/UISnackbar/UISnackbar.vue";
import differenceInHours from "date-fns/differenceInHours";
import { computed, ref } from "vue";
import { useLocalStorage } from "@vueuse/core";
import { useI18n } from "vue-i18n";
import { type RouteRecordName, useRoute, useRouter } from "vue-router";
import { LocalStorageKey } from "@/constants/local_storage_key";
import { useLayoutStore } from "@/stores/layout";
import { useUserStore } from "@/stores/user";
import { useSystemMessageService } from "@/services/SystemMessageService";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useAccountGet } from "@/composable/API/useAccountGet";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import type { TSystemMessage } from "@/types/api/TSystemMessage";
import { useAutoAnimate } from "@formkit/auto-animate/vue";
import { isNil } from "@/helpers/other";
import { formatCurrency } from "@/helpers";
import { useSocialsBotConnection } from "@/composable/useSocialsBotConnection";
import { useUnpaidOrder } from "@/composable";
import { useBlockedCountrySnackbar } from "@/components/Widgets/Snackbars/useBlockedCountrySnackbar";

// base
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const userStore = useUserStore();
const layout = useLayoutStore();
const { unpaidOrder, isActual } = useUnpaidOrder();
const { checkBlockedCountrySnackbar, userCountryCode } =
  useBlockedCountrySnackbar();

const SYSTEM_MESSAGES_VARIANT: any = {
  0: "info",
  10: "warning",
  20: "error",
};
const SnackbarName = {
  SYSTEM_MESSAGE: "system-message",
  BOT_CONNECT: "bot-connect",
  DEPOSIT_REQUIRED: "deposit-required",
};

const snackbars = useLocalStorage(LocalStorageKey.SNACKBAR, [] as string[]);

const removeSnackbar = (id: string) => {
  if (!snackbars.value.includes(id)) {
    snackbars.value.push(id);
  }
};

const isShowSnackbar = (id: string) => {
  return !snackbars.value.includes(id);
};

const { isConnected, whatsappLink, telegramLink } = useSocialsBotConnection();

// system messages snackbar start
const systemMessagesRef = ref<TSystemMessage[]>([]);
const systemMessages = computed<TSystemMessage[]>(() => {
  return systemMessagesRef.value.filter((item: TSystemMessage) => {
    return !snackbars.value.includes(
      SnackbarName.SYSTEM_MESSAGE + "-" + item.id
    );
  });
});
const updateMessages = async () => {
  const response = await useSystemMessageService("systemMessages");

  if (!response.status) {
    console.error(
      "Widgets/Snackbars->updateMessages error handled: ",
      response
    );
    return;
  }

  systemMessagesRef.value = response.data;

  if (await checkBlockedCountrySnackbar()) {
    systemMessagesRef.value.push({
      id: systemMessagesRef.value.length + 1,
      title: "",
      type: 20,
      content: t("common.snackbar.blockedCountry", {
        r: t(`unavailable-country-list.${userCountryCode.value}`),
      }),
      to_show_once: false,
    });
  }
};
const onCloseSystemMessage = (id: number) => {
  removeSnackbar(SnackbarName.SYSTEM_MESSAGE + "-" + id);
  systemMessagesRef.value = systemMessagesRef.value.filter(
    (item: TSystemMessage) =>
      item.id !== id &&
      snackbars.value.includes(SnackbarName.SYSTEM_MESSAGE + "-" + id)
  );
};
const hasNewBinMessage = (content: string) => {
  return content
    .toLowerCase()
    .startsWith(
      "Mastercard has just issued a new BIN-542723 exclusively for PST.NET.".toLowerCase()
    );
};
updateMessages();
// system messages snackbar end

// deposit required snackbar start
const { subscriptionsInfo } = useSubscriptionsInfo();

const timeBeforeSubscriptionUpdate = computed(() => {
  const orderedNext = subscriptionsInfo.value?.ordered_next;
  if (!orderedNext) return null;
  const orderedNextDate = new Date(orderedNext ?? null);
  const timezoneOffset = orderedNextDate.getTimezoneOffset();
  const dateWithOffset = new Date(
    new Date().setMinutes(timezoneOffset + new Date().getMinutes())
  );

  return differenceInHours(orderedNextDate, dateWithOffset);
});

const showDepositSnackbar = computed(() => {
  if (userStore.isTeamMember) return false;

  const status = subscriptionsInfo.value?.status;
  const amount = subscriptionsInfo.value?.amount;

  let summary = userStore.summary;

  const accountBalanceIsLowerThanAmount =
    summary?.accounts_balance &&
    amount &&
    parseInt(summary.accounts_balance) < parseInt(amount);

  const subscriptionUpdateIsSoon =
    !isNil(timeBeforeSubscriptionUpdate.value) &&
    timeBeforeSubscriptionUpdate.value! >= 0 &&
    timeBeforeSubscriptionUpdate.value! < 12;

  return (
    status === 10 && accountBalanceIsLowerThanAmount && subscriptionUpdateIsSoon
  );
});

const openDepositModal = async () => {
  const { data: accounts } = await useAccountGet();
  const accountsWithAddresses = accounts.value?.data?.filter(
    (item: TUserAccountResource) => {
      return item.addresses.length > 0;
    }
  );
  const query = {
    ...route.query,
    action: "transaction",
    type: "deposit",
    to_id:
      accountsWithAddresses !== undefined ? accountsWithAddresses[0]["id"] : "",
    to_direction: "account",
  };
  await router.push({
    name: route.name as RouteRecordName,
    query,
    params: route.params,
  });
  layout.state.actions = true;
};
// deposit required snackbar end
const [snackbarsWrapper] = useAutoAnimate();

// pay notice
const payNoticeMessage = computed(() => {
  const name = unpaidOrder.value?.name;
  const amount = unpaidOrder.value?.amount;
  return name && amount
    ? t("pay.snackbar-notice-msg-full", {
        p: formatCurrency(Number(amount), "USD"),
        n: name,
      })
    : t("pay.snackbar-notice-msg");
});

const payNoticeLink = computed(() => {
  const uuid = unpaidOrder.value?.uuid;
  return uuid ? `/pay/${uuid}` : "";
});

const showPaymentNotice = computed(() =>
  Boolean(
    isShowSnackbar(LocalStorageKey.UNPAID_ORDER) &&
      unpaidOrder.value &&
      isActual
  )
);
</script>

<template>
  <div ref="snackbarsWrapper">
    <!--    System Messages-->
    <div class="w-full flex flex-col">
      <UISnackbar
        v-for="message in systemMessages"
        :key="message.id"
        :variant="SYSTEM_MESSAGES_VARIANT[message.type] || 'info'"
        closeable
        container
        @close="onCloseSystemMessage(message.id)">
        <p>{{ message.content }}</p>
        <RouterLink
          v-if="hasNewBinMessage(message.content)"
          to="/app/promo-cards">
          <UIButton
            color="white"
            size="s">
            <template #default>
              {{ $t("team.membersCards.issueCardButton") }}
            </template>
            <template #right>
              <DynamicIcon name="arrow-narrow-right" />
            </template>
          </UIButton>
        </RouterLink>
      </UISnackbar>
    </div>
    <!--    Bot Connect-->
    <UISnackbar
      v-if="!isConnected && isShowSnackbar(SnackbarName.BOT_CONNECT)"
      closeable
      container
      variant="contrast"
      @close="removeSnackbar(SnackbarName.BOT_CONNECT)">
      <p>
        {{ $t("widgets.botConnectMessage") }}
      </p>
      <template #buttons>
        <div class="bot-buttons">
          <a
            :href="whatsappLink"
            target="_blank">
            <UIButton
              class="w-full md:w-fit"
              color="white"
              size="s">
              <template #left>
                <DynamicIcon name="whatsapp_colored" />
              </template>
              {{ $t("label.whatsappBot") }}
            </UIButton>
          </a>
          <a
            :href="telegramLink"
            target="_blank">
            <UIButton
              class="w-full md:w-fit"
              color="white"
              size="s">
              <template #left>
                <DynamicIcon name="telegram-color-circle" />
              </template>
              {{ $t("label.telegramBot") }}
            </UIButton>
          </a>
        </div>
      </template>
    </UISnackbar>
    <!--    Deposit Required-->
    <UISnackbar
      v-if="
        showDepositSnackbar && isShowSnackbar(SnackbarName.DEPOSIT_REQUIRED)
      "
      closeable
      container
      variant="error"
      @close="removeSnackbar(SnackbarName.DEPOSIT_REQUIRED)">
      <div
        class="flex items-start lg:items-center lg:justify-between w-full gap-3 flex-col lg:flex-row">
        <div class="flex-grow-0 flex-shrink text-normal">
          {{
            timeBeforeSubscriptionUpdate && timeBeforeSubscriptionUpdate > 1
              ? t("snackbar.lowFundsForSubscription", [
                  timeBeforeSubscriptionUpdate,
                ])
              : t("snackbar.lowFundsForSubscriptionSoon")
          }}
        </div>
      </div>
      <template #buttons>
        <UIButton
          class="text-fg-primary flex-shrink-0 lg:self-end"
          color="white"
          size="s"
          @click="openDepositModal">
          {{ $t("snackbar.lowFunds.topUpAccount") }}
        </UIButton>
      </template>
    </UISnackbar>
    <!-- Pay Notice -->
    <UISnackbar
      v-if="showPaymentNotice"
      closeable
      container
      variant="warning"
      @close="removeSnackbar(LocalStorageKey.UNPAID_ORDER)">
      <p>{{ payNoticeMessage }}</p>
      <RouterLink :to="payNoticeLink">
        <UIButton
          color="white"
          size="s">
          <template #default> {{ $t("pay.btn-pay") }}</template>
          <template #right>
            <DynamicIcon name="arrow-narrow-right" />
          </template>
        </UIButton>
      </RouterLink>
    </UISnackbar>
  </div>
</template>

<style lang="scss" scoped>
.bot-buttons {
  @apply grid grid-cols-1 xxs:grid-cols-2;
  @apply w-full items-center gap-y-2 gap-x-3 ml-auto md:flex lg:w-fit;
}
</style>
