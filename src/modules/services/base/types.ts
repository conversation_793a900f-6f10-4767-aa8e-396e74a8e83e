import type { AxiosRequestConfig } from "axios";

export interface IServiceResponse<D, E> {
  data?: D;
  error?: E;
  status: boolean;
}

export type TCommonResponse = {
  status: boolean;
};
export type TErrorResponse = {
  message?: string;
};
export type TGetServiceResponse = TCommonResponse &
  TErrorResponse & {
    service?: any;
    extractError?: any;
    excludeFromLoading: Array<string>;
  };
export type TServiceResponse<T = any> = TCommonResponse &
  TErrorResponse & {
    data?: T;
    axiosResponse?: any;
  };

export type TPostAxiosRequestConfig = {
  [key: string]: any;
};
export type TCustomAxiosRequestConfig<TData = TPostAxiosRequestConfig> = TData &
  AxiosRequestConfig & {
    uniqueLoadingKey?: string;
    urlParams?: { [key: string]: string | number };
  };

export type TServiceEndpointConfig = {
  fetcher: (args: Array<any>) => Promise<any>;
  extractError: (result: TServiceResponse) => TServiceResponse;
};

export type TApiResponse = {
  success: boolean;
  message?: string;
  [key: string]: any;
};
