import type { TTransactionSummaryResource } from "@/types/api/TTransactionSummaryResource";
import type { MaybeRef } from "vue";
import type { UseFetchOptions } from "@vueuse/core";
import { useGetRequest } from "@/composable/useGetRequest";
import type { TResponseMeta } from "@/types/api/TResponseMeta";

export type TTransactionSummaryResourceReq = {
  start?: string;
  stop?: string;
  cards?: string;
  statuses?: string;
  merchant?: string;
  members?: string;
  is_new?: number;
};

export const useTransactionsV2SummaryGet = (
  reqConfig: MaybeRef<TTransactionSummaryResourceReq> = {},
  useFetchOptions: UseFetchOptions = {}
) => {
  return useGetRequest<
    { data: TTransactionSummaryResource | null; meta: TResponseMeta },
    MaybeRef<TTransactionSummaryResourceReq>
  >("transactions-v2/summary", reqConfig, useFetchOptions);
};
