<script generic="T" lang="ts" setup>
import type {
  TUISelectOption,
  TUISelectProps,
} from "@/components/ui/UISelect/types";
import { computed, shallowRef } from "vue";
import UISelect from "@/components/ui/UISelect/UISelect.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIFilterSelectOption from "@/components/ui/UIFilterSelect/UIFilterSelectOption.vue";

const props = withDefaults(defineProps<TUISelectProps<T>>(), {
  label: "Select value",
  cleared: false,
  fitDropdown: false,
});

const emit = defineEmits<{
  clearSelectedValue: [];
  optionsEndIntersection: [value: boolean];
}>();

defineSlots<{
  selectedOption: (props: { option: TUISelectOption<T> }) => any;
  option: (props: {
    option: TUISelectOption<T>;
    handleSelectOption: (v: TUISelectOption<T>) => void;
    selected: boolean;
  }) => any;
  group: (slotProps: { label: string; options: TUISelectOption<T>[] }) => any;
  reset: (props: any) => any;
}>();

const selectRef = shallowRef<any>(null);

const selectedValue = defineModel<(string | number)[] | string | number | null>(
  { default: null }
);

const currentOption = computed(() => {
  return props.options.find((item) => item.value == selectedValue.value);
});

const clearSelectHandler = (hide: () => void) => {
  if (props.multiple) {
    selectedValue.value = [];
  } else {
    selectedValue.value = null;
  }
  selectRef.value.hideDropdownHandle();
  hide();
  emit("clearSelectedValue");
};

const optionsEndIntersectionHandler = (v: boolean) => {
  emit("optionsEndIntersection", v);
};

const isSelectedValueEmpty = computed(() => {
  if (Array.isArray(selectedValue.value)) {
    return selectedValue.value.length === 0;
  } else {
    return !selectedValue.value;
  }
});
</script>

<template>
  <UISelect
    ref="selectRef"
    v-bind="props"
    v-model="selectedValue"
    class="ui-filter-select"
    :class="{ 'is-disabled': props.disabled }"
    :is-fullscreen="isFullscreen"
    :cleared="false"
    :fullscreen-title="label"
    label=""
    @options-end-intersection="optionsEndIntersectionHandler">
    <template #selectedValueContainer>
      <div class="flex items-center">
        <span class="text-fg-secondary mr-2 font-normal text-nowrap">{{
          props.label
        }}</span>
        <slot
          :option="currentOption!"
          name="selectedOption">
          <span class="truncate">
            {{ currentOption?.label }}
          </span>
        </slot>
      </div>
    </template>

    <template
      v-if="props.cleared && !isFullscreen && !isSelectedValueEmpty"
      #reset="{ hide }">
      <slot
        :hide="hide"
        name="reset">
        <div
          class="ui-filter-select__reset"
          @click.stop="clearSelectHandler(hide)">
          <DynamicIcon
            class="w-4"
            name="close-small" />
          <span class="ml-2">{{ $t("clear") }}</span>
        </div>
      </slot>
    </template>

    <template
      v-if="props.grouping"
      #group="{ label, options }">
      <slot
        :label="label"
        :options="options"
        name="group">
      </slot>
    </template>

    <template #option="optionContext">
      <slot
        :handle-select-option="optionContext.handleSelectOption"
        :option="optionContext.option"
        :selected="optionContext.selected"
        name="option">
        <UIFilterSelectOption
          :option="optionContext.option"
          :is-selected="optionContext.selected" />
      </slot>
    </template>
  </UISelect>
</template>

<style lang="scss" scoped>
.ui-filter-select {
  :deep(.ui-select) {
    @apply border-0 bg-bg-level-1-5 hover:bg-bg-level-2;

    .ui-select__chevron {
      @apply text-fg-secondary;
    }
  }

  &.is-disabled {
    @apply opacity-50;
  }
}

.ui-filter-select__reset {
  @apply flex items-center flex-grow transition-colors cursor-pointer rounded-[0.25rem]
  text-3.5 text-fg-primary hover:bg-bg-level-1 px-2 min-h-8 active:bg-bg-none-clicked;
}

.ui-filter-select__reset {
  @apply text-fg-secondary;
}
</style>
