<script lang="ts" setup>
import { computed, ref } from "vue";
import UITextarea from "@/components/ui/UITextarea/UITextarea.vue";
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import SupportTrigger from "@/components/SupportTrigger.vue";
import { useAutoAnimate } from "@formkit/auto-animate/vue";
import UICurrencyInput from "@/components/ui/UICurrencyInput/UICurrencyInput.vue";
import { useI18n } from "vue-i18n";
import { useBusinessMoneyRequestPost } from "@/composable/API/useBusinessMoneyRequestPost";
import { useRequestMoneySidebar } from "@/components/RequestMoneySidebar/useRequestMoneySidebar";

const { t } = useI18n();

const [wrapper] = useAutoAnimate({ duration: 200 });

const { isVisible, toggle } = useRequestMoneySidebar();

const amount = ref(1);
const comment = ref("");
const step = ref<number>(1);
const fetching = ref(false);

const createMoneyRequestStatus = ref<boolean | undefined>(undefined);

const createMoneyRequest = async () => {
  createMoneyRequestStatus.value = undefined;
  fetching.value = true;

  const response = await useBusinessMoneyRequestPost({
    description: comment.value,
    amount: String(amount.value),
  });

  fetching.value = false;
  step.value = 3;

  if (!(response.data.value?.success === true)) {
    createMoneyRequestStatus.value = false;
    return;
  }
  createMoneyRequestStatus.value = true;
};

type TFieldError = {
  touched: boolean;
  error: string | null;
};

type TErrorsObject = {
  amount: TFieldError;
  comment: TFieldError;
};

const errorsObject = ref<TErrorsObject>({
  amount: {
    touched: false,
    error: null,
  },
  comment: {
    touched: false,
    error: null,
  },
});

const validateFrom = () => {
  if (amount.value < 1 && errorsObject.value.amount.touched) {
    errorsObject.value.amount.error = t(
      "team.requestFromMember.errorLessDollar"
    );
  } else {
    errorsObject.value.amount.error = null;
  }
  if (comment.value.length < 5 && errorsObject.value.comment.touched) {
    errorsObject.value.comment.error = t(
      "team.requestFromMember.commentLength"
    );
  } else {
    errorsObject.value.comment.error = null;
  }
};

const isSendButtonDisabled = computed(() => {
  return (
    !!errorsObject.value.amount.error ||
    !!errorsObject.value.comment.error ||
    !errorsObject.value.amount.touched ||
    !errorsObject.value.comment.touched ||
    fetching.value
  );
});
const isCancelButtonDisabled = computed(() => fetching.value);

const setAmount = (value: number | null) => {
  amount.value = value || 0;
  touchField("amount");
};

const setComment = (value: string) => {
  comment.value = value;
  touchField("comment");
};

const touchField = (field: keyof TErrorsObject) => {
  errorsObject.value[field].touched = true;
  validateFrom();
};

const closeSidebar = () => {
  resetState();
  toggle();
};

const resetState = () => {
  amount.value = 1;
  comment.value = "";
  step.value = 1;
  fetching.value = false;
  createMoneyRequestStatus.value = undefined;
  errorsObject.value = {
    amount: {
      touched: false,
      error: null,
    },
    comment: {
      touched: false,
      error: null,
    },
  };
};
</script>

<template>
  <UISideModal
    :title="$t('team.requestMoney.title')"
    :is-open="isVisible"
    @close="closeSidebar">
    <template #buttons>
      <div class="mr-3">
        <SupportTrigger />
      </div>
    </template>
    <template #content="{ contentHeight }">
      <div
        ref="wrapper"
        :style="{ height: contentHeight - 35 + 'px' }">
        <div v-if="step === 1">
          <div class="flex flex-col">
            <UICurrencyInput
              :model-value="amount"
              size="m"
              :currency="'USD'"
              :label="$t('team.requestMoney.toAmount')"
              :error="errorsObject.amount.error"
              @blur="touchField('amount')"
              @update:model-value="setAmount">
              <template #rightIcon>
                <span class="text-fg-secondary">$</span>
              </template>
            </UICurrencyInput>
            <div class="grid grid-cols-3 space-x-2 mt-1">
              <UIButton
                color="grey-solid"
                size="s"
                shape="square"
                @click="() => setAmount(50)">
                50 $
              </UIButton>
              <UIButton
                color="grey-solid"
                size="s"
                shape="square"
                @click="() => setAmount(100)">
                100 $
              </UIButton>
              <UIButton
                color="grey-solid"
                size="s"
                shape="square"
                @click="() => setAmount(500)">
                500 $
              </UIButton>
            </div>

            <div class="mt-10">
              <UITextarea
                v-model="comment"
                class="w-full"
                :label="$t('team.requestMoney.commentLabel')"
                :native-max-length="255"
                :total-characters-in-counter="255"
                :error="errorsObject.comment.error"
                :placeholder="$t('team-requests.commentPlaceholder')"
                @input="setComment"
                @blur="touchField('comment')" />
            </div>
          </div>

          <div class="mt-10">
            <UIButton
              type="button"
              color="black"
              class="w-full"
              :disabled="isSendButtonDisabled"
              @click="createMoneyRequest">
              {{ $t("team.requestMoney.sendButton") }}
            </UIButton>
            <UIButton
              class="mt-2 w-full"
              type="button"
              :disabled="isCancelButtonDisabled"
              @click="closeSidebar">
              {{ $t("team.requestMoney.cancelButton") }}
            </UIButton>
          </div>
        </div>

        <div
          v-else
          class="h-full flex items-center flex-col justify-center space-y-10">
          <template v-if="createMoneyRequestStatus">
            <DynamicIcon
              name="check-circle-plain"
              class="w-14 h-14" />
            <span
              class="text-fg-primary leading-8 text-7 text-center font-medium">
              {{ $t("team.requestMoney.requestSuccess") }}
            </span>
            <UIButton
              type="button"
              color="black"
              @click="closeSidebar"
              >{{ $t("Close") }}
            </UIButton>
          </template>
          <template v-else>
            <DynamicIcon
              name="warning-circle-plain"
              class="w-14 h-14" />
            <div class="flex flex-col">
              <span
                class="text-fg-primary leading-8 text-7 text-center font-medium">
                {{ $t("team.requestMoney.requestFail") }}
              </span>
              <span
                class="text-fg-primary leading-6 text-xl text-center font-medium inline-block mt-2.5">
                {{ $t("buttons.tryAgain") }}
              </span>
            </div>
            <UIButton
              type="button"
              color="black"
              @click="step = 1"
              >{{ $t("buttons.retry") }}
            </UIButton>
          </template>
        </div>
      </div>
    </template>
  </UISideModal>
</template>
