<script lang="ts" setup>
import UiAccordion from "@/components/ui/Accordion/Accordion.vue";
import TransactionItem from "@/components/Teams/Transactions/TransactionItem.vue";
import DetailedItem from "@/components/History/TableItemDetailed.vue";

interface Props {
  template: string;
  data: any;
  isActive: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(["toggle"]);

const onToggleTransaction = () =>
  emit("toggle", props.isActive ? -1 : props.data.id);
</script>

<template>
  <UiAccordion
    :is-active="isActive"
    data-cy="transaction-item">
    <template #header>
      <TransactionItem
        :template="template"
        :data="data"
        @toggle="onToggleTransaction" />
    </template>

    <template #body>
      <DetailedItem
        :data="data"
        :is-team="true" />
    </template>
  </UiAccordion>
</template>

<style lang="sass" module></style>
