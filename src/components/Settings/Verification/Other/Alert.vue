<script setup lang="ts">
interface Props {
  title: string;
  type: "info" | "attention";
  text: string;
}
defineProps<Props>();
</script>

<template>
  <div :class="[$style.root, $style[type]]">
    <div :class="$style.title">{{ title }}</div>
    {{ text }}
  </div>
</template>

<style module lang="scss">
.root {
  @apply mt-4 p-5;
  border-radius: 16px;
  font-size: 14px;
}
.info {
  background-color: rgba(241, 242, 244, 1);
}
.attention {
  background-color: rgba(255, 71, 71, 0.2);
}
.title {
  font-size: 15px;
  font-weight: 600;
  @apply mb-2;
}
</style>
