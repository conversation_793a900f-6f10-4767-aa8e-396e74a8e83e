<script setup lang="ts">
import SelectWithSearch from "@/components/ui/Select/SelectWithSearch.deprecated.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import InputBase from "@/components/ui/Input/InputBase.vue";
import Tag from "@/components/ui/Tags/Tag.vue";

import { computed } from "vue";
import { useState } from "@/helpers/utilities";
import type { TSelectOption } from "@/components/PaymentsTable/types";
import type { TPromoCode } from "@/types/promoCode";

interface Props {
  list: Array<TPromoCode>;
  selected: Array<TSelectOption["code"]>;
}

const emit = defineEmits(["change"]);
const props = defineProps<Props>();

const [search, setSearch] = useState<string>("");

const options = computed<Array<TSelectOption>>(() =>
  props.list.map((promo: TPromoCode) => ({
    title: promo.code,
    code: promo.id.toString(),
  }))
);
const filteredOptions = computed<Array<TSelectOption>>(() =>
  options.value.filter((promo: TSelectOption) =>
    promo.title.toLowerCase().includes(search.value.toLowerCase())
  )
);
const onChangeOption = (code: TSelectOption["code"]) => {
  const list = props.selected;

  if (list.includes(code)) {
    const index = list.indexOf(code);
    if (index > -1) {
      list.splice(index, 1);
    }
  } else {
    list.push(code);
  }

  emit("change", list);
};
</script>

<template>
  <SelectWithSearch
    :values="selected"
    :options="filteredOptions"
    :with-search="true"
    :label="$t('affiliate.statistic.filters.promoLabel')"
    @select="onChangeOption"
    @close="() => setSearch('')">
    <template #search>
      <InputBase
        v-mask="'NNNNNNNNNNNNNNN'"
        :value="search"
        type="text"
        :placeholder="$t('search')"
        :with-input-wrapper="false"
        @input="(e) => setSearch(e.target.value)">
        <template #leftIcon>
          <DynamicIcon
            name="search"
            class="w-5 h-5 min-w-[20px]" />
        </template>
      </InputBase>
    </template>

    <Tag
      v-for="option in options.filter(
        (item: TSelectOption) => selected.includes(item.code)
      )"
      :key="`promo-select-${option.code}`"
      :title="option.title"
      color="default"
      with-delete
      @click.stop="() => null"
      @delete="() => onChangeOption(option.code)" />
  </SelectWithSearch>
</template>

<style scoped lang="scss"></style>
