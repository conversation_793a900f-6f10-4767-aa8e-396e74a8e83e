<script setup lang="ts">
import { ref, computed, watchEffect } from "vue";
import { useI18n } from "vue-i18n";
import type {
  TNavigationKey,
  TNavigationTab,
} from "@/components/SubscriptionsPromo/types";
import UITabs from "@/components/ui/UITabs/UITabs.vue";

const props = defineProps<{
  currentIntersectionTab: TNavigationKey;
}>();

const emit = defineEmits<{
  "tab-click": [value: TNavigationKey];
}>();

const { t } = useI18n();

const tabs = computed<TNavigationTab[]>(() => {
  return [
    {
      label: t("subscription-promo.features"),
      value: "features",
    },
    {
      label: t("subscription-promo.pricing"),
      value: "pricing",
    },
    {
      label: t("subscription-promo.cards-in-private"),
      value: "cards",
    },
    {
      label: t("subscription-promo.faq"),
      value: "faq",
    },
  ];
});

const currentTab = ref<TNavigationKey>("features");

watchEffect(() => {
  currentTab.value = props.currentIntersectionTab;
});

/**
 * Emit event for scroll to current section, using 'id'
 * @param val - value of tab, must be matched with 'id' of HTMLDivElement
 *              fot correct scrollTo
 */
const onTabClick = (val: string | number) => {
  emit("tab-click", val as TNavigationKey);
};
</script>

<template>
  <UITabs
    v-model="currentTab"
    :options="tabs"
    size="l"
    @update:model-value="onTabClick" />
</template>
