import { getDomain } from "@/helpers/url";

// TODO: we should not have file with name "other", rename

/**
 * Checks if a value is null or undefined
 *
 * @example
 * isNil(null)      // true
 * isNil(undefined) // true
 * isNil(0)         // false
 * isNil("")        // false
 * isNil(false)     // false
 *
 * @category Helpers
 *
 * @param {any} value - The value to check
 * @returns {boolean} True if the value is null or undefined
 */
export function isNil(value: any) {
  return value === null || value === undefined;
}

/**
 * Opens legal page in a new window: privacy policy
 *
 * @category Helpers
 */
export function openPrivacy() {
  return window.open(`${getDomain()}/legal/privacy`);
}

/**
 * Opens legal page in a new window: terms of service
 *
 * @category Helpers
 */
export function openTos() {
  return window.open(`${getDomain()}/legal/tos`);
}

/**
 * Opens legal page in a new window: restrictions
 *
 * @category Helpers
 */
export function openRestrictions() {
  return window.open(`${getDomain()}/legal/restrictions`);
}

/**
 * Opens legal page in a new window: affiliate program
 *
 * @category Helpers
 */
export function openAffiliate() {
  return window.open(`${getDomain()}/legal/affiliate`);
}
