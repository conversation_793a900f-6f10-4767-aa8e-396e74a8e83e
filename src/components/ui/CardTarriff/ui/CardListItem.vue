<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import type { ICardTariffE, ICardTariff } from "@/composable/Tariff";
import CardListItemTag from "./CardListItemTag.vue";
import ButtonBase from "@/components/ui/Button/ButtonBase.vue";
import ExperimentsService from "@/services/ExperimentsService";
import { openLink, openPrivateLanding } from "@/helpers/events";
import { computed } from "vue";
import { useSupportManager } from "@/composable";

interface IProps {
  fakeTariff: ICardTariffE;
  userTariff?: ICardTariff;
}

const props = defineProps<IProps>();

defineEmits<{
  getCardSlug: [slug: ICardTariffE["slug"]];
}>();
const { telegramLink, whatsAppLink } = useSupportManager();

const fixed = (val: string | undefined): string => {
  if (val === undefined) return "";
  return Number(val).toLocaleString();
};

const isPlatinumNotAllowed = computed<boolean>(() => {
  return (
    props.fakeTariff.slug === "platinum-credit" &&
    props.userTariff?.is_allowed_for_user === false
  );
});

const isLoadedFeeTransactionAmount = computed(() => {
  return (
    props.userTariff?.fee_transaction_amount !== undefined &&
    props.userTariff?.fee_transaction_amount
  );
});

const isLoadedCardPrice = computed(() => {
  return (
    props.userTariff?.card_price !== undefined && props.userTariff?.card_price
  );
});

const isLoadedFeeTopUp = computed(() => {
  return (
    props.userTariff?.fee_topup !== undefined && props.userTariff?.fee_topup
  );
});

const openSupport = (key: "telegram" | "whatsapp") => {
  if (key === "telegram") {
    openLink(`${telegramLink.value}`, true);
  }

  if (key === "whatsapp") {
    openLink(`${whatsAppLink.value}`, true);
  }
};
</script>
<template>
  <div :class="[$style.root, $style[fakeTariff.ui ?? 'light']]">
    <!-- col-1  -->
    <div class="w-full lg:w-[55%]">
      <!-- tags  -->
      <div
        :class="$style.tags"
        class="flex-wrap">
        <CardListItemTag
          v-for="(tag, index) in fakeTariff.tags"
          :key="index"
          :icon="tag.icon"
          :title="tag.title"
          :style="tag.style ?? 'default'"
          class="flex-shrink-0" />
      </div>
      <DynamicIcon
        :name="fakeTariff.slug"
        :path="'cards-first'"
        :class="$style.card_view_mobile" />

      <!-- content  -->
      <div :class="$style.content">
        <h2 :class="$style.title">{{ fakeTariff?.name ?? "" }}</h2>
        <p :class="$style.text">{{ fakeTariff?.description ?? "" }}</p>

        <template v-if="fakeTariff.triggers?.length">
          <div :class="$style.card_items">
            <!-- trigger -->
            <div
              v-for="(trigger, index) in fakeTariff.triggers"
              :key="index"
              :class="$style.card_item">
              <span>{{ trigger.value }}</span>
              <p>{{ $t(trigger.title) }}</p>
            </div>
          </div>
        </template>

        <template v-else>
          <!-- card items  -->
          <div :class="$style.card_items">
            <!-- transaction fee -->
            <div :class="$style.card_item">
              <template v-if="!isPlatinumNotAllowed">
                <div
                  v-if="!isLoadedFeeTransactionAmount"
                  class="flex flex-col flex-auto">
                  <div class="flex flex-none">
                    <div
                      class="h-[25px] w-[80px] mt-2 bg-gray-500 rounded animate-pulse w-full"></div>
                  </div>
                  <div class="flex flex-auto"></div>
                  <div class="flex flex-none mb-2">
                    <div
                      class="h-[16px] w-[120px] mt-2 bg-gray-300 rounded animate-pulse w-full"></div>
                  </div>
                </div>
                <div v-else>
                  <span>{{ fixed(userTariff?.fee_transaction_amount) }}%</span>
                  <p>{{ $t("Transaction fee") }}</p>
                </div>
              </template>
              <template v-else>
                <span>{{ $t("Special") }}</span>
                <p>{{ $t("Transaction fee") }}</p>
              </template>
            </div>
            <!-- deposit fee -->
            <div :class="$style.card_item">
              <div
                v-if="!isLoadedFeeTopUp"
                class="flex flex-col flex-auto">
                <div class="flex flex-none">
                  <div
                    class="h-[25px] w-[80px] mt-2 bg-gray-500 rounded animate-pulse w-full"></div>
                </div>
                <div class="flex flex-auto"></div>
                <div class="flex flex-none mb-2">
                  <div
                    class="h-[16px] w-[120px] mt-2 bg-gray-300 rounded animate-pulse w-full"></div>
                </div>
              </div>
              <div v-else>
                <span>{{ fixed(userTariff?.fee_topup) }}%</span>
                <p>{{ $t("createFirstCard.depositeFee") }}</p>
              </div>
            </div>
            <!-- monthly payment -->
            <template v-if="!isPlatinumNotAllowed">
              <div
                v-if="!isLoadedCardPrice"
                class="flex flex-col flex-auto">
                <div class="flex flex-none">
                  <div
                    class="h-[25px] w-[80px] mt-2 bg-gray-500 rounded animate-pulse w-full"></div>
                </div>
                <div class="flex flex-auto"></div>
                <div class="flex flex-none mb-2">
                  <div
                    class="h-[16px] w-[120px] mt-2 bg-gray-300 rounded animate-pulse w-full"></div>
                </div>
              </div>
              <div
                v-else
                :class="$style.card_item">
                <span
                  v-if="ExperimentsService.getStatus('freeCard') === '1'"
                  class="mr-2"
                  >0 $</span
                >
                <span
                  :class="
                    ExperimentsService.getStatus('freeCard') === '1'
                      ? 'line-through opacity-40'
                      : null
                  ">
                  {{ fixed(userTariff?.card_price) }}
                  $</span
                >
                <p>{{ $t("createFirstCard.monthlyPayment") }}</p>
              </div>
            </template>
            <template v-else>
              <div :class="$style.card_item">
                <span>{{ $t("Special") }}</span>
                <p>{{ $t("createFirstCard.monthlyPayment") }}</p>
              </div>
            </template>
          </div>
        </template>
      </div>

      <!-- card btns  -->
      <template v-if="!isPlatinumNotAllowed">
        <div :class="$style.btns">
          <template v-if="fakeTariff.buttons?.length">
            <a
              v-if="fakeTariff.buttons[0]?.url"
              :href="fakeTariff.buttons[0]?.url"
              target="_blank">
              <ButtonBase
                :title="$t(fakeTariff.buttons[0]?.title!)"
                class="w-full btn-dark"
                :type="fakeTariff.ui === 'dark' ? 'light' : 'primary'" />
            </a>
            <ButtonBase
              v-else
              :title="$t(fakeTariff.buttons[0]?.title!)"
              class="w-full btn-dark"
              :type="fakeTariff.ui === 'dark' ? 'light' : 'primary'"
              @click="openPrivateLanding()" />

            <a
              v-if="fakeTariff.buttons[1]?.url"
              :href="fakeTariff.buttons[1]?.url"
              target="_blank">
              <ButtonBase
                :title="$t(fakeTariff.buttons[1]?.title!)"
                class="w-full btn-dark"
                :type="fakeTariff?.ui === 'dark' ? 'primary-light' : 'light'" />
            </a>
            <ButtonBase
              v-else
              :title="$t(fakeTariff.buttons[1]?.title!)"
              class="w-full btn-dark"
              :type="fakeTariff?.ui === 'dark' ? 'primary-light' : 'light'"
              @click="openPrivateLanding()" />
          </template>
          <template v-else>
            <ButtonBase
              :title="$t('createCard.btn.getCard')"
              class="w-full btn-dark"
              :type="'primary'"
              @click="$emit('getCardSlug', fakeTariff.slug)" />
            <a
              v-if="fakeTariff.url"
              :href="fakeTariff.url"
              target="_blank">
              <ButtonBase
                class="w-full"
                :title="$t('createCard.btn.learnMore')"
                :type="'light'" />
            </a>
          </template>
        </div>
      </template>
      <template v-else>
        <!-- Only for platinum-card -->
        <div :class="$style.btns">
          <ButtonBase
            class="w-full"
            :title="'Telegram'"
            :type="'white'"
            :class="$style['btn-white']"
            @click="openSupport('telegram')" />
          <ButtonBase
            class="w-full"
            :title="'Whatsapp'"
            :type="'white'"
            :class="$style['btn-white']"
            @click="openSupport('whatsapp')" />
        </div>
      </template>
    </div>
    <!-- col-2  -->
    <div>
      <DynamicIcon
        :name="fakeTariff.slug"
        :path="'cards-first'"
        :class="$style.card_view" />
    </div>
  </div>
</template>
<style module lang="scss">
.root {
  @apply bg-neutral-100 flex flex-col lg:flex-row items-start justify-between
  gap-8 p-6 lg:p-9 rounded-[36px] w-full font-granate;
}

.tags {
  @apply flex gap-[10px] items-center overflow-x-auto;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    width: 0;
  }
}

.content {
  @apply my-9;
  .title {
    @apply text-2xl font-medium;
  }

  .text {
    @apply text-neutral-600 mt-2 text-lg;
  }

  .card_item {
    span {
      @apply text-[25px] font-medium;
    }

    p {
      @apply text-neutral-600 text-sm md:text-lg;
    }
  }

  .card_items {
    @apply grid grid-cols-3 gap-5 w-full lg:w-[80%] mt-7;
  }
}

.btns {
  @apply grid w-full grid-cols-1 lg:w-2/3 md:grid-cols-2 gap-4;
}

.card_view {
  @apply w-0 h-0 opacity-0 lg:w-auto lg:h-auto lg:opacity-100;
}

.card_view_mobile {
  @apply mx-auto w-full h-auto md:w-2/3 mt-9 lg:w-0 lg:h-0 lg:opacity-0 lg:m-0;
}

.root.dark {
  @apply bg-slate-900;
  .title {
    @apply text-white;
  }

  .text {
    @apply text-neutral-300;
  }

  .card_item {
    span {
      @apply text-white;
    }
  }
}

.promo_btn {
  background-color: rgba($color: #fff, $alpha: 0.2) !important;
}

.btn-white {
  border: 1px solid black;
}
</style>
