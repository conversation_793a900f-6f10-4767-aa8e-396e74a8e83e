<script setup lang="ts">
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

defineEmits(["confirm"]);
</script>

<template>
  <div
    class="flex flex-col justify-center items-center m-auto h-screen mt-[-10rem]">
    <div class="flex flex-col items-center max-w-[29.5rem] space-y-10">
      <div class="flex flex-none bg-bg-green-solid rounded-full p-4 w-14 h-14">
        <DynamicIcon
          name="check"
          class="text-white w-6 h-6" />
      </div>
      <div class="flex text-7 leading-8 font-semibold text-center">
        {{ $t("create-card.success-message") }}
      </div>
      <div class="flex flex-none">
        <UIButton
          size="m"
          color="black"
          @click="$emit('confirm')">
          {{ $t("Continue") }}
        </UIButton>
      </div>
    </div>
  </div>
</template>
