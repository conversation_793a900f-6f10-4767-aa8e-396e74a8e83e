<template>
  <VTooltip
    :triggers="[]"
    :shown="
      passwordErrors.filter((rule) => rule.isFailed).length > 0 && isInFocus
    "
    :auto-hide="false">
    <template #popper>
      <p
        class="text-lg text-center mb-3 font-semibold"
        data-cy="tooltip_title">
        {{ $t("register.password_must_contain") }}
      </p>

      <p
        v-for="error in passwordErrors"
        :key="error.text"
        class="text-lg flex flex-row items-center gap-3 mt-2"
        :class="error.isFailed ? 'text-error-light' : 'text-success-light'">
        <Validation :success="!error.isFailed" /> {{ error.text }}
      </p>
    </template>
  </VTooltip>

  <UiInputPassword
    :value="value"
    data-cy="input_register_password"
    v-bind="$attrs"
    :error="passwordError"
    autocomplete="new-password"
    :placeholder="$t('password')"
    @input="(v) => $emit('input', v)"
    @blur="onBlurHandler"
    @focus="onFocusHandler" />
</template>

<script>
import Validation from "~/components/ui/Validation/Validation";
import UiInputPassword from "@/components/ui/InputPassword/InputPassword";

export default {
  emits: ["input"],
  name: "PasswordInput",
  components: {
    UiInputPassword,
    Validation,
  },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  methods: {
    onBlurHandler() {
      this.isInFocus = false;
    },
    onFocusHandler() {
      this.isInFocus = true;
    },
  },
  data() {
    return {
      isInFocus: false,
      password: [
        {
          validate: (value) => /[A-Z]/g.test(value),
          message: this.$t("register.capital_letter"),
        },
        {
          validate: (value) => /[0-9]/g.test(value),
          message: this.$t("register.number"),
        },
        {
          validate: (value) => value.length >= 8,
          message: this.$t("register.symbols"),
        },
        {
          validate: (value) => /[a-z]/g.test(value),
          message: this.$t("register.small_letter"),
        },
      ],
    };
  },
  computed: {
    passwordErrors() {
      const isInvalid = this.password.filter(
        (rule) => !rule.validate(this.value)
      );

      if (this.value.length > 0 && isInvalid) {
        return this.password.map((rule) => ({
          text: rule.message,
          isFailed: !rule.validate(this.value),
        }));
      }
      return [];
    },
    passwordError() {
      const failedItem = this.passwordErrors.find((item) => item.isFailed);

      if (failedItem) {
        return failedItem?.text + this.$t("validation.isRequired");
      } else {
        return "";
      }
    },
  },
};
</script>

<style scoped></style>
