<script lang="ts" setup>
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import EmailVerified from "@/components/EmailVerified/EmailVerified.vue";
import router from "@/router";
import { RouteName } from "@/constants/route_name";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

const onClose = () => {
  router.push({ name: RouteName.DASHBOARD });
};
</script>

<template>
  <div class="email-verification-complete-view">
    <UIFullScreenModal
      :is-open="true"
      @close="onClose">
      <template #title>
        <DynamicIcon
          name="pst-logo"
          class="w-[186px] h-auto ml-[calc(50%-93px)]"
          path="./" />
      </template>
      <template #content>
        <div class="email-verification-complete">
          <EmailVerified />
        </div>
      </template>
    </UIFullScreenModal>
  </div>
</template>
