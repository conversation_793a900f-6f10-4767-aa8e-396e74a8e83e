import type { TCurrencySymbol } from "@/types/TCurrencySymbol";
import { IsoCodeNames } from "@/constants/iso_code_names";

type TAccountCurrency = {
  title?: string;
  altTitle?: string;
  isoCode: IsoCodeNames;
  icon?: string;
  symbol?: TCurrencySymbol;
};

/**
 * Gets currency information for a given account currency ID.
 *
 * @TODO refactor this, improve typing of argument, should not return empty object
 *
 * @example
 * getAccountCurrencyByCurrencyId(2)  // USD currency info
 * getAccountCurrencyByCurrencyId(14) // BTC currency info
 * getAccountCurrencyByCurrencyId(999) // {}
 *
 *
 * @see {@link getCurrencySymbolByIso} - Get currency symbol by ISO code
 * @see {@link getCurrencyIcon} - Get currency icon by ISO code
 * @see {@link getCurrencyById} - Get currency information by ID
 *
 * @category Helpers
 *
 * @param {number} currencyId - Currency id
 * @returns {TAccountCurrency} Currency object containing title, altTitle, isoCode, symbol and icon
 */
export function getAccountCurrencyByCurrencyId(currencyId: number) {
  const ACCOUNTS: Record<number, TAccountCurrency> = {
    2: {
      title: "USD",
      altTitle: "US Dollar",
      isoCode: IsoCodeNames.USD,
      symbol: "$",
      icon: "square-usd",
    },
    3: {
      title: "EUR",
      altTitle: "Euro",
      isoCode: IsoCodeNames.EUR,
      symbol: "€",
      icon: "square-eur",
    },
    14: {
      title: "BTC",
      altTitle: "Bitcoin",
      isoCode: IsoCodeNames.BTC,
      symbol: "₿",
      icon: "square-btc",
    },
    15: {
      title: "Tether",
      altTitle: "USDT",
      isoCode: IsoCodeNames.USDT,
      symbol: "₮",
      icon: "square-usdt",
    },
  };

  return ACCOUNTS[currencyId] ?? {};
}
