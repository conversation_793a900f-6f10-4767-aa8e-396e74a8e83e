<script>
import QrCode from "qrcode";
import Information from "@/components/ui/InfoMessage/Information.vue";
import InputCopy from "@/components/ui/Input/InputCopy.vue";

import { useDictionary } from "@/stores/dictionary";
import Env from "@/config/env";
import { useUserStore } from "@/stores/user";
import { storeToRefs } from "pinia";
import { useSupportLink } from "@/composable/useSupportLink";

export default {
  name: "DepositFromCryptocurrency",
  components: { Information, InputCopy },
  props: {
    activeAccount: {
      type: Object,
      default: () => ({}),
    },
  },
  setup() {
    const {
      dictionary: { paymentSystems },
    } = useDictionary();
    const userStore = useUserStore();
    const { getUserFees } = useUserStore();
    const { telegramSupportPstUuid } = useSupportLink();
    const { user, userFees } = storeToRefs(userStore);
    const supportLink = `${Env.supportTeamTg}?start=${telegramSupportPstUuid.value}`;
    getUserFees();
    return { paymentSystems, user, userFees, supportLink };
  },
  data() {
    return {
      imageSrc: null,
    };
  },
  computed: {
    address() {
      if (
        this.activeAccount === undefined ||
        this.activeAccount?.addresses === undefined
      ) {
        return "";
      }

      return this.activeAccount?.addresses[0]?.address || "";
    },
    serviceFee() {
      if (this.userFees.deposit) {
        return Number(this.userFees.deposit * 100).toFixed(
          this.userFees.deposit > 0 ? 2 : 0
        );
      }
      if (this.user.is_without_exchange_fee) {
        return Number(0).toFixed(2);
      }
      return Number(
        this.paymentSystems.find(
          (item) => item.name === this.activeAccount._currency?.iso_code
        )?.fee_percent || 1
      ).toFixed(2);
    },
  },
  watch: {
    async address() {
      this.imageSrc = await QrCode.toDataURL(this.address);
    },
  },
  async mounted() {
    if (this.address === "") {
      return;
    }
    this.imageSrc = await QrCode.toDataURL(this.address);
  },
  methods: {
    openTelegram() {
      window.open(Env.supportTeamTg);
    },
    openSkype() {
      window.open(Env.supportTeamSkype);
    },
  },
};
</script>

<template>
  <div class="flex flex-col gap-6">
    <div
      v-if="activeAccount?._currency?.iso_code === 'USDT'"
      class="flex flex-col gap-1 mt-2">
      <p class="text-md font-medium text-warning-dark">USDT TRC-20</p>
      <p class="text-base mt-1 text-greyscale-600">
        {{
          $t(
            "To top up your account, send any amount to the address " +
              "below. USDT funds are credited within a few minutes."
          )
        }}
      </p>
    </div>

    <div
      :style="`background-image: url('${imageSrc}')`"
      class="bg-no-repeat bg-contain bg-center w-full min-h-[300px]" />

    <InputCopy
      :label="
        activeAccount?._currency?.iso_code === 'USDT'
          ? $t('USDT TRC-20 address')
          : $t('Wallet address')
      "
      input-class="font-extrabold text-sm h-[58px]"
      :copy="true"
      label-class="pb-1 text-sm"
      label-type="outside"
      disabled
      class="w-full"
      :value="address"
      data-cy="wallet-address" />

    <div class="flex justify-between">
      <span class="text-sm">{{ $t("deposit_service_fee") }}</span>
      <span
        class="text-sm"
        data-cy="deposit-fee"
        >{{ serviceFee }}%</span
      >
    </div>

    <Information v-if="activeAccount?._currency?.iso_code === 'USDT'">
      {{ $t("If you require ERC-20, please contact") }}
      <a
        :href="supportLink"
        target="_blank"
        class="text-blue-900 cursor-pointer">
        {{ $t("our support team") }}
      </a>
    </Information>
  </div>
</template>

<style lang="scss" module>
.WidgetAccountListItem__icon {
  @apply bg-white  w-max rounded-sm p-[7px] mb-1 w-[24px] h-[24px];
  & svg {
    @apply w-[18px] h-[18px];
  }
}
</style>
