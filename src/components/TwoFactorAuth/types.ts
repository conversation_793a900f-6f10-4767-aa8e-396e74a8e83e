import type { TUserTwoFactorAuthProvider } from "@/types/api/TUserSecuritySettings";
import type { TUser2FAProvider } from "@/types/api/TUser2FAProvider";
import type { TUserInfo } from "@/types/user/user.types";

export type TwoFactorAuthStep = "list" | "connect-tg" | "connect-wa";
export type TwoFactorAuthProvider = TUser2FAProvider;
export type ProviderItemMode = "default" | "edit";
export type TwoFactorAuthState = {
  enabledProvider: string | null;
  user_telegram: TUserInfo["telegram"] | undefined;
  user_telegram_2fa: TUserInfo["telegram_2fa"] | undefined;
  user_whatsapp: TUserInfo["whatsapp"] | undefined;
  user_whatsapp_2fa: TUserInfo["whatsapp_2fa"] | undefined;
  telegramProvider: TUserTwoFactorAuthProvider | undefined;
  whatsappProvider: TUserTwoFactorAuthProvider | undefined;
  registration_method: "telegram" | "site" | string | undefined;
};

export type TwoFactorCodeDialogState =
  | "none"
  | "show-confirm-dialog"
  | "show-code-dialog"
  | "show-disconnected-dialog"
  | "show-unable-to-disconnect-dialog";

export type DisconnectDialogsState = {
  provider: TwoFactorAuthProvider | null;
  dialogState: TwoFactorCodeDialogState;
  code: string;
  incorrectCodeError: boolean;
};

export const providerToStepMap: Record<
  TwoFactorAuthProvider,
  TwoFactorAuthStep
> = {
  telegram: "connect-tg",
  whatsapp: "connect-wa",
};

export type ProviderName = "Telegram" | "WhatsApp";

export const providerToNameMap: Record<TwoFactorAuthProvider, ProviderName> = {
  telegram: "Telegram",
  whatsapp: "WhatsApp",
};

export type ProviderIconName =
  | "telegram-color-circle"
  | "whatsapp-color-circle";

export const providerToIconNameMap: Record<
  TwoFactorAuthProvider,
  ProviderIconName
> = {
  telegram: "telegram-color-circle",
  whatsapp: "whatsapp-color-circle",
};
