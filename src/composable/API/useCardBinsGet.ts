import { useGetRequest } from "@/composable/useGetRequest";
import type { UseFetchOptions } from "@vueuse/core";
import type { TCardTariffSlug } from "@/composable";
import type { TBinResource } from "@/types/api/TBinResource";

export const useCardBinsGet = (
  type: TCardTariffSlug,
  options: UseFetchOptions = {}
) => {
  return useGetRequest<{ data: TBinResource[] }>(
    "card/bins",
    { type },
    options
  );
};
