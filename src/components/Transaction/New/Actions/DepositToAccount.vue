<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import ModalTransactions from "../ui/Modal/ModalTransactions.vue";
import AccountInfo from "../ui/AccountInfo.vue";
import ActionVariantToogle from "../ui/ActionVariantToogle.vue";
import DepositToFiat from "./Deposit/DepositToFiat.vue";
import DepositCryptocurrency from "./Deposit/DepositCryptocurrency.vue";

import { useUserAccounts } from "@/composable/useUserAccounts";
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { getAccountUSDBalance } from "../helpers/withdraw";
import ConfirmModal from "@/components/Transaction/DepositWarningFee/ConfirmModal.vue";
import DepositWarning from "@/components/ui/Warning/DepositWarning.vue";

import { useUserStore } from "@/stores/user";

import router from "@/router";
import { attentions } from "@/config/attentions";

interface Props {
  activeAccount?: any;
}

const depositToFiatRef = ref<any>(null);
const props = withDefaults(defineProps<Props>(), {
  activeAccount: undefined,
});

const userStore = useUserStore();
const userIsWarn = computed<boolean>(() => !!userStore?.user?.show_warn);

const emit = defineEmits<{ (event: "close"): void }>();

const close = () => {
  emit("close");
};

const { accounts } = useUserAccounts({ selectUsdAccount: true });
const { t } = useI18n();
const confirmModalData = ref<{
  show: boolean;
  confirmed: boolean;
  lastType: string;
  amount: string;
}>({
  show: false,
  confirmed: false,
  lastType: "",
  amount: "",
});

const actions = computed<any>(() => {
  return [
    {
      id: 1,
      title: t("Deposit USDT"),
      icon: "usdt-circle",
      description: t("Transfer to one of your PST accounts"),
    },
    {
      id: 2,
      title: t("Deposit Bitcoin"),
      icon: "btc-circle",
      description: t("Transfer to one of your PST accounts"),
    },
  ];
});

const activeType = ref<any>(null);
// if (props.activeAccount._currency.iso_code === "BTC") {
//   activeType.value = 2;
// }
// if (props.activeAccount._currency.iso_code === "USDT") {
//   activeType.value = 1;
// }

const showDepositToFiat = ref<boolean>(true);

const activeCrypto = computed(() => {
  return accounts.value.find(
    (v: any) =>
      v._currency.iso_code === (activeType.value === 1 ? "USDT" : "BTC")
  );
});

const showDepositWarnForCrypto = computed(() => {
  if (activeType.value === 2) {
    return attentions.btc;
  }
  return attentions.usdt_trc20;
});

const USDbalance = ref("0");
getAccountUSDBalance(props.activeAccount).then((result) => {
  USDbalance.value = result;
});

const onCancel = () => {
  confirmModalData.value.show = false;
  activeType.value = null;
  showDepositToFiat.value = true;

  depositToFiatRef.value.depositForm.step = 1;
  depositToFiatRef.value.depositForm.isConfirm = false;
};
const selectedGateway = ref<string>("");
const onConfirm = async (v: string, amount?: string) => {
  confirmModalData.value.lastType = v;

  if (amount) {
    confirmModalData.value.amount = amount;

    const fakeAccount = {
      balance: amount,
      _currency: props.activeAccount?._currency,
    };

    const usdAmount = await getAccountUSDBalance(fakeAccount);

    if (Number(usdAmount) < 50) {
      if (confirmModalData.value.show) {
        return;
      } else if (!confirmModalData.value.confirmed) {
        confirmModalData.value.show = true;
      }

      if (!confirmModalData.value.confirmed) {
        return;
      }
    }
  }

  showDepositToFiat.value = false;
  selectedGateway.value = v;

  if (["mastercard", "visa", "union-pay"].includes(selectedGateway.value)) {
    await router.push("/app/buy_crypto");
  }
};
const onConfirmModal = (): void => {
  confirmModalData.value.confirmed = true;
  confirmModalData.value.show = false;
  onConfirm(confirmModalData.value.lastType);
};

const amountWithSymbol = computed<string>(() => {
  const confirmModalDataAmount: string = confirmModalData.value.amount ?? "0";
  const currencySymbol: string = props.activeAccount?._currency?.symbol ?? "";
  return `${confirmModalDataAmount}${currencySymbol}`;
});

watch(
  () => selectedGateway.value,
  (newVal) => {
    if (newVal === "btc") {
      activeType.value = 2;
    }
    if (newVal === "usdt-trc20") {
      activeType.value = 1;
    }
  }
);
</script>

<template>
  <ModalTransactions
    closable
    @close="close">
    <template #title>{{ $t("account-deposit-from") }}</template>

    <ConfirmModal
      v-if="confirmModalData.show"
      :amount="amountWithSymbol"
      :is-warn="userIsWarn"
      @confirm="onConfirmModal"
      @cancel="onCancel" />

    <template #from>
      <AccountInfo
        :account="props.activeAccount"
        :usd-balance="USDbalance" />
    </template>

    <template v-if="activeType === null">
      <DepositToFiat
        v-if="showDepositToFiat"
        ref="depositToFiatRef"
        :active-account="props.activeAccount"
        @confirm="onConfirm" />

      <template v-if="!showDepositToFiat && !activeType">
        <div class="bg-[#D4EDDB] p-5 rounded-2xl flex flex-col gap-5 mb-5">
          <span
            class="flex items-start gap-3 text-lg font-extrabold leading-snug">
            <DynamicIcon
              name="development"
              class="w-6 h-6" />
            {{ $t("deposit.development.title") }}
          </span>
          <span class="text-base font-medium tracking-tight">
            {{ $t("deposit.development.description") }}👇
          </span>
        </div>
        <div class="flex flex-col gap-5 my-10">
          <h4>{{ $t("deposit.methods") }}:</h4>
          <div class="flex flex-col">
            <h6>{{ $t("deposit.gateway.crypto_coin") }}</h6>
            <ul class="grid grid-cols-2 gap-3">
              <li
                class="deposit-method"
                @click="activeType = 2">
                <div>
                  <DynamicIcon
                    path="gateway"
                    name="btc" />
                </div>
                <div class="text-base font-extrabold text-center leading-snug">
                  Bitcoin
                </div>
              </li>
              <li
                class="deposit-method"
                @click="activeType = 1">
                <div>
                  <DynamicIcon
                    path="gateway"
                    name="usdt-trc20" />
                </div>
                <div class="text-base font-extrabold text-center leading-snug">
                  Tether • Tron
                </div>
              </li>
              <li
                class="deposit-method"
                @click="onConfirm('visa')">
                <div>
                  <DynamicIcon
                    path="gateway"
                    name="visa" />
                </div>
                <div class="text-base font-extrabold text-center leading-snug">
                  Visa
                </div>
              </li>
              <li
                class="deposit-method"
                @click="onConfirm('mastercard')">
                <div>
                  <DynamicIcon
                    path="gateway"
                    name="mastercard" />
                </div>
                <div class="text-base font-extrabold text-center leading-snug">
                  Mastercard
                </div>
              </li>
            </ul>
          </div>
        </div>
      </template>
    </template>

    <template v-if="activeType">
      <ActionVariantToogle
        :actions="actions"
        :active-variant="activeType"
        @change="(v) => (activeType = v)" />

      <template v-if="userIsWarn">
        <!-- If the user is in the waiting area, then by the number of views. -->
        <DepositWarning
          class="mt-6 mb-5"
          :text="
            $t('transaction.depositWarning.attention_text_warn', {
              f: '500',
              s: '100%',
              t: '$1000',
              fo: '0%',
            })
          " />
        <DepositWarning
          v-if="activeType === 2 && showDepositWarnForCrypto"
          :text="
            $t('transaction.depositWarning.attention_text_btc', {
              f: '$1000',
              s: '$10',
              t: '$1000',
              fo: '0%',
            })
          " />
      </template>
      <template v-else>
        <DepositWarning
          v-if="showDepositWarnForCrypto"
          class="mt-6"
          :text="
            activeType === 2
              ? $t('transaction.depositWarning.attention_text_btc', {
                  f: '$1000',
                  s: '$10',
                  t: '$1000',
                  fo: '0%',
                })
              : undefined
          " />
      </template>

      <DepositCryptocurrency
        :active-account="activeCrypto"
        class="mt-5" />
    </template>
  </ModalTransactions>
</template>

<style lang="scss" scoped>
.deposit-method {
  @apply py-3 px-2 rounded-base border flex flex-col gap-2 items-center justify-center;
  @apply cursor-pointer transition-all border-greyscale-200 hover:border-greyscale-500;
}
</style>
