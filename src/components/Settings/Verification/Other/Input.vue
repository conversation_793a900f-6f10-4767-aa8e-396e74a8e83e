<script lang="ts" setup>
import InputBase from "@/components/ui/Input/InputBase.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  icon?: string;
  error?: string;
}

defineProps<Props>();
</script>

<template>
  <InputBase
    :label-class="`${$style.label}`"
    :input-class="`${$style.root}`"
    :error="error">
    <template
      v-if="icon"
      #leftIcon>
      <DynamicIcon
        :name="icon"
        :class="$style.icon" />
    </template>
  </InputBase>
</template>

<style lang="scss" module>
.root {
  @apply h-[56px] focus:ring-neutral-900 focus:border-neutral-900;

  &.error {
    @apply border-error-base;
  }
}

.label {
  @apply font-normal text-sm mb-1 text-neutral-500;

  &.error {
    @apply text-error-base;
  }
}

.icon {
  @apply w-6 h-6 min-w-[24px] text-neutral-400;
}
</style>
