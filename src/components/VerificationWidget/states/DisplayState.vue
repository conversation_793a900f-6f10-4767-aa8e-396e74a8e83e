<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

withDefaults(
  defineProps<{
    text?: string | null;
    subtext?: string | null;
    icon?: string;
    iconColor?: string;
    color?: string;
  }>(),
  {
    icon: "verify_not_outlined",
    iconColor: "text-fg-level-1",
    color: "text-fg-green",
    text: null,
    subtext: null,
  }
);
</script>

<template>
  <div
    class="display-state"
    :class="color">
    <DynamicIcon
      :class="iconColor"
      :name="icon"
      class="w-4 h-4" />
    <div class="texts">
      <div class="text-3.5 leading-4 font-normal">
        {{ text }}
      </div>
      <div class="text-3 leading-3.5 font-normal text-fg-secondary">
        {{ subtext }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.display-state {
  @apply flex items-center rounded px-2.5 py-2 w-full gap-1.5;
}
</style>
