<template>
  <div class="VueSelect">
    <label
      v-if="label"
      :for="id"
      class="text-greyscale-600 block"
      :class="labelClass">
      {{ label }}
    </label>
    <v-select
      :options="options"
      :placeholder="placeholder"
      :searchable="searchable"
      class="VueSelect__input"
      :input-id="id"
      :value="value"
      :disabled="disabled"
      :reduce="reduce"
      @input="selectOption">
    </v-select>

    <transition name="error-input">
      <span
        v-if="error"
        :class="{
          VueSelect__error: true,
          VueSelect__error__label: label,
        }">
        {{ error }}
      </span>
    </transition>
  </div>
</template>

<script>
export default {
  name: "UiVueSelect",
  props: {
    error: {
      type: String,
      default: "",
    },
    value: {
      type: [String, Number],
      default: "",
    },
    options: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "",
    },
    searchable: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: "",
    },
    labelType: {
      type: String,
      default: "outside",
    },
    labelClass: {
      type: String,
      default: "font-extrabold pb-2",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    reduce: {
      type: Function,
      default: () => ({}),
    },
  },
  data() {
    return {
      id: null,
    };
  },
  created() {
    this.id = (+new Date() * Math.random()).toString(30).substring(0, 5);
  },
  methods: {
    selectOption(val) {
      this.$emit("input", val);
    },
  },
};
</script>

<style lang="scss">
.VueSelect {
  @apply relative;

  &__input {
    @apply border-none outline-none;
    &:focus {
      @apply border-none outline-none;
    }

    & input::placeholder {
      @apply text-greyscale-500;
    }

    &.vs--open {
      & .vs__dropdown-toggle {
        @apply border-secondary-base pl-4 w-full rounded-base border transition outline-none shadow-none py-4;
      }
    }

    & .vs__dropdown-toggle {
      @apply pl-4 w-full rounded-base border border-greyscale-300 transition outline-none shadow-none py-4 pr-10;
    }

    & .vs__selected-options {
      @apply py-0;
    }

    .vs__search,
    .vs__search:focus {
      @apply m-0 p-0 border-none;
    }

    & .vs__selected {
      @apply m-0 p-0 border-none;
    }

    & .vs__dropdown-menu {
      @apply absolute left-0 top-full bg-white w-full overflow-y-auto rounded-[12px] mt-1 border-none p-0;
      box-shadow: 4px 12px 32px rgba(93, 106, 131, 0.1);
    }

    & .vs__dropdown-option--highlight {
      @apply bg-transparent text-secondary-base;
    }

    & .vs__dropdown-option {
      @apply p-4 text-base font-medium text-greyscale-600 transition-all;

      &:hover {
        @apply text-secondary-base;
      }
    }

    & .vs__actions {
      @apply px-4 py-0 h-full absolute top-0 bottom-0 right-0;
      & svg {
        @apply w-4 h-4;
      }
    }

    & .vs__no-options {
      @apply py-4 text-base text-greyscale-600;
    }
  }

  &__error {
    @apply absolute top-0 left-0 ml-3 -mt-3 p-1 text-sm text-error-light bg-white rounded-full;

    &__label {
      @apply top-8;
    }
  }
}
</style>
