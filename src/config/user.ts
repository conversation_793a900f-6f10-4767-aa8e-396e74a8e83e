export const userTeamOwnerType = 2;
export const userTeamMemberType = 3;

export const cardPermission = "card";
export const accountPermission = "account";
const fullPermission = [cardPermission, accountPermission];

// build interface helpers config
export const availableTransactionActions = {
  user: {
    withdraw: {
      children: {
        toPstCard: { available: fullPermission },
        toAnotherUser: { available: fullPermission },
        fromThePlatform: { available: fullPermission },
      },
      available: fullPermission,
    },
    deposit: {
      available: fullPermission,
    },
    exchange: {
      available: fullPermission,
    },
  },
  master: {
    withdraw: {
      children: {
        toPstCard: { available: fullPermission },
        toAnotherUser: { available: fullPermission },
        fromThePlatform: { available: fullPermission },
      },
      available: fullPermission,
    },
    deposit: {
      available: fullPermission,
    },
    exchange: {
      available: fullPermission,
    },
  },
  member: {
    withdraw: {
      children: {
        toPstCard: { available: fullPermission },
        toAnotherUser: { available: [] },
        fromThePlatform: { available: [] },
      },
      available: [cardPermission],
    },
    deposit: {
      available: [cardPermission],
    },
    exchange: {
      available: fullPermission,
    },
  },
};

export const messageCodes = [
  "ru",
  "en",
  "es",
  "fr",
  "de",
  "it",
  "pt",
  "zh",
  "bg",
  "th",
  "tr",
  "ja",
  "hi",
  "ua",
];
