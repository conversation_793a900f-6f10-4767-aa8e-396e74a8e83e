import { defineService } from "@modules/services/base";
import type { TServicePayloads, TServiceResponses } from "./types";
import { ServiceEndpoint } from "./types";

export * from "./types";

const useVerificationService = defineService<
  TServiceResponses,
  TServicePayloads
>("verificationServiceConfig");

const info = () =>
  useVerificationService<
    TServiceResponses[ServiceEndpoint.GET_KEY],
    TServicePayloads[ServiceEndpoint.GET_KEY]
  >(ServiceEndpoint.GET_KEY);

const selfieUrl = () =>
  useVerificationService<
    TServiceResponses[ServiceEndpoint.SELFIE_URL_KEY],
    TServicePayloads[ServiceEndpoint.SELFIE_URL_KEY]
  >(ServiceEndpoint.SELFIE_URL_KEY);

const verification = (arg: TServicePayloads[ServiceEndpoint.POST_KEY]) =>
  useVerificationService<
    TServiceResponses[ServiceEndpoint.POST_KEY],
    TServicePayloads[ServiceEndpoint.POST_KEY]
  >(ServiceEndpoint.POST_KEY, arg);

const actual = () =>
  useVerificationService<
    TServiceResponses[ServiceEndpoint.ACTUAL],
    TServicePayloads[ServiceEndpoint.ACTUAL]
  >(ServiceEndpoint.ACTUAL);

export const VerificationService = {
  info,
  verification,
  actual,
  selfieUrl,
};
