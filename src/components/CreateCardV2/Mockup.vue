<template>
  <UIFullScreenModal :is-open="true">
    <template #content>
      <h1>{{ snapshot?.value }}</h1>
      <pre>{{ snapshot?.context }}</pre>
      <UIButton
        v-if="snapshot.can({ type: 'back' })"
        class="mb-10"
        @click="send({ type: 'back' })">
        {{ "< back" }}
      </UIButton>

      <div
        v-if="snapshot?.value === 'cardTypeSelect'"
        class="flex space-x-4">
        <UIButton @click="send({ type: 'selectCardType', value: 'Ultima' })">
          Ultima
        </UIButton>
        <UIButton @click="send({ type: 'selectCardType', value: 'ForAdv' })">
          Advertisement
        </UIButton>
      </div>

      <div
        v-if="snapshot?.value === 'cardTariffSelect'"
        class="flex space-x-4">
        <UIButton @click="send({ type: 'selectTariff', value: 'Ultima' })">
          Ultima
        </UIButton>
        <UIButton
          @click="send({ type: 'selectTariff', value: 'Advertisement' })">
          Advertisement
        </UIButton>
        <UIButton @click="send({ type: 'selectTariff', value: 'Facebook' })">
          Facebook
        </UIButton>
        <UIButton @click="send({ type: 'selectTariff', value: 'Google' })">
          Google
        </UIButton>
      </div>
      <div
        v-if="snapshot?.value === 'binSelect'"
        class="flex space-x-4"></div>
    </template>
  </UIFullScreenModal>
</template>

<script setup lang="ts">
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { assign, enqueueActions, setup } from "xstate";
import { useMachine } from "@xstate/vue";
import { computed, ref } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

const initialValue = computed(() => {
  if (route.query.card_type === "ultima") {
    return "ultimaCardSetup";
  }
  if (route.query.card_type === "ForAdv") {
    return "cardTariffSelect";
  }
  return "cardTariffSelect";
});

type CardType = "Ultima" | "ForAdv";
type CardTariff = "Ultima" | "Advertisement" | "Facebook" | "Google";

type MachineTypes = {
  events:
    | { type: "selectCardType"; value: CardType }
    | {
        type: "selectTariff";
        value: CardTariff;
      }
    | {
        type: "selectBin";
        value: string;
      }
    | { type: "submitSetup" }
    | { type: "back" }
    | { type: "resetTariff" };
};
const machine = setup({
  types: {} as MachineTypes,
  actions: {
    resetTariff: enqueueActions(({ enqueue }) => {
      enqueue.assign({
        cardTariff: null,
      });
    }),
    resetCardType: enqueueActions(({ enqueue }) => {
      enqueue.assign({
        cardType: null,
      });
    }),
  },
}).createMachine({
  id: "create-card",
  context: {
    cardType: null,
    cardTariff: null,
    selectedBin: null,
    steps: 5,
    currentStep: 1,
  },
  initial: initialValue.value,
  states: {
    cardTypeSelect: {
      on: {
        selectCardType: {
          target: "cardTariffSelect",
          actions: assign({
            cardType: ({ event }) => {
              return event.value;
            },
          }),
        },
      },
    },
    cardForAdvTariffSelect: {
      on: {
        selectTariff: {
          target: "binSelect",
          actions: assign({
            cardTariff: ({ event }) => event.value,
          }),
        },
        back: {
          target: "cardTypeSelect",
          actions: [{ type: "resetCardType" }],
        },
      },
    },
    cardTariffSelect: {
      on: {
        selectTariff: {
          target: "binSelect",
          actions: assign({
            cardTariff: ({ event }) => event.value,
          }),
        },
        back: {
          target: "cardTypeSelect",
          actions: [{ type: "resetCardType" }],
        },
      },
    },
    binSelect: {
      on: {
        selectBin: {
          target: "cardSetup",
        },
        back: {
          target: "cardTariffSelect",
          actions: [{ type: "resetTariff" }],
        },
      },
    },
    defaultCardSetup: {
      on: {
        submitSetup: {
          target: "approveCardSetup",
        },
      },
    },
    ultimaCardSetup: {
      on: {
        submitSetup: {
          target: "approveCardSetup",
        },
      },
    },
    ultimaWithSubscriptionCardSetup: {
      on: {
        submitSetup: {
          target: "approveCardSetup",
        },
      },
    },
    approveCardSetup: {
      on: {},
    },
  },
});

const { send, snapshot: snsht, actorRef } = useMachine(machine);
const snapshot = ref(snsht);

actorRef.subscribe((s) => (snapshot.value = s));

console.log(snapshot.value.can({ type: "back" }));
</script>
