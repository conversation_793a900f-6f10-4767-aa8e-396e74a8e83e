import { useSupportLink } from "@/composable/useSupportLink";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";

import { useManagerGet, usePrivateManagerGet } from "@/composable";
import type {
  TManagerResponse,
  TSupportManagerConfig,
} from "@/composable/useSupportManager/types";
import {
  ERemoteStorageKeys,
  RemoteStorageService,
} from "@modules/services/remoteStorage";
import { openLink } from "@/helpers/events";

/**
 * Create support manager links
 *
 * @param {TSupportManagerConfig} config
 * @category Composables
 */

//manager order is important
const MANAGERS = ["David_PST_Private", "Mike_PST_Private"];
const PRIVATE_TELEGRAM = "PST_Private";

export function useSupportManager(
  config: TSupportManagerConfig = {
    privateManager: false,
    useTelegramPrivateLink: false,
  }
) {
  const { t } = useI18n();
  let response: TManagerResponse;
  const isFetchingPrivateManager = ref(false);

  if (config.privateManager) {
    response = usePrivateManagerGet();
  } else {
    response = useManagerGet();
  }

  const { data, isFinished, error } = response;

  const supportLink = useSupportLink();

  const whatsAppLink = computed(() => {
    return `https://wa.me/${data.value?.data?.whatsapp}?text=${
      config.message
        ? config.message
        : t("support.welcomeMessage", {
            a: supportLink.whatsappPstUuid.value,
          })
    }`;
  });

  const getPrivateManagerTelegram = async () => {
    const randomManagerIndex = Math.floor(Math.random() * MANAGERS.length);
    const remoteManagerIndex = await RemoteStorageService.get(
      ERemoteStorageKeys.PRIVATE_MANAGER
    );
    const isManagerSet = Number.isInteger(remoteManagerIndex);

    if (!isManagerSet) {
      await RemoteStorageService.set(
        ERemoteStorageKeys.PRIVATE_MANAGER,
        randomManagerIndex
      );

      return MANAGERS[randomManagerIndex];
    } else {
      return MANAGERS[remoteManagerIndex];
    }
  };

  const redirectToPrivateManager = async () => {
    isFetchingPrivateManager.value = true;
    const managerTelegram = await getPrivateManagerTelegram();
    isFetchingPrivateManager.value = false;

    openLink(
      `https://t.me/${managerTelegram}?start=${
        supportLink.telegramPrivatePstUuid.value
      }${config.telegramCustomLinkEnd ?? ""}`,
      true
    );
  };

  const telegramLink = computed(() => {
    const telegramContact = config.useTelegramPrivateLink
      ? PRIVATE_TELEGRAM
      : data.value?.data?.telegram?.replace("@", "") || PRIVATE_TELEGRAM;

    return `https://t.me/${telegramContact}?start=${
      config.privateManager
        ? `${supportLink.telegramPrivatePstUuid.value}`
        : `${supportLink.telegramSupportPstUuid.value}`
    }${config.telegramCustomLinkEnd ?? ""}`;
  });

  const supportData = computed(() => {
    return data.value?.data;
  });

  const isSuccessLoadData = computed(() => {
    return isFinished.value && !error.value;
  });

  return {
    whatsAppLink,
    telegramLink,
    isSuccessLoadData,
    supportData,
    redirectToPrivateManager,
    isFetchingPrivateManager,
  };
}
