<script setup lang="ts">
interface Props {
  address?: string;
  currency?: string;
}

const props = defineProps<Props>();
</script>

<template>
  <div :class="$style.root">
    <span :class="$style.root__title">{{ props.currency }} wallet address</span>
    <span :class="$style.root__address">{{ props.address }}</span>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply flex flex-col gap-1 text-base;

  &__title {
    @apply text-greyscale-600 font-semibold;
  }

  &__address {
    @apply font-medium;
  }
}
</style>
