import { usePstFetch } from "@/composable/usePstFetch";
import type { UseFetchOptions } from "@vueuse/core";

type ErrorData = {
  cooldown_seconds: number;
};

export type EmailChangeSendOldPostError = {
  data: ErrorData;
  full_code: string;
};

export type EmailChangeSendOldPostResponse = {
  success: boolean;
  message?: string;
  error?: EmailChangeSendOldPostError;
};

export const useEmailChangeSendOldPost = (options: UseFetchOptions = {}) => {
  return usePstFetch("email/change/send-old", options)
    .post()
    .json<EmailChangeSendOldPostResponse>();
};
