<script setup lang="ts">
import UiTransition from "@/components/ui/UITransition.vue";
import { useField } from "vee-validate";

interface Props {
  type?: "text" | "email" | "password" | "number";
  placeholder?: string;
  inputClass?: string;
  labelClass?: string;
  labelText?: string;
  labelBottomText?: string;
  errorMsg?: string;
  modelValue?: string;
  required?: boolean;
  name: string;
  inputMin?: number;
  inputNewDesign?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  type: "text",
  required: false,
  inputNewDesign: false,
  inputMin: 0,
});
defineEmits(["update:modelValue", "blur"]);

const { value, resetField } = useField(props.name, undefined, {
  syncVModel: true,
});
if (props.inputNewDesign && props.inputMin) {
  resetField({
    value: props.inputMin,
  });
}
</script>
<template>
  <template v-if="!inputNewDesign">
    <div :class="[errorMsg ? $style.error : '']">
      <label
        v-if="labelText"
        :class="$style.label"
        >{{ labelText }}</label
      >
      <div :class="$style.input_wrap">
        <input
          v-model="value"
          :type="type"
          :placeholder="placeholder ?? ''"
          :class="[$style.input, inputClass ?? '']"
          :required="required" />
        <div :class="$style.input__right_icon">
          <slot name="rigth-icon"></slot>
        </div>
      </div>
      <UiTransition :name="'fade'">
        <small
          v-show="errorMsg"
          :class="$style.error_msg"
          >{{ errorMsg ?? "" }}</small
        >
      </UiTransition>
    </div>
  </template>

  <template v-else>
    <div :class="[errorMsg ? $style.error : '']">
      <label
        v-if="labelText"
        :class="$style['new-label']"
        >{{ labelText }}</label
      >
      <div :class="$style.input_wrap">
        <input
          v-model="value"
          :type="type"
          :min="inputMin"
          :placeholder="placeholder ?? ''"
          :class="[$style['new-input'], inputClass ?? '']"
          :required="required" />
        <div :class="$style['new-input__right-icon']">
          <slot name="rigth-icon"></slot>
        </div>
      </div>
      <div>
        <span
          v-if="labelBottomText"
          :class="$style['new-input__sub-label']"
          class="mt-[4px]"
          >{{ labelBottomText }}</span
        >
      </div>
      <UiTransition :name="'fade'">
        <small
          v-show="errorMsg"
          :class="$style.error_msg"
          >{{ errorMsg ?? "" }}</small
        >
      </UiTransition>
    </div>
  </template>
</template>
<style module lang="scss">
.input {
  @apply border border-neutral-150 rounded pl-3 pr-10 h-11 focus:ring-1 focus:ring-neutral-900;
}

.new-input {
  @apply text-[color:var(--neutral-n-900,#15191d)] text-4 not-italic font-medium leading-6;
  @apply border-[1px] border-neutral-150 rounded pl-3 pr-10 h-11 focus:ring-1 focus:ring-neutral-900;
}

.input_wrap {
  @apply relative;
}

.input__right_icon {
  @apply absolute pr-3 right-0 top-0 h-full flex items-center justify-center;
}

.new-input__right-icon {
  @apply text-[color:var(--neutral-n-400,#9fa1a3)] text-right text-[15px] not-italic font-semi-medium leading-6;
  @apply absolute pr-3 right-0 top-0 h-full flex items-center justify-center;
  font-family: "ALS Granate VF";
}
.label {
  @apply font-medium text-sm text-neutral-500;
}
.new-label {
  @apply text-[color:var(--neutral-n-500,#838689)] text-xs not-italic font-semi-medium leading-4;
  font-family: "ALS Granate VF";
}
.new-input__sub-label {
  @apply text-[color:var(--neutral-n-500,#838689)] text-xs not-italic font-semibold leading-4;
  font-family: "ALS Granate VF";
}

.error_msg {
  @apply inline-block text-sm font-medium mt-2;
}

.error {
  .input {
    @apply ring-2 ring-red-500;
  }

  .label,
  .error_msg {
    @apply text-red-500;
  }
}
</style>
