<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import type { TCardResource } from "@/types/api/TCardResource";
import type { TXUR } from "@/types/api/TXUR";
import { computed } from "vue";
import { getPaymentSystemByCardNumber } from "@/helpers/getPaymentSystemByCardNumber";
import { prepareAccountBalance } from "@/helpers";

type Tariff = {
  id: number;
  slug: string;
  name: string;
};

type Props = {
  card: TCardResource;
  rates: TXUR;
  tariffs: Tariff[];
  tariffName: string;
};

const props = defineProps<Props>();

const cardGateway = computed(() => {
  return getPaymentSystemByCardNumber(props.card.mask);
});

const cardTags = computed(() => {
  return props.card.tags.map((item) => {
    if (item.is_system && item.name === "3D Secure") {
      return "3D-S";
    }
  });
});
</script>

<template>
  <div class="flex items-center justify-between h-15 gap-2">
    <div class="overflow-hidden w-full">
      <p class="font-medium text-ellipsis overflow-hidden whitespace-nowrap">
        {{ props.card.description ?? tariffName }}
      </p>
      <div class="flex items-center gap-1 text-3.5">
        <div
          class="rounded flex items-center justify-center w-8 h-5 bg-bg-level-1">
          <DynamicIcon
            :name="cardGateway"
            path="gateway"
            class="w-5 h-auto" />
        </div>

        <div>{{ props.card.mask.slice(-4) }}</div>

        <div>{{ tariffName }}</div>

        <div
          v-for="tag in cardTags"
          :key="tag">
          {{ tag }}
        </div>
      </div>
    </div>

    <div class="flex-shrink-0">
      {{ prepareAccountBalance(props.card.account.balance, "USD") }} $
    </div>
  </div>
</template>
