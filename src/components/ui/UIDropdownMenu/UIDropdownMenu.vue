<template>
  <UIPopperDropdown
    :placement="props.placement"
    @show="$emit('show')"
    @hide="$emit('hide')">
    <slot name="trigger">
      <button>.</button>
    </slot>

    <template #popper="{ hide }">
      <slot
        name="popper"
        :hide="hide">
        <div class="ui-dropdown-menu-list gap-0.5 p-1.5">
          <div
            class="ui-dropdown-menu-list__item items-center truncate"
            v-for="item of items"
            :key="item.title"
            :class="{
              'color-default':
                item?.color === 'default' || item?.color === undefined,
              'color-red': item?.color === 'red',
            }"
            @click="
              () => {
                hide();
                item.callback();
              }
            ">
            <DynamicIcon
              v-if="item?.icon"
              :name="item?.icon"
              class="w-6 h-6 mr-2 inline-block" />
            {{ item.title }}
          </div>
        </div>
      </slot>
    </template>
  </UIPopperDropdown>
</template>

<script setup lang="ts">
import type { TDropdownMenuItem } from "@/components/ui/UIDropdownMenu/TDropdownMenuItem";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIPopperDropdown from "@/components/ui/UIPopperDropdown/UIPopperDropdown.vue";

const props = withDefaults(
  defineProps<{
    items: TDropdownMenuItem[];
    placement?:
      | "auto"
      | "auto-start"
      | "auto-end"
      | "top"
      | "top-start"
      | "top-end"
      | "right"
      | "right-start"
      | "right-end"
      | "bottom"
      | "bottom-start"
      | "bottom-end"
      | "left"
      | "left-start"
      | "left-end";
  }>(),
  {
    placement: "auto",
  }
);
</script>

<style scoped lang="scss">
.ui-dropdown-menu-list {
  @apply min-w-[12.5rem];
}

.ui-dropdown-menu-list__item {
  @apply py-1 px-2 h-8  text-3.5 leading-6 font-normal
  rounded transition-all cursor-pointer;
}

.ui-dropdown-menu-list__item.color-default {
  @apply text-fg-primary hover:bg-bg-none-hover active:bg-bg-none-clicked;
}

.ui-dropdown-menu-list__item.color-red {
  @apply text-fg-red hover:bg-bg-red-light-hover active:bg-bg-red-light-clicked;
}
</style>
