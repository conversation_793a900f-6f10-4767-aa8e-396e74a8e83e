import { formatCurrency } from "@/helpers";
import { computed, type MaybeRef, type ComputedRef, unref } from "vue";

/**
 * Formats a currency value based on the provided currency code.
 *
 * @example
 * const formattedAmount = useCurrencyFormatter(amount, "USD");
 *
 * @param {number} value - The value to be formatted.
 * @param {string} currency - The currency code to be used for formatting.
 * @returns {ComputedRef<string>} - The computed formatted currency value.
 *
 * @category Composables
 */
export function useCurrencyFormatter(
  value: MaybeRef<number>,
  // TODO: currency should be a correct currency type
  currency: MaybeRef<string>
): ComputedRef<string> {
  return computed(() => {
    let mappedCurrency = unref(currency);

    mappedCurrency = mappedCurrency === "USDT" ? "USD" : mappedCurrency;

    return formatCurrency(unref(value), mappedCurrency);
  });
}
