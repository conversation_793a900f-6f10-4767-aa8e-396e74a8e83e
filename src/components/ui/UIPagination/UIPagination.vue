<template>
  <div>
    <Skeleton
      :options="{
        width: 248,
        height: 24,
      }"
      :pending="loading">
      <template #loader>
        <rect
          height="24"
          rx="8"
          width="24"
          x="4" />
        <rect
          height="24"
          rx="8"
          width="24"
          x="40" />
        <rect
          height="24"
          rx="8"
          width="24"
          x="76" />
        <rect
          height="24"
          rx="8"
          width="24"
          x="112" />
        <rect
          height="24"
          rx="8"
          width="24"
          x="148" />
        <rect
          height="24"
          rx="8"
          width="24"
          x="184" />
        <rect
          height="24"
          rx="8"
          width="24"
          x="220" />
      </template>

      <div class="flex items-center gap-2">
        <div
          :class="pagination.nav"
          @click="!isPrevDisabled && onPrev()">
          <ChevronRightIcon
            :class="isPrevDisabled && 'opacity-50 cursor-default'"
            class="w-4 cursor-pointer rotate-180" />
        </div>
        <div
          v-for="(number, index) in numbers"
          :key="number?.value + index">
          <div
            v-if="!number.dots"
            :class="[
              pagination.item,
              number.value === page &&
                'bg-bg-contrast hover:!bg-bg-contrast !text-white',
            ]"
            class="font-medium"
            @click.stop="
              () => !number.dots && !loading && onSetPage(number.value)
            ">
            {{ number.value }}
          </div>
          <div
            v-else
            class="select-none w-8 h-8 leading-8 text-center text-fg-secondary rounded">
            ...
          </div>
        </div>

        <div
          :class="pagination.nav"
          @click="!isNextDisabled && onNext()">
          <ChevronRightIcon
            :class="isNextDisabled && 'opacity-50 cursor-default'"
            class="w-4 cursor-pointer" />
        </div>
      </div>
    </Skeleton>
  </div>
</template>

<script lang="ts" setup>
import Skeleton from "@/components/ui/Skeleton/Skeleton.vue";
import ChevronRightIcon from "@/assets/svg/icon/chevron-right.svg";

import { computed } from "vue";

type TNumber = {
  value: number;
  dots?: boolean;
};

interface Props {
  page: number; //current page
  count: number; //count pages total
  range?: number;
  bordered?: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), { range: 3 });
const emit = defineEmits(["change"]);

const emitPage = (page: number) => {
  window.scrollTo({ top: 0 });
  emit("change", page);
};
const onPrev = () => emitPage(Math.max(props.page - 1, 1));
const onNext = () => emitPage(Math.min(props.page + 1, props.count));
const onSetPage = (value: number) => emitPage(value);
const createNumber = (value: number): TNumber => ({
  value,
  dots: value === -1,
});

const isPrevDisabled = computed<boolean>(() => props.page === 1);
const isNextDisabled = computed<boolean>(() => props.page === props.count);
const numbers = computed<Array<TNumber>>(() => {
  const range = props.page > 2 ? 1 : 2;
  const count = props.count;
  const page = props.page;

  if (count < range * 2 + 2) {
    return Array.from({ length: count }, (v, i: number) => i + 1).map(
      createNumber
    );
  }

  const pages = [];

  pages.push(1);

  if (page > range + 2) {
    pages.push(-1);
  }

  const start = Math.max(2, page - range);
  const end = Math.min(count - 1, page + range);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  if (page < count - range - 1) {
    pages.push(-1);
  }

  pages.push(count);

  return pages.map(createNumber);
});
</script>

<style lang="scss" module="pagination">
.item {
  @apply flex items-center justify-center w-8 h-8 rounded text-fg-secondary cursor-pointer
  text-3.5 transition-colors leading-none hover:bg-bg-level-2;
}

.nav {
  @apply flex items-center justify-center w-8 h-8 rounded text-fg-primary cursor-pointer text-normal bg-bg-level-2;
}
</style>
