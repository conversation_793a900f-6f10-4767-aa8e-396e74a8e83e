<script setup lang="ts">
import type { TCardResource } from "@/types/api/TCardResource";
import UIButton from "../ui/UIButton/UIButton.vue";
import { useCardIdPatch } from "@/composable/API/useCardIdPatch";
import UITextInput from "../ui/UITextInput/UITextInput.vue";
import { onClickOutside } from "@vueuse/core";
import { computed, ref } from "vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useUserTariff } from "@/composable/useUserTariff";

const props = defineProps<{ card: TCardResource }>();
const emit = defineEmits<{
  renameCard: [];
}>();
const { getUserTariffNameByTariffId } = useUserTariff();

const userTariffNameByTariffId = computed(() =>
  getUserTariffNameByTariffId(props.card.tariff_id)
);

const renameInput = ref({
  description: props.card?.description ?? userTariffNameByTariffId.value,
});

const { error, isFetching, execute } = useCardIdPatch(
  props.card.id,
  renameInput.value,
  { immediate: false }
);

const isShowRenameInput = ref(false);
const isShowDoneRenameBtn = ref(false);

const inputHandle = () => {
  isShowDoneRenameBtn.value = true;
};

const keyUpEnterHandle = async () => {
  await execute();

  if (error.value) {
    return;
  }

  emit("renameCard");
};

const target = ref(null);

onClickOutside(target, () => {
  isShowRenameInput.value = false;
  isShowDoneRenameBtn.value = false;

  renameInput.value.description =
    props.card?.description ??
    getUserTariffNameByTariffId(props.card.tariff_id);
});
</script>

<template>
  <div
    ref="target"
    class="flex items-center justify-between w-full">
    <UITextInput
      v-if="isShowRenameInput"
      v-model="renameInput.description"
      :focus="isShowRenameInput"
      @click.stop
      @input="inputHandle"
      @key-up-enter="keyUpEnterHandle" />

    <span
      v-else
      class="text-ellipsis overflow-hidden w-[80%] whitespace-nowrap"
      >{{ props.card?.description ?? userTariffNameByTariffId }}</span
    >

    <UIButton
      v-if="!isShowDoneRenameBtn"
      ref="renameBtn"
      size="xs"
      icon-only
      color="grey-solid"
      class="edit-card-btn"
      :disabled="isFetching"
      @click.stop="isShowRenameInput = !isShowRenameInput">
      <DynamicIcon
        name="edit"
        class="w-full h-auto" />
    </UIButton>

    <UIButton
      v-if="isShowDoneRenameBtn"
      ref="doneRename"
      size="xs"
      icon-only
      color="green-solid"
      class="edit-card-btn"
      :disabled="isFetching"
      @click.stop="keyUpEnterHandle">
      <DynamicIcon
        name="check"
        class="w-full h-auto" />
    </UIButton>
  </div>
</template>
