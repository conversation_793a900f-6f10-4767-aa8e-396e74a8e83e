import type {
  IUserBusinessApiService,
  IUserBusinessRequestConfig,
} from "./model";
import { useAxios } from "@/helpers";
import { USER_BUSINESS_API } from "./api";
import type { AxiosError } from "axios";

export class UserBusinessApiService implements IUserBusinessApiService {
  async updateMember(
    memberId: number,
    config: IUserBusinessRequestConfig["updateMember"]
  ): ReturnType<IUserBusinessApiService["updateMember"]> {
    const res = await useAxios().post(
      USER_BUSINESS_API.MEMBER_UPDATE + `/${memberId}`,
      config
    );
    try {
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
  async deleteMember(
    config: IUserBusinessRequestConfig["deleteMember"]
  ): ReturnType<IUserBusinessApiService["deleteMember"]> {
    const res = await useAxios().delete(
      USER_BUSINESS_API.MEMBER_DELETE + `/${config}`
    );
    try {
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
  async postDeleteInviteMember(
    config: IUserBusinessRequestConfig["deleteInviteMember"]
  ): ReturnType<IUserBusinessApiService["postDeleteInviteMember"]> {
    const res = await useAxios().post(
      USER_BUSINESS_API.MEMBER_INVITE_DELETE + `/${config}`
    );
    try {
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
  async postInviteMember(
    config: IUserBusinessRequestConfig["inviteMember"]
  ): ReturnType<IUserBusinessApiService["postInviteMember"]> {
    const res = await useAxios().post(USER_BUSINESS_API.MEMBER_INVITE, config);
    try {
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
  async dashBoard(
    config: IUserBusinessRequestConfig["dashboard"] = {}
  ): ReturnType<IUserBusinessApiService["dashBoard"]> {
    try {
      const res = await useAxios().get(USER_BUSINESS_API.DASHBOARD, {
        params: config,
      });

      return {
        status: true,
        data: res.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }

  async getMembers(
    config: IUserBusinessRequestConfig["members"] = {}
  ): ReturnType<IUserBusinessApiService["getMembers"]> {
    try {
      const res = await useAxios().get(USER_BUSINESS_API.MEMBERS_LIST, {
        params: config,
      });
      return {
        status: true,
        data: res.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
  async getMembersApproved(
    config: IUserBusinessRequestConfig["membersApproveList"] = {}
  ): ReturnType<IUserBusinessApiService["getMembersApproved"]> {
    try {
      const res = await useAxios().get(USER_BUSINESS_API.MEMBERS_APPROVE_LIST, {
        params: config,
      });
      return {
        status: true,
        data: res.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
}
