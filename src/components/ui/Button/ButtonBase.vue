<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  title?: string;
  titleClass?: string;
  type?: "primary" | "light" | "primary-light" | "white" | "critical";
  isLoading?: boolean;
  size?: "xs" | "small";
}
const props = withDefaults(defineProps<Props>(), {
  type: "primary",
});
</script>
<template>
  <button
    :disabled="props.isLoading"
    :class="[
      $style.btn,
      props.size ? $style[props.size] : null,
      $style[props.type],
    ]">
    <slot name="icon"></slot>
    <DynamicIcon
      v-if="props.isLoading"
      name="loader"
      class="w-6 h-auto" />
    <span
      v-if="title"
      :class="[titleClass, props.isLoading ? 'opacity-0 w-0 h-0' : null]"
      >{{ title }}</span
    >
  </button>
</template>

<style module lang="scss">
.btn {
  @apply flex items-center rounded-small py-3 px-3 gap-2 font-hauss transition-colors justify-center;
  &:disabled {
    @apply bg-bg-level-2 text-fg-secondary;
  }
  &:disabled:hover {
    @apply bg-bg-level-2 text-fg-secondary;
  }
}
.primary {
  @apply bg-bg-contrast text-white;
  svg {
    fill: #fff;
    path {
      fill: #fff;
    }
  }
  &:hover {
    @apply bg-bg-contrast-hover;
  }
}
.light {
  @apply bg-neutral-150 text-black;
  svg {
    @apply fill-black;
    path {
      @apply fill-black;
    }
  }
  &:hover {
    @apply bg-neutral-200;
  }
}
.white {
  @apply bg-white text-black;
  svg {
    @apply fill-black;
    path {
      @apply fill-black;
    }
  }
  &:hover {
    @apply bg-neutral-100;
  }
}

.critical {
  @apply bg-bg-red-solid text-white;
  &:hover {
    @apply bg-bg-red-solid-hover;
  }
}

.primary-light {
  @apply bg-white text-white bg-opacity-20;
  svg {
    fill: #fff;
    path {
      fill: #fff;
    }
  }
  &:hover {
    @apply bg-neutral-700;
  }
}

.small {
  @apply text-normal py-1;
}
.xs {
  @apply text-normal min-h-[auto] py-0.5;
}
</style>
