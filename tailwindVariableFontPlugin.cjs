// https://gist.github.com/brennanangel/2f5b511d87bf6161c3c80dc55372856a
/* eslint-env node */
const plugin = require("tailwindcss/plugin");
const fontVariationSettings = plugin(function ({ addUtilities }) {
  addUtilities({
    ".font-thin": {
      fontWeight: 100,
      fontVariationSettings: '"wght" 100',
    },
  });

  addUtilities({
    ".font-extralight": {
      fontWeight: 200,
      fontVariationSettings: '"wght" 200',
    },
  });

  addUtilities({
    ".font-light": {
      fontWeight: 200,
      fontVariationSettings: '"wght" 200',
    },
  });

  addUtilities({
    ".font-normal": {
      fontWeight: 400,
      fontVariationSettings: '"wght" 400',
    },
  });

  addUtilities({
    ".font-regular": {
      fontWeight: 400,
      fontVariationSettings: '"wght" 400',
    },
  });

  addUtilities({
    ".font-semi-medium": {
      fontWeight: 475,
      fontVariationSettings: '"wght" 475',
    },
  });

  addUtilities({
    ".font-medium": {
      fontWeight: 550,
      fontVariationSettings: '"wght" 550',
    },
  });

  addUtilities({
    ".font-semibold": {
      fontWeight: 600,
      fontVariationSettings: '"wght" 600',
    },
  });

  addUtilities({
    ".font-bold": {
      fontWeight: 720,
      fontVariationSettings: '"wght" 720',
    },
  });

  addUtilities({
    ".font-extrabold": {
      fontWeight: 800,
      fontVariationSettings: '"wght" 800',
    },
  });

  addUtilities({
    ".font-black": {
      fontWeight: 850,
      fontVariationSettings: '"wght" 850',
    },
  });
});

module.exports = fontVariationSettings;
