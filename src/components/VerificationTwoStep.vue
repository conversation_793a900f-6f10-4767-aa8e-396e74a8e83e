<script lang="ts" setup>
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import { useVerificationPost } from "@/composable/API/useVerificationPost";
import { watch, computed, onUnmounted, reactive, ref } from "vue";
import { useCallToast } from "@/composable/useCallToast";
import { TYPE } from "vue-toastification";
import { useVerificationActualGet } from "@/composable/API/useVerificationActualGet";
import { Skeletor } from "vue-skeletor";
import UITransition from "@/components/ui/UITransition.vue";
/**
 * SelfieStep from current verification
 */
import SelfieStep from "@/components/Settings/Verification/Steps/SelfieStep.vue";
import VerificationSupportSection from "@/components/VerificationSupportSection.vue";
import type { TVerificationMode } from "@/types/verification/verification";

const verificationPostBody = reactive({
  step: "scale",
});

const isShowScaleVerificationFrame = ref(false);
const isShowUnlimitedVerificationStep = ref(false);

const {
  data: verificationActualData,
  error: verificationActualError,
  isFetching: verificationActualFetching,
  execute: verificationActualUpdate,
} = useVerificationActualGet();

const verificationActualStep = computed<string | null | undefined>(() => {
  return verificationActualData.value?.data?.step;
});

const {
  data: verificationPostData,
  error: verificationPostError,
  isFetching: verificationPostFetching,
  execute: verificationPostFn,
} = useVerificationPost(verificationPostBody, {
  immediate: false,
});

const updateVerificationActualInterval = ref<NodeJS.Timer>();

const passScaleVerificationHandle = () => {
  isShowScaleVerificationFrame.value = true;
  verificationPostFn();
  updateVerificationActualInterval.value = setInterval(
    () => verificationActualUpdate(),
    5000
  );
};

const passUnlimitedVerificationHandle = () => {
  verificationPostBody.step = "unlimited";
  verificationPostFn();
  isShowUnlimitedVerificationStep.value = true;
  updateVerificationActualInterval.value = setInterval(
    () => verificationActualUpdate(),
    5000
  );
};

const closeUnlimitedVerificationStepHandle = () => {
  isShowUnlimitedVerificationStep.value = false;
  clearInterval(updateVerificationActualInterval.value);
};

const closeScaleVerificationFrameHandle = () => {
  isShowScaleVerificationFrame.value = false;
  clearInterval(updateVerificationActualInterval.value);
};

watch(verificationPostError, (newVal) => {
  if (newVal) {
    useCallToast({
      title: "Server error",
      body: verificationPostData?.value?.message,
      options: {
        type: TYPE.ERROR,
        id: "verification-post-error",
      },
    });
    closeScaleVerificationFrameHandle();
  }
});

watch(verificationActualError, (newVal) => {
  if (newVal) {
    useCallToast({
      title: "Server error",
      body: verificationActualData?.value?.message,
      options: {
        type: TYPE.ERROR,
        id: "verification-actual-error",
      },
    });
    clearInterval(updateVerificationActualInterval.value);
  }
});

watch(verificationActualStep, (newVal, oldVal) => {
  if (newVal === "Scale" && (oldVal === "Welcome" || oldVal === null)) {
    closeScaleVerificationFrameHandle();
  }

  if (newVal === "Unlimited") {
    closeUnlimitedVerificationStepHandle();
  }
});

onUnmounted(() => {
  clearInterval(updateVerificationActualInterval.value);
});
</script>

<template>
  <div>
    <UITransition>
      <div
        v-if="
          verificationActualFetching ||
          verificationActualError ||
          !verificationActualData
        "
        class="py-10 grid grid-cols-1 md:grid-cols-2 gap-2 w-full">
        <Skeletor
          as="div"
          class="rounded"
          height="33rem"></Skeletor>

        <Skeletor
          as="div"
          class="rounded"
          height="33rem"></Skeletor>
      </div>

      <div
        v-else
        class="py-10 grid grid-cols-1 md:grid-cols-2 gap-2 w-full">
        <!--          col -1 -->
        <div
          class="rounded px-5 py-6 flex flex-col gap-10 bg-bg-level-1 min-h-[35.7rem]">
          <!--            header -->
          <div class="flex items-start justify-between">
            <p class="text-7 font-bold w-3/12 leading-8">
              {{ $t("verification.twoStepVerificationView.col-1.title") }}
            </p>
            <DynamicIcon
              name="powered-by-sumsub"
              class="w-21 h-auto" />
          </div>
          <!--            body -->
          <div class="grid grid-cols-[auto_1fr] gap-4 w-full">
            <div class="shrink-0">
              <div
                class="two-step-verification__steps-wrap"
                :class="{
                  'scale-passed': verificationActualStep === 'Scale',
                  'unlimited-passed': verificationActualStep === 'Unlimited',
                }">
                <div class="two-step-verification__step-item shrink-0">
                  <span v-if="verificationActualStep === 'Welcome'">1</span>

                  <DynamicIcon
                    v-if="
                      verificationActualStep === 'Scale' ||
                      verificationActualStep === 'Unlimited'
                    "
                    name="check"
                    class="w-4 h-4" />
                </div>

                <div class="steps-dot-wrap relative">
                  <div class="steps-dot-wrap__gradient"></div>
                  <div
                    v-for="item in 25"
                    :key="item"
                    class="w-0.5 h-[0.2rem] rounded bg-fg-purple shrink-0"></div>
                </div>

                <div class="two-step-verification__step-item shrink-0">
                  <span
                    v-if="
                      verificationActualStep === 'Scale' ||
                      verificationActualStep === 'Welcome'
                    "
                    >2</span
                  >

                  <DynamicIcon
                    v-if="verificationActualStep === 'Unlimited'"
                    name="check"
                    class="w-4 h-4" />
                </div>
              </div>
            </div>

            <div>
              <div>
                <p class="text-3.5 leading-4 font-medium">
                  {{
                    $t("verification.twoStepVerificationView.col-1.scale.title")
                  }}
                </p>

                <p class="text-4.5 text-fg-purple font-medium leading-6">
                  {{
                    $t(
                      "verification.twoStepVerificationView.col-1.scale.subtitle"
                    )
                  }}
                </p>
              </div>

              <!-- tags -->
              <div class="flex flex-col xl:flex-row items-start gap-1.5 mt-4">
                <div
                  class="h-8 px-3 bg-bg-level-2 rounded-full flex items-center justify-center">
                  <span class="leading-4 text-3.5 text-nowrap">{{
                    $t("verification.twoStepVerificationView.col-1.scale.tag-1")
                  }}</span>
                </div>

                <div
                  class="h-8 px-3 bg-bg-level-2 rounded-full flex items-center justify-center">
                  <span class="leading-4 text-3.5 text-nowrap">{{
                    $t("verification.twoStepVerificationView.col-1.scale.tag-2")
                  }}</span>
                </div>
              </div>

              <!-- button -->
              <UIButton
                v-if="
                  verificationActualStep === 'Welcome' ||
                  verificationActualStep === null
                "
                color="black"
                class="w-full mt-5"
                :disabled="verificationPostFetching"
                @click="passScaleVerificationHandle">
                <template #left>
                  <DynamicIcon name="id" />
                </template>
                {{ $t("verification.twoStepVerificationView.col-1.scale.btn") }}
              </UIButton>

              <!--divider-->

              <DynamicIcon
                name="verification-divider"
                class="w-full my-7" />

              <div>
                <p class="text-3.5 leading-4 font-medium">
                  {{
                    $t(
                      "verification.twoStepVerificationView.col-1.unlimited.title"
                    )
                  }}
                </p>

                <p class="text-4.5 text-fg-purple font-medium leading-6">
                  {{
                    $t(
                      "verification.twoStepVerificationView.col-1.unlimited.subtitle"
                    )
                  }}
                </p>
              </div>

              <!-- tags -->
              <div class="flex flex-col xl:flex-row items-start gap-1.5 mt-4">
                <div
                  class="h-8 px-3 bg-bg-level-2 rounded-full flex items-center justify-center">
                  <span class="leading-4 text-3.5 text-nowrap">{{
                    $t(
                      "verification.twoStepVerificationView.col-1.unlimited.tag-1"
                    )
                  }}</span>
                </div>

                <div
                  class="h-8 px-3 bg-bg-level-2 rounded-full flex items-center justify-center">
                  <span class="leading-4 text-3.5 text-nowrap">{{
                    $t(
                      "verification.twoStepVerificationView.col-1.unlimited.tag-2"
                    )
                  }}</span>
                </div>
              </div>

              <!-- button -->
              <UIButton
                v-if="verificationActualStep === 'Scale'"
                color="black"
                :disabled="verificationPostFetching"
                class="w-full mt-5"
                @click="passUnlimitedVerificationHandle">
                <template #left>
                  <DynamicIcon name="selfie" />
                </template>
                {{
                  $t("verification.twoStepVerificationView.col-1.unlimited.btn")
                }}
              </UIButton>
            </div>
          </div>
        </div>

        <!--          col-2 -->
        <VerificationSupportSection />
      </div>
    </UITransition>

    <UIFullScreenModal
      :is-open="isShowScaleVerificationFrame"
      :title="$t('verification.twoStepVerificationView.col-1.scale.title')"
      @close="closeScaleVerificationFrameHandle">
      <template #content>
        <div v-if="verificationPostFetching || !verificationPostData">
          <Skeletor
            as="div"
            class="mb-4 rounded"
            height="3rem"></Skeletor>

          <Skeletor
            as="div"
            class="rounded"
            height="35rem"></Skeletor>
        </div>

        <iframe
          v-else
          id="sumframe"
          :src="verificationPostData?.url"
          class="fixed top-[7.5rem] bg-bg-level-0 left-0 w-full h-full lg:top-[6.25rem] z-8"
          allow="camera; microphone"></iframe>
      </template>
    </UIFullScreenModal>

    <UIFullScreenModal
      :title="$t('verification.twoStepVerificationView.col-1.unlimited.title')"
      :is-open="isShowUnlimitedVerificationStep"
      @close="closeUnlimitedVerificationStepHandle">
      <template #content>
        <UITransition>
          <div
            v-if="
              verificationPostFetching ||
              !verificationPostData ||
              !verificationActualStep
            "
            class="w-full lg:w-[50%] lg:mx-auto py-10">
            <Skeletor
              as="div"
              class="rounded mb-3"
              height="3rem"></Skeletor>

            <Skeletor
              as="div"
              class="rounded mb-3"
              height="6.9rem"></Skeletor>

            <Skeletor
              as="div"
              class="rounded mb-3"
              height="15rem"></Skeletor>

            <Skeletor
              as="div"
              class="rounded"
              height="3rem"></Skeletor>
          </div>

          <div
            v-else
            class="w-full lg:w-[50%] lg:mx-auto py-10">
            <SelfieStep
              :data="verificationPostData"
              :actual="verificationActualStep as TVerificationMode"
              @closed="closeUnlimitedVerificationStepHandle" />
          </div>
        </UITransition>
      </template>
    </UIFullScreenModal>
  </div>
</template>

<style lang="scss" scoped>
.two-step-verification__step-item {
  @apply flex items-center justify-center w-9 h-9 bg-fg-purple text-4.5
  font-medium rounded-full text-fg-contrast;
}

.two-step-verification__steps-wrap {
  @apply flex items-center flex-col justify-between gap-2.5;

  .two-step-verification__step-item:first-child {
    @apply opacity-100;
  }

  .two-step-verification__step-item:last-child {
    @apply opacity-30;
  }

  .steps-dot-wrap__gradient {
    @apply absolute top-0 left-0 w-full h-full bg-gradient-to-t from-bg-level-1;
  }

  .steps-dot-wrap {
    @apply h-[11.875rem] lg:h-[9.375rem] overflow-hidden flex flex-col items-center gap-1 shrink-0;
  }
}

.two-step-verification__steps-wrap.scale-passed {
  .two-step-verification__step-item:first-child {
    @apply opacity-30;
  }

  .two-step-verification__step-item:last-child {
    @apply opacity-100;
  }

  .steps-dot-wrap {
    @apply h-[7.812rem] lg:h-[5.625rem];
  }

  .steps-dot-wrap__gradient {
    @apply bg-gradient-to-b;
  }
}

.two-step-verification__steps-wrap.unlimited-passed {
  .two-step-verification__step-item:first-child {
    @apply opacity-100;
  }

  .two-step-verification__step-item:last-child {
    @apply opacity-100;
  }

  .steps-dot-wrap {
    @apply h-[7.812rem] lg:h-[5.625rem];
  }
}
</style>
