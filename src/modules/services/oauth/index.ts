import { defineService } from "@modules/services/base";
import type { TServicePayloads, TServiceResponses } from "./types";
import { ServiceEndpoint } from "./types";

const useOauthService = defineService<TServiceResponses, TServicePayloads>(
  "oauthServiceConfig"
);

const googleLink = useOauthService<
  TServiceResponses[ServiceEndpoint.GOOGLE_LINK_KEY],
  TServicePayloads[ServiceEndpoint.GOOGLE_LINK_KEY]
>;

const appleLink = useOauthService<
  TServiceResponses[ServiceEndpoint.APPLE_LINK_KEY],
  TServicePayloads[ServiceEndpoint.APPLE_LINK_KEY]
>;

const jwtCallback = useOauthService<
  TServiceResponses[ServiceEndpoint.JWT_CALLBACK_KEY],
  TServicePayloads[ServiceEndpoint.JWT_CALLBACK_KEY]
>;

export const OauthService = {
  googleLink: googleLink.bind(null, ServiceEndpoint.GOOGLE_LINK_KEY),
  appleLink: appleLink.bind(null, ServiceEndpoint.APPLE_LINK_KEY),
  jwtCallback: jwtCallback.bind(null, ServiceEndpoint.JWT_CALLBACK_KEY),
};
