<template>
  <UINavigation
    :list="navigationUser"
    :with-labels="props.withLabels" />
</template>

<script setup lang="ts">
import { computed } from "vue";
import UINavigation from "@/components/ui/UINavigation/UINavigation.vue";
import type { INavigationItem } from "@/components/Navigation/types";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { RouteName } from "@/constants/route_name";
import { useI18n } from "vue-i18n";

interface PropsNavigationUser {
  withLabels: boolean;
}

const { t } = useI18n();

const { subscriptionsInfo } = useSubscriptionsInfo();

const props = withDefaults(defineProps<PropsNavigationUser>(), {
  withLabels: true,
});

const navigationUser = computed<INavigationItem[]>(() => [
  {
    id: 1,
    to: { name: RouteName.DASHBOARD },
    icon: "home",
    label: t("team.aside-dashboard"),
  },
  {
    id: 2,
    to: { name: RouteName.ACCOUNTS },
    icon: "wallet",
    label: t("my_accounts"),
  },
  {
    id: 3,
    to: { name: RouteName.CARDS },
    icon: "card",
    label: t("my_cards"),
  },
  {
    id: 4,
    to: { name: RouteName.PERSONAL_PAYMENTS },
    icon: "payments",
    label: t("personal.payments"),
  },
  {
    id: 5,
    to: {
      name: subscriptionsInfo?.value?.status
        ? RouteName.SUBSCRIPTION_DETAIL
        : RouteName.SUBSCRIPTION,
    },
    icon: "private",
    label: "Private",
  },
  {
    id: 6,
    to: { name: RouteName.TEAM },
    icon: "team",
    label: t("personal.aside-team"),
  },
  {
    id: 7,
    to: { name: RouteName.AFFILIATE },
    icon: "gift",
    label: t("affiliate.title.new"),
  },
]);
</script>
