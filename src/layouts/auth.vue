<script lang="ts" setup>
import Logo2 from "@/components/icons/Logo2.vue";
</script>

<template>
  <div :class="auth.root">
    <div :class="auth.header">
      <Logo2 />
    </div>

    <div :class="auth.main">
      <slot />
    </div>

    <PortalTarget
      name="modals"
      multiple
      :class="auth.target" />
  </div>
</template>

<style lang="scss" module="auth">
.root {
  @apply flex flex-col items-center gap-10 px-2 min-h-screen;
}
.header {
  @apply flex items-center justify-center px-5 py-7;

  & svg {
    @apply w-[137px] h-[40px];
  }
}
.main {
  @apply max-w-[440px] w-full h-full pb-10;
}
.target {
  @apply absolute z-[1010];
}
</style>
