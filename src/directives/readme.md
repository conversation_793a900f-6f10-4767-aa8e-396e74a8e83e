## Z-INDEX Derective

Needed to set z-index when clicking on an element.
An object or an array of objects should be passed to the dereq, where elem is the selector of the element for which the z-index should be set, index is the z-index value.

    v-index="{ elem: '#top-bar-new', index: 50 }"

    //or

    v-index="[{ elem: '#top-bar-new', index: 999 },{ elem: '.balance-widget', index: 40 },]"


## The v-track derektive

The v-track dereq hover tracker event for analytics
For example

    v-track:button="{ 
    page_version: 'free_card-1',
    name: 'Pick up',
    version: 'free_card-1',
    location: 'free_card_before',
    }"
