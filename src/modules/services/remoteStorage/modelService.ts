import type { IRemoteStorageModel, ERemoteStorageKeys } from "./model";
import type { IRemoteStorageCardDto } from "./dto";
import type {
  IServiceResponse,
  TCommonResponse,
} from "@modules/services/base/types";

export interface IRemoteStorageService {
  get: (key: any) => Promise<any>;
  set: (key: any, value: any) => Promise<any>;
  setProMode: (
    config: IRemoteStorageModel<ERemoteStorageKeys.IS_PRO_MODE, boolean>
  ) => Promise<IServiceResponse<{ success: boolean }, unknown>>;
  getCard: (
    key: ERemoteStorageKeys.CARD_DATA
  ) => Promise<IRemoteStorageCardDto | undefined>;
  setCard: (
    config: IRemoteStorageModel<
      ERemoteStorageKeys.CARD_DATA,
      IRemoteStorageCardDto
    >
  ) => Promise<TCommonResponse | undefined>;
}
