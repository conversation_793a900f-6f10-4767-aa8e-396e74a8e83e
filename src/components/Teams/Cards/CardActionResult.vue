<template>
  <div class="flex flex-col items-center gap-6">
    <DynamicIcon
      :name="props.icon"
      class="w-[120px] h-[120px]" />

    <div class="flex flex-col gap-4">
      <h3 class="text-center">
        {{ title }}
      </h3>

      <p class="text-gray-600 text-base font-medium text-center">
        {{ subtitle }}
      </p>
    </div>

    <UiButton
      class="w-full"
      :title="$t('buttons.close')"
      @click="emit('close')" />
  </div>
</template>

<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

interface Props {
  // check-circle || card-blocked-circle
  icon?: string;
  title?: string;
  subtitle?: string;
}

const emit = defineEmits(["close"]);
const props = withDefaults(defineProps<Props>(), {
  icon: "card-blocked-circle",
  title: "title",
  subtitle: "subtitle",
});
</script>

<style lang="sass" module></style>
