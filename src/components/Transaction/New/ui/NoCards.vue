<template>
  <div :class="$style.root">
    <DynamicIcon
      name="card"
      class="w-6 h-6 mx-auto" />

    {{ $t("No card found") }}
  </div>
</template>

<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
</script>

<style lang="scss" module>
.root {
  @apply flex py-4 flex-col gap-2 justify-center items-center text-center mx-auto text-greyscale-500 font-medium;
}
</style>
