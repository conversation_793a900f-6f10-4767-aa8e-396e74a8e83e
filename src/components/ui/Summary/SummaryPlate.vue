<script lang="ts" setup>
import SectionLoader from "@/components/ui/Loader/SectionLoader.vue";

interface Props {
  padding?: string;
  border?: string;
  loaderColor?: string;
  loading?: boolean;
}

const props = defineProps<Props>();
</script>

<template>
  <div
    class="relative rounded-2xl"
    :class="[
      padding ? padding : $style.defaultPadding,
      border ? border : $style.defaultBorder,
    ]">
    <SectionLoader
      v-if="loading"
      :color="loaderColor" />

    <slot />
  </div>
</template>

<style lang="scss" module>
.defaultPadding {
  @apply py-6 px-4;
}

.defaultBorder {
  @apply border border-gray-200;
}
</style>
