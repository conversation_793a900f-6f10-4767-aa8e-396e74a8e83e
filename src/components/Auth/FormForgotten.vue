<template>
  <div :class="$style.FormForgotten">
    <div class="text-h4 lg:text-h3 font-extrabold">
      {{ $t("need_help_with_your_account") }}
    </div>

    <div v-if="newPassword">
      <ValidationPasswordInput
        :value="password"
        ref="password"
        @input="(v) => (password = v)" />

      <ConfirmationPasswordInput
        class="mt-3"
        ref="confirmation"
        :value="repeatPassword"
        :confirmation="password"
        @input="(v) => (repeatPassword = v)" />

      <span
        v-if="error"
        data-cy="login_error"
        class="col-span-2 text-sm text-error-light">
        {{ error }}
      </span>

      <UiButton
        data-cy="login_button_login_with_email"
        size="large"
        type="primary"
        :title="$t('sign_in')"
        class="col-span-2 mt-3 mb-4 min-w-full"
        @click="setNewPassword"
        :disabled="isButtonDisabled" />
    </div>

    <div v-else>
      <div class="mb-6">
        <span class="text-greyscale-600">
          {{
            $t(
              "Enter the email address associated with your account and we will send you a link to reset your password"
            )
          }}
        </span>
      </div>

      <UiInputText
        :value="email.value.value"
        @input="email.setValue"
        @blur="setHelperEmail"
        type="email"
        :error="email.errorMessage"
        placeholder="E-Mail"
        class="mb-4 w-full"
        data-cy="forgot_input"
        @change="checkUserForExist"
        @keydown.enter="sendLink()" />

      <span
        v-if="info"
        class="font-light"
        :class="success ? 'text-success-base' : 'text-error-base'">
        {{ info }}
      </span>

      <UiButton
        :title="$t('send_link')"
        size="large"
        class="mb-2 mt-4 min-w-full"
        data-cy="send_link"
        :disabled="isSendButtonDisabled"
        @click="sendLink" />
    </div>
  </div>
</template>

<script>
import { useEmail } from "@/helpers/validation";
import UiInputText from "~/components/ui/InputText/InputText";
import UiButton from "~/components/ui/Button/Button";

import ValidationPasswordInput from "@/components/Auth/ValidationPasswordInput";
import ConfirmationPasswordInput from "@/components/Auth/ConfirmationPasswordInput";
import { useStorage } from "@vueuse/core";
import { UserService } from "@modules/services/user";
import { loggerConsole } from "@/helpers/logger/Logger";
import Config from "@/config/env";

export default {
  setup() {
    const authHelperEmail = useStorage("auth-helper-email", "");
    const email = useEmail("email", authHelperEmail.value);
    const logger = loggerConsole();

    const setHelperEmail = () => {
      if (email.meta.valid) {
        authHelperEmail.value = email.value.value;
      } else {
        authHelperEmail.value = "";
      }
    };

    return {
      email,
      logger,
      setHelperEmail,
      authHelperEmail,
    };
  },
  name: "AuthFormForgotten",
  components: {
    UiInputText,
    UiButton,
    ValidationPasswordInput,
    ConfirmationPasswordInput,
  },
  props: {
    newPassword: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      info: "",
      success: null,
      error: false,
      password: "",
      repeatPassword: "",
      linkSended: false,
      isLoading: false,
    };
  },
  computed: {
    isButtonDisabled() {
      return (
        this.newPassword &&
        (this.$refs.password?.passwordError ||
          this.$refs.confirmation?.confirmationError ||
          this.password.length === 0 ||
          this.repeatPassword.length === 0)
      );
    },
    isSendButtonDisabled() {
      return this.linkSended || this.isLoading || this.email.errorMessage.value;
    },
  },
  methods: {
    async sendLink() {
      if (this.isSendButtonDisabled) {
        return;
      }

      try {
        this.isLoading = true;

        const recaptcha_token = await window.grecaptcha.execute(
          Config.grecaptcha,
          {
            action: "submit",
          }
        );
        const result = await UserService.resetPassword({
          email: this.email.value.value,
          recaptcha_token,
        });

        this.success = result.status;

        this.info = result.status
          ? this.$t("recovery_link_was_send")
          : result.message;
        this.linkSended = result.status;
      } catch (ex) {
        console.error("FormForgotten->sendLink error handled: ", ex);
      } finally {
        this.isLoading = false;
      }
    },
    async setNewPassword() {
      const result = await UserService.setPassword({
        password: this.password,
        token: this.$route.query?.token,
      });

      if (result.status) {
        await this.$router.push("/login");
      } else {
        this.logger.error("setNewPassword", "failed");
      }
    },
  },
};
</script>

<style lang="scss" module>
.FormForgotten {
  @apply relative py-10 px-4 lg:px-10 bg-white rounded-lg flex flex-col justify-center items-center text-center gap-4;
  box-shadow: 0 16px 24px rgba(93, 106, 131, 0.02);
}
</style>
