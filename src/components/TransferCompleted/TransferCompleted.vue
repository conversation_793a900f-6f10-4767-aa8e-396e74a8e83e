<script setup lang="ts">
import { RouteName } from "@/constants/route_name";
import { getAccountName, prepareAccountBalance } from "@/helpers";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { computed } from "vue";
import { useUserTariff } from "@/composable";
import type { TCardResource } from "@/types/api/TCardResource";
import type { TransferFormAccountOption } from "@/components/TransferModal/types";
import type { IsoCodeNames } from "@/constants/iso_code_names";
import { getCurrencySymbolByIso } from "@/helpers/account";
import { useI18n } from "vue-i18n";
import eventEmitter from "@/services/EventEmitter";
import { EmitEventsNames } from "@/constants/emit_events_names";
import { useCardsTableState } from "@/components/CardsTable/useCardsTableState";
import { useTeamModalAccountInfo } from "@/composable/useTeamModalAccountInfo";

type Props = {
  hasPrivate?: boolean;
  toCard?: TCardResource | null;
  fromCard?: TCardResource | null;
  toAccount?: TransferFormAccountOption | null;
  fromAccount?: TransferFormAccountOption | null;
  toAmount: number;
  toDirection?: "card" | "account" | null;
  isImmediate?: boolean;
  fromDirection?: "card" | "account" | null;
  toCurrency: IsoCodeNames | null;
  fromCurrency?: IsoCodeNames | null;
  isSpeedUpAvailable?: boolean;
};

type Emit = {
  close: [];
};

const props = withDefaults(defineProps<Props>(), {
  hasPrivate: false,
  isSpeedUpAvailable: true,
  isImmediate: false,
  toCard: null,
  fromCard: null,
  toAccount: null,
  fromAccount: null,
  toDirection: null,
  fromCurrency: null,
  fromDirection: null,
});

const emit = defineEmits<Emit>();
const { t } = useI18n();
const { getUserTariffNameByTariffId } = useUserTariff();
const { open: openTeamAccountInfo } = useTeamModalAccountInfo();

const withdrawalHours = props.hasPrivate ? 1 : 12;
const { setModalState, setSelectedCard } = useCardsTableState();

const isTransferFromAccountToCard = computed<boolean>(
  () => props.fromDirection === "account" && props.toDirection === "card"
);

const isTransferFromAccountToAccount = computed<boolean>(
  () => props.fromDirection === "account" && props.toDirection === "account"
);

const isImmediateTransfer = computed<boolean>(() => {
  return (
    isTransferFromAccountToCard.value ||
    isTransferFromAccountToAccount.value ||
    props.isImmediate
  );
});

const amountSent = computed(() => {
  return `${isImmediateTransfer.value ? "+" : ""}${prepareAccountBalance(
    props.toAmount,
    props.toCurrency ?? "USD"
  )} ${getCurrencySymbolByIso(props.toCurrency ?? "USD")}`;
});

const title = computed(() => {
  if (isTransferFromAccountToCard.value) {
    return t("transfer-completed.card-topped-up");
  }

  if (isTransferFromAccountToAccount.value) {
    return t("transfer-completed.account-topped-up");
  }

  return t("transfer-completed.default-top-up");
});

const goToClickHandler = () => {
  emit("close");

  if (isTransferFromAccountToAccount.value && props.toAccount) {
    const isToMemberAccount = "user_id" in props.toAccount;

    if (isToMemberAccount) {
      openTeamAccountInfo(props.toAccount.id);
    } else {
      eventEmitter.emit(EmitEventsNames.MODALS_ACCOUNT_INFO, {
        accountId: props.toAccount.id,
      });
    }

    return;
  }

  if (isTransferFromAccountToCard.value && props.toCard) {
    setSelectedCard(props.toCard);
    setModalState(true, "cardDetailFull");
  }
};

const canSpeedUpTransfer = computed(() => {
  return (
    !props.hasPrivate && !isImmediateTransfer.value && props.isSpeedUpAvailable
  );
});
</script>

<template>
  <div class="h-full flex flex-col gap-10 justify-center items-center">
    <div class="success-icon-container">
      <DynamicIcon
        class="text-fg-contrast w-7 h-7"
        name="check-thin" />
    </div>
    <div class="flex flex-col items-center text-center">
      <div class="text-4.5 font-medium leading-6 mb-4">
        <slot name="title">{{ title }}</slot>
      </div>
      <p class="text-9 font-medium leading-10 mb-1">
        {{ amountSent }}
      </p>
      <div class="text-3.5 leading-4 text-fg-secondary">
        <div class="flex py-0.5 items-center justify-center">
          <slot name="from">
            <p v-if="props.fromCard">
              <span class="px-1">{{
                getUserTariffNameByTariffId(props.fromCard.tariff_id)
              }}</span>
              <span class="px-1">{{ props.fromCard.mask.slice(-4) }}</span>
            </p>
            <p
              v-else-if="props.fromCurrency"
              class="px-1">
              {{ getAccountName(props.fromCurrency) }}
            </p>
          </slot>
          <DynamicIcon
            v-if="props.toCard || props.toAccount || $slots.to"
            name="arrow-right"
            class="w-4 h-4" />
          <p class="px-1 flex gap-1">
            <slot name="to">
              <template v-if="toCard">
                <span>{{ getUserTariffNameByTariffId(toCard.tariff_id) }}</span>
                <span>{{ toCard.mask.slice(-4) }}</span>
              </template>
              <template v-else-if="props.toCurrency && props.toAccount">
                {{ getAccountName(props.toCurrency) }}
              </template>
            </slot>
          </p>
        </div>
        <p
          v-if="!isImmediateTransfer"
          class="py-0.5 px-1">
          <slot name="delay">
            {{
              t("transfer-completed.delay", {
                a: withdrawalHours,
                b: t("common.hour", withdrawalHours),
              })
            }}
          </slot>
        </p>
      </div>
    </div>

    <div
      class="flex gap-2 items-center justify-center *:max-w-[8.75rem] w-full *:w-full">
      <UIButton
        v-if="isTransferFromAccountToAccount || isTransferFromAccountToCard"
        color="grey-solid"
        @click="goToClickHandler">
        {{
          props.toCard
            ? t("transfer-completed.go-to-card")
            : t("transfer-completed.go-to-account")
        }}
      </UIButton>
      <UIButton
        v-else
        color="grey-solid"
        @click="emit('close')">
        {{ $t("buttons.close") }}
      </UIButton>
      <router-link
        v-if="canSpeedUpTransfer"
        :to="{ name: RouteName.SUBSCRIPTION_TARIFF }"
        @click="emit('close')">
        <UIButton
          class="w-full"
          color="orange-solid">
          {{ t("transfer-completed.speed-up") }}
        </UIButton>
      </router-link>
    </div>
  </div>
</template>

<style scoped lang="scss">
.success-icon-container {
  @apply w-15 h-15 rounded-full shadow-[0px_0px_0px_8px]
  shadow-bg-green-light flex items-center justify-center bg-bg-green-solid;
}
</style>
