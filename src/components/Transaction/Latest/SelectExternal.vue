<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useI18n } from "vue-i18n";
import type { TWithdrawToDirection } from "@/components/Transaction/Latest/types";
interface Props {
  directions: Array<TWithdrawToDirection>;
}
const emit = defineEmits(["change"]);
const { t } = useI18n();
const props = defineProps<Props>();
</script>

<template>
  <div :class="selectExternal.root">
    <h4>{{ t("withdraw.chooseDirection") }}</h4>
    <ul>
      <li
        v-for="(item, index) in props.directions"
        :key="index"
        @click="emit('change', item.id)">
        <span>
          <DynamicIcon
            :name="item.icon"
            :path="item.iconPath"
            :class="
              selectExternal[
                `${item.icon === 'square-usdt' ? 'icon-full' : 'icon-fixed'}`
              ]
            " />
        </span>
        <p>{{ item.title }}</p>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" module="selectExternal">
.root {
  @apply flex flex-col gap-10 h-full;

  & h4,
  & h6 {
    @apply font-semibold leading-[1.2];
  }

  & > ul {
    @apply flex flex-col gap-3;

    & > li {
      @apply flex items-center gap-3 p-3;
      @apply cursor-pointer rounded-[6px];
      @apply text-normal text-neutral-800 leading-[1.2];
      @apply bg-neutral-100;
      @apply transition-all hover:bg-neutral-150;

      & > span {
        @apply w-10 h-10 min-w-[40px] overflow-hidden;
      }

      &:first-of-type {
        & > span {
          @apply flex items-center justify-center;
          @apply rounded-[6px];
          @apply bg-neutral-0;
        }
      }

      &:last-of-type {
        & > span {
          @apply rounded-[6px];
        }
      }
    }
  }
}

.icon {
  &-fixed {
    @apply w-6 h-6;
  }

  &-full {
    @apply w-full h-full;
  }
}
</style>
