// polyfill
// TODO: Remove after browser support has been downgraded.
import "./polyfill.replaceAll";

import Chevron from "@/assets/svg/icon/chevron-d.svg";
import type { App as AppType, WritableComputedRef } from "vue";
import { createApp } from "vue";

import { createPinia } from "pinia";
import "@/assets/scss/app.scss";
import "vue-skeletor/dist/vue-skeletor.css";
import "vue-toastification/dist/index.css";
import Portal from "vue3-portal";
import AuthGuard from "@/middleware/auth";
import TeamGuard from "@/middleware/teamGuard";
import {
  vClickOutside,
  vFocus,
  vIndex,
  vMask,
  vRipple,
  vTrack,
} from "./directives";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import "floating-vue/dist/style.css";
import SetupCalendar from "v-calendar";
import FloatingVue from "floating-vue";
import App from "./App.vue";
import router from "./router";
import { focusManager, VueQueryPlugin } from "vue-query";
import Toast from "vue-toastification";
import { initSentry } from "@/helpers/sentry";
import { Container } from "@/helpers/container/container";
import { AmplitudeTracker } from "@/helpers/tracker/amplitude.tracker";
import { GoogleTracker } from "@/helpers/tracker/google.tracker";
import { TrackerService } from "@/helpers/tracker/tracker.types";
import { Logger } from "@/helpers/logger/Logger";
import OneSignalVuePlugin from "@onesignal/onesignal-vue3";
import Config from "@/config/env";
import utmMiddleware from "./middleware/utmMiddleware";
import "simplebar-vue/dist/simplebar.min.css";
import geoLockMiddleware from "@/middleware/geoLockMiddleware";
import paymentMiddleware from "@/middleware/payment";
import { getBaseFontSize } from "@/helpers/getBaseFontSize";
import { useI18nInstance } from "@/composable/useI18nInstance";
import { useDictionary } from "@/stores/dictionary";
import AuthConfig from "@/config/auth";
import * as Sentry from "@sentry/vue";
import ExperimentsService from "@/services/ExperimentsService";
import { locales } from "@/config/i18n";
import type { I18n } from "vue-i18n";
import { useUserStore } from "@/stores/user";
import { useAuth } from "@/composable";
import type { TUserInfo } from "@/types/user/user.types";
import type { TErrorResponse } from "@modules/services/base/types";
import { has } from "lodash";
import { autoAnimatePlugin } from "@formkit/auto-animate/vue";

//Global setting for vue-select component
vSelect.props.components.default = () => ({
  Deselect: null,
  OpenIndicator: Chevron,
});

//Global setting for vue-select component end

/** */
class CreateApp {
  private app: AppType | null = null;
  private i18n: I18n | null = null;

  /** */
  async init() {
    const { i18nInstance, setLanguageLazy } = useI18nInstance();

    this.app = createApp(App);
    this.app.use(createPinia());
    this.app.use(autoAnimatePlugin);

    // register services in container
    Container.register(TrackerService.AMPLITUDE, AmplitudeTracker);
    Container.register(TrackerService.GOOGLE, GoogleTracker);

    const userStore = useUserStore();
    const { delToken, setAuthForExtension, getToken } = useAuth();

    const userHasError = (
      value: Partial<TUserInfo> | TErrorResponse
    ): value is TErrorResponse => {
      return has(value, "message");
    };

    this.i18n = i18nInstance;
    this.app.provide("setLanguageLazy", setLanguageLazy);
    this.app.provide<Logger>("logger", new Logger());

    const { getDictionary } = useDictionary();
    await getDictionary();

    // init sentry
    const dsn = import.meta.env.VITE_SENTRY_DSN;

    if (dsn) {
      initSentry(this.app, router, dsn);
    }

    router.beforeEach(geoLockMiddleware);
    router.beforeEach(utmMiddleware);
    router.beforeEach(paymentMiddleware);
    router.beforeEach(AuthGuard);
    router.beforeEach(TeamGuard);

    const pathname = window.location.pathname;
    const urlParams = new URLSearchParams(window.location.search);

    let lang = "en";
    const queryLang = urlParams.get("lang");
    if (queryLang && queryLang !== "en") {
      lang = queryLang;
    }

    if (getToken()) {
      await Promise.all<(Partial<TUserInfo> | any) | void>([
        userStore.getUser(),
        userStore.getUserFees(),
      ]).then(async ([userResponse]) => {
        // if user is Unauthenticated
        if (
          userHasError(userResponse) &&
          userResponse.message === "Unauthenticated"
        ) {
          if (!AuthConfig.availablePaths.includes(pathname)) {
            Sentry.addBreadcrumb({
              category: "oauth",
              message:
                `logout reason: Unauthenticated, Debug: ` +
                location.href +
                location.search,
              level: "info",
            });

            // Del token user is Unauthenticated
            delToken();

            await router.push("/login");
            return;
          }
        }

        setAuthForExtension();
        const kycLevel = await userStore.getActualKycLevel();
        if (kycLevel === "old") {
          ExperimentsService.resetActiveExperiments();
        }

        if (!userHasError(userResponse)) {
          lang = userStore.language;
        }
      });
    }

    if (locales.find((v) => v.code === lang)) {
      await setLanguageLazy(lang);
      if (this.i18n) {
        (this.i18n.global.locale as WritableComputedRef<string>).value = lang;
      }
    }

    this.app.use(VueQueryPlugin);
    this.app.use(Toast, {
      shareAppContext: true,
      containerClassName: "ui-toast__container",
      transition: "Vue-Toastification__fade",
    });
    this.app.use(router);
    this.app.use(this.i18n);
    this.app.use(Portal);
    this.app.use(SetupCalendar);
    this.app.use(FloatingVue, {
      themes: {
        "ui-popper-fade-slide-right": {
          $resetCss: true,
          triggers: ["click"],
          autoHide: true,
          placement: "bottom",
        },
        "ui-popper-dropdown": {
          $resetCss: true,
          triggers: ["click"],
          autoHide: true,
          placement: "bottom",
        },
        "ui-popper-dropdown-black": {
          triggers: ["click"],
          $resetCss: true,
          autoHide: true,
          placement: "bottom",
        },
        "ui-tooltip": {
          $resetCss: true,
          triggers: ["hover"],
          autoHide: true,
          placement: "bottom",
        },
      },
    });

    if (!import.meta.env.DEV) {
      this.app.use(OneSignalVuePlugin, {
        appId: Config.onesignalAppId,
        safari_web_id: Config.onesignalSafariId,
        allowLocalhostAsSecureOrigin: true,
        notificationClickHandlerMatch: "origin",
        notificationClickHandlerAction: "focus",
      });
    }

    this.app.directive("ripple", vRipple);
    this.app.directive("click-outside", vClickOutside);
    this.app.directive("mask", vMask);
    this.app.directive("focus", vFocus);
    this.app.directive("track", vTrack);
    this.app.directive("index", vIndex);

    this.app.component("v-select", vSelect);

    // focus manager settings by vue query
    focusManager.setEventListener((handleFocus: any) => {
      const onEventHandler = (event: Event) => {
        // update queries only for visibilitychange
        if (event.type === "visibilitychange") {
          return handleFocus(event);
        } else {
          return false;
        }
      };

      // Listen to visibility change and focus
      if (typeof window !== "undefined" && window.addEventListener) {
        window.addEventListener("visibilitychange", onEventHandler, false);
        window.addEventListener("focus", onEventHandler, false);
      }

      return () => {
        // Be sure to unsubscribe if a new handler is set
        window.removeEventListener("visibilitychange", handleFocus);
        window.removeEventListener("focus", handleFocus);
      };
    });

    // app styles
    const appHeight = () => {
      const doc = document.documentElement;
      doc.style.setProperty(
        "--ui-app-height",
        `${window.innerHeight / getBaseFontSize()}rem`
      );
    };
    window.addEventListener("resize", appHeight);
    appHeight();

    await router.isReady(); // wait until ready is ready
    this.app.mount("#app");
  }
}

export default CreateApp;
