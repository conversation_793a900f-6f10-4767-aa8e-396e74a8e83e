<template>
  <transition name="modal">
    <div :class="$style.root">
      <div
        :class="[$style.wrapper, wrapperClass]"
        v-click-outside="close">
        <slot />
      </div>
    </div>
  </transition>
</template>

<script lang="ts" setup>
interface Props {
  wrapperClass?: string;
}

defineProps<Props>();
const emit = defineEmits(["close"]);

// methods
const close = () => emit("close");
</script>

<style lang="scss" module>
.root {
  @apply fixed z-[1020] top-0 left-0 right-0 bottom-0 w-full h-full bg-white;
  @apply bg-opacity-75 block overflow-y-auto flex items-center justify-center;
  transition: opacity 0.4s ease;
}

.wrapper {
  @apply flex items-center justify-center flex-col h-auto bg-white px-5 py-7;
  @apply max-w-[500px] rounded-xl w-full;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04), 0px 8px 16px rgba(0, 0, 0, 0.08);
}
</style>
