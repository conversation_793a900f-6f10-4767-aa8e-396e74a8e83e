import type { TAxiosRequestConfig } from "@/types/api";
import type { IBin } from "@/types/card";

export enum ServiceBase {
  value = "business",
}

export enum ServiceEndpoint {
  LIST = "list",
  INFO = "info",
  UPDATE = "update",
  BLOCK_CARD = "blockCard",
  MULTI_BUY = "multiBuy",
  MULTI_BUY_CHECK = "multiBuyCheck",
  CREATE_REFILL = "createRefill",
  DELETE_REFILL = "deleteRefill",
  UPDATE_REFILL = "updateRefill",
  CHECK_AUTO_BUY_TRANSACTION = "checkAutoBuyTransaction",
  GET_BINS = "bins",
  AUTO_BUY_SHOW = "autoBuyShow",
}

export type TPayloads = {
  [ServiceEndpoint.LIST]: TAxiosRequestConfig<any, { type: string }>;
  [ServiceEndpoint.UPDATE]: Partial<
    Record<"description" | "favorite", boolean | string>
  > & { urlParams: { id: number } };
  [ServiceEndpoint.INFO]: { urlParams: { id: number } };
  [ServiceEndpoint.BLOCK_CARD]: {
    urlParams: { id: number };
    data: { account_id: number };
  };
  [ServiceEndpoint.MULTI_BUY]: {
    account_id: number;
    type: string;
    start_balance: number;
    description: string;
    system: string;
    bin: string;
    count: number;
  };
  [ServiceEndpoint.MULTI_BUY_CHECK]: {
    account_id: number;
    type: string;
    start_balance: number;
    description: string;
    system: string;
    bin: string;
    count: number;
  };
  [ServiceEndpoint.CREATE_REFILL]: {
    card_id: number;
    minimum_balance: string;
    amount_refill: string;
  };
  [ServiceEndpoint.DELETE_REFILL]: undefined;
  [ServiceEndpoint.UPDATE_REFILL]: {
    card_id: number;
    minimum_balance: string;
    amount_refill: string;
    urlParams: { id: number };
    active: boolean;
  };
  [ServiceEndpoint.CHECK_AUTO_BUY_TRANSACTION]: {
    urlParams: {
      id: number;
    };
  };
  [ServiceEndpoint.GET_BINS]: TAxiosRequestConfig<any, { type: string }>;
  [ServiceEndpoint.AUTO_BUY_SHOW]: undefined;
};

export type TResponses = {
  [ServiceEndpoint.LIST]: any;
  [ServiceEndpoint.UPDATE]: any;
  [ServiceEndpoint.INFO]: any;
  [ServiceEndpoint.BLOCK_CARD]: any;
  [ServiceEndpoint.MULTI_BUY]: any;
  [ServiceEndpoint.MULTI_BUY_CHECK]: any;
  [ServiceEndpoint.CREATE_REFILL]: any;
  [ServiceEndpoint.DELETE_REFILL]: any;
  [ServiceEndpoint.UPDATE_REFILL]: any;
  [ServiceEndpoint.CHECK_AUTO_BUY_TRANSACTION]: any;
  [ServiceEndpoint.GET_BINS]: IBin[];
  [ServiceEndpoint.AUTO_BUY_SHOW]: any;
};
