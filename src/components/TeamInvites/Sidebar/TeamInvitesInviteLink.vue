<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { copyToClipBoard } from "@/helpers/copyToClipboard";
import { computed, ref } from "vue";
import { TOAST_TYPE, useCallToast } from "@/composable";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps<{
  masterHash: string;
  isLoading: boolean;
}>();

const emits = defineEmits(["refresh-link"]);

const showCopiedCheckmark = ref(false);

const inviteLink = computed(() => {
  return `https://app.pst.net/register?master_hash=${props.masterHash}`;
});

const refreshLinkHandler = () => {
  emits("refresh-link");
};

const copy = () => {
  copyToClipBoard(inviteLink.value);
  useCallToast({
    title: t("team-invites.copied"),
    options: {
      type: TOAST_TYPE.DEFAULT,
    },
  });
  showCopiedCheckmark.value = true;
  setTimeout(() => {
    showCopiedCheckmark.value = false;
  }, 2000);
};
</script>

<template>
  <div>
    <div class="mb-2">
      <span class="font-medium text-4 leading-6 text-fg-primary">
        {{ $t("team-invites.invite-via-link") }}
      </span>
    </div>
    <div class="w-full bg-bg-level-1 rounded p-3 flex items-start">
      <div>
        <span class="text-normal leading-5 text-fg-primary">
          {{ inviteLink }}
        </span>
      </div>
      <div class="flex items-center">
        <div>
          <DynamicIcon
            name="reload"
            color="fg-primary"
            class="cursor-pointer w-6"
            :class="{ 'animate-spin': props.isLoading }"
            @click="refreshLinkHandler" />
        </div>

        <div>
          <DynamicIcon
            v-if="!showCopiedCheckmark"
            name="copy"
            color="fg-primary"
            class="cursor-pointer w-6"
            @click="copy" />
        </div>
        <DynamicIcon
          v-if="showCopiedCheckmark"
          name="check"
          color="fg-primary"
          class="w-6" />
      </div>
    </div>
  </div>
</template>
