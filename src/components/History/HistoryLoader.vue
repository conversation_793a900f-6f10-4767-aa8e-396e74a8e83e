<template>
  <div :class="$style.TransactionsView">
    <UiTable
      class="mt-6"
      template="xl:grid-cols-[minmax(64px,_1fr)_minmax(64px,_1fr)_minmax(64px,_100px)_minmax(64px,_100px)_minmax(64px,_100px)] gap-x-3 gap-y-2">
      <template #header>
        <transition
          name="accordion"
          @before-enter="beforeEnter"
          @enter="enter"
          @before-leave="beforeLeave"
          @leave="leave">
          <UiTableHeader
            v-if="showFilters"
            :template="'sm:grid-cols-[minmax(64px,_1fr)_minmax(64px,_1fr)_minmax(64px,_100px)_minmax(64px,_100px)_minmax(64px,_100px)] gap-x-3 gap-y-2'"
            :headers="headers"
            :sort="sort" />
        </transition>
      </template>

      <template #body="{ template }">
        <UiTableGroup class="mt-2">
          <template #title>
            <content-loader
              viewBox="0 0 400 10"
              :speed="2"
              primaryColor="#f3f3f3"
              secondaryColor="#ecebeb">
              <rect
                x="0"
                y="0"
                rx="3"
                ry="3"
                width="50"
                height="10" />
            </content-loader>
          </template>
          <template
            v-for="index in 15"
            :key="index">
            <div
              class="grid p-4 items-center cursor-pointer gap-3 relative xl:grid-cols-[minmax(64px,_1fr)_minmax(64px,_1fr)_minmax(64px,_100px)_minmax(64px,_100px)_minmax(64px,_100px)] gap-x-3 gap-y-2">
              <div class="flex items-center justify-between order-1">
                <div class="flex items-center flex-nowrap relative">
                  <div
                    class="w-[48px] h-[48px] rounded-full bg-gray-50 flex items-center justify-center mr-4">
                    <Skeleton :options="{ width: 48, height: 48 }">
                      <template #loader>
                        <circle
                          cx="24"
                          cy="24"
                          r="24" />
                      </template>
                    </Skeleton>
                  </div>
                  <div
                    class="hidden xl:block text-lg font-semibold text-gray-900 w-[200px]">
                    <content-loader
                      viewBox="0 0 220 50"
                      :speed="2"
                      primaryColor="#f3f3f3"
                      secondaryColor="#ecebeb">
                      <rect
                        x="0"
                        y="12"
                        rx="8"
                        ry="8"
                        width="180"
                        height="24" />
                    </content-loader>
                  </div>
                  <div
                    class="block xl:hidden text-base font-semibold text-gray-900 w-[100px]">
                    <content-loader
                      viewBox="0 0 80 50"
                      :speed="2"
                      primaryColor="#f3f3f3"
                      secondaryColor="#ecebeb">
                      <rect
                        x="0"
                        y="16"
                        rx="8"
                        ry="8"
                        width="60"
                        height="20" />
                    </content-loader>
                  </div>
                </div>
                <div class="block xl:hidden w-[80px]">
                  <content-loader
                    viewBox="0 0 80 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="10"
                      rx="4"
                      ry="4"
                      width="70"
                      height="16" />
                    <rect
                      x="0"
                      y="32"
                      rx="4"
                      ry="4"
                      width="40"
                      height="12" />
                  </content-loader>
                </div>
              </div>
              <div class="order-2 xl:order-3 flex xl:hidden justify-between">
                <div class="block xl:hidden w-[60px]">
                  <content-loader
                    viewBox="0 0 60 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="11"
                      rx="8"
                      ry="8"
                      width="60"
                      height="20" />
                  </content-loader>
                </div>
                <div class="w-[200px]">
                  <content-loader
                    viewBox="0 0 220 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="12"
                      rx="8"
                      ry="8"
                      width="200"
                      height="24" />
                  </content-loader>
                </div>
              </div>
              <div class="order-3 xl:order-2 flex justify-between">
                <div class="block xl:hidden w-[60px]">
                  <content-loader
                    viewBox="0 0 60 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="11"
                      rx="8"
                      ry="8"
                      width="60"
                      height="20" />
                  </content-loader>
                </div>
                <div class="w-[200px]">
                  <content-loader
                    viewBox="0 0 220 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="12"
                      rx="8"
                      ry="8"
                      width="200"
                      height="24" />
                  </content-loader>
                </div>
              </div>
              <div class="order-4 xl:order-3 hidden xl:block">
                <content-loader
                  viewBox="0 0 80 50"
                  :speed="2"
                  primaryColor="#f3f3f3"
                  secondaryColor="#ecebeb">
                  <rect
                    x="0"
                    y="12"
                    rx="4"
                    ry="4"
                    width="60"
                    height="12" />
                  <rect
                    x="0"
                    y="30"
                    rx="4"
                    ry="4"
                    width="30"
                    height="8" />
                </content-loader>
              </div>
              <div class="order-5 flex justify-between">
                <div class="block xl:hidden w-[60px]">
                  <content-loader
                    viewBox="0 0 60 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="11"
                      rx="8"
                      ry="8"
                      width="60"
                      height="20" />
                  </content-loader>
                </div>
                <div class="block xl:hidden w-[100px]">
                  <content-loader
                    viewBox="0 0 80 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="6"
                      rx="6"
                      ry="6"
                      width="70"
                      height="18" />
                  </content-loader>
                </div>
                <div class="hidden xl:block w-[120px]">
                  <content-loader
                    viewBox="0 0 80 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="16"
                      rx="4"
                      ry="4"
                      width="50"
                      height="18" />
                  </content-loader>
                </div>
              </div>
              <div class="order-6 flex justify-between">
                <div class="block xl:hidden w-[100px]">
                  <content-loader
                    viewBox="0 0 80 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="12"
                      rx="6"
                      ry="6"
                      width="80"
                      height="18" />
                  </content-loader>
                </div>
                <div class="w-[90px]">
                  <content-loader
                    viewBox="0 0 90 50"
                    :speed="2"
                    primaryColor="#f3f3f3"
                    secondaryColor="#ecebeb">
                    <rect
                      x="0"
                      y="12"
                      rx="12"
                      ry="12"
                      width="80"
                      height="32" />
                  </content-loader>
                </div>
              </div>
            </div>
          </template>
        </UiTableGroup>
      </template>
    </UiTable>
  </div>
</template>

<script>
import UiTable from "~/components/ui/Table/Table";
import UiTableHeader from "~/components/ui/TableHeader/TableHeader";
import UiTableGroup from "~/components/ui/TableGroup/TableGroup";
import { ContentLoader } from "vue-content-loader";
import Skeleton from "@/components/ui/Skeleton/Skeleton";

export default {
  name: "HistoryView",
  components: {
    Skeleton,
    UiTable,
    UiTableHeader,
    UiTableGroup,
    ContentLoader,
  },
  props: {
    count: {
      type: Number,
      default: null,
    },
    showFilters: {
      type: Boolean,
      default: true,
    },
    headers: {
      type: Array,
      default: () => [],
    },
    sort: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    beforeLeave(el) {
      el.style.height = el.scrollHeight + "px";
    },
    leave(el) {
      el.style.height = "0";
    },
    beforeEnter(el) {
      el.style.height = "0";
    },
    enter(el) {
      el.style.height = el.scrollHeight + "px";
    },
  },
};
</script>

<style lang="scss" module>
.transaction {
  @apply grid xl:hidden;
}
</style>
