import { ref, computed } from "vue";
import type { TUserPersonalNotification } from "@/types/api/TUserPersonalNotification";
import {
  useUserPersonalNotificationGet,
  useUserPersonalNotificationOpenPost,
  useUserPersonalNotificationReadPost,
  useUserPersonalNotificationClosePost,
} from "@/composable/API";

/**
 * Get user personal notification &
 * Update notification status
 *
 * @property {Ref<TUserPersonalNotification | null>} personalNotification - Personal Notification Data
 * @property {ComputedRef<boolean>} isModalShowed - Shows \ hides personal notification modal
 * @property {Promise} setNotificationRead - Send POST request to mark notification as read
 * @property {Promise} onNotificationLinkOpen - Send POST request to mark notification as opened
 * @property {Promise} onClose - Send POST request to mark notification as closed
 *
 * @example
 * const { personalNotification } = usePersonalNotification();
 *
 * @category Composables
 */
export function usePersonalNotification() {
  const { data, isFetching } = useUserPersonalNotificationGet();

  const personalNotification = computed<TUserPersonalNotification | null>(
    () => {
      return data.value?.data ?? null;
    }
  );

  // Flag to close modal immediately without waiting backend response
  const isOpen = ref(true);

  const isModalShowed = computed<boolean>(() => {
    return isOpen.value && Boolean(personalNotification.value);
  });

  const setNotificationRead = async () => {
    if (!personalNotification.value) return;

    if (isModalShowed.value) {
      await useUserPersonalNotificationReadPost(personalNotification.value.id);
    }
  };

  const onNotificationLinkOpen = async () => {
    if (!personalNotification.value) return;

    await useUserPersonalNotificationOpenPost(personalNotification.value.id);
  };

  const onClose = async () => {
    if (!personalNotification.value) return;

    isOpen.value = false;
    await useUserPersonalNotificationClosePost(personalNotification.value.id);
  };

  return {
    personalNotification,
    isFetching,
    isModalShowed,
    setNotificationRead,
    onNotificationLinkOpen,
    onClose,
  };
}
