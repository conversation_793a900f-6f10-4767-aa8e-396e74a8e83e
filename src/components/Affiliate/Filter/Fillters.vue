<script setup lang="ts">
import PaymentsWrapper from "@/components/ui/Wrapper/PaymentsWrapper.vue";
import DatePicker from "@/components/ui/DatePicker/DatePicker.vue";
import SelectPromo from "@/components/Affiliate/Filter/SelectPromo.vue";
import SelectUsers from "@/components/Affiliate/Filter/SelectUsers.vue";

import type { TAffiliateFilters } from "@/components/Affiliate/types";
import type { TPromoCode } from "@/types/promoCode";

interface Props {
  listPromo: Array<TPromoCode>;
  filters: TAffiliateFilters;
}

const emit = defineEmits(["change", "reset"]);
const props = defineProps<Props>();

const setFilters = (
  field: keyof TAffiliateFilters,
  value: TAffiliateFilters[typeof field]
) => {
  emit("change", { ...props.filters, [field]: value });
};
</script>

<template>
  <PaymentsWrapper
    with-reset
    :title="$t('affiliate.statistic.filters')"
    @reset="emit('reset')">
    <div class="grid grid-cols-1 md:grid-cols-3 items-start gap-5">
      <!--
          -->
      <DatePicker
        :placeholder="'DD.MM.YYYY - DD.MM.YYYY'"
        :label="$t('payments.filters.filter.date')"
        label-class="text-sm text-neutral-500"
        is-range
        without-input
        :max-date="new Date()"
        :masks="{ input: 'DD.MM.YYYY' }"
        :value="filters.dates"
        @input="(value) => setFilters('dates', value)" />

      <SelectPromo
        :list="listPromo || []"
        :selected="filters.selectedPromo"
        @change="(value) => setFilters('selectedPromo', value)" />

      <SelectUsers
        :selected="filters.selectedUsers"
        @change="(value) => setFilters('selectedUsers', value)" />
    </div>
  </PaymentsWrapper>
</template>

<style scoped lang="scss"></style>
