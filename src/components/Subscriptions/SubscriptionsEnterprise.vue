<script lang="ts" setup>
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useSupportManager } from "@/composable";

const { telegramLink } = useSupportManager({
  privateManager: true,
  useTelegramPrivateLink: true,
});
</script>
<template>
  <div class="subscriptions-enterprise">
    <div class="container">
      <div class="description">
        <div class="title">Enterprise</div>
        <div class="text">
          {{ $t("pst-private.enterprise-special-subscription") }}
        </div>
      </div>
      <div class="telegram-button">
        <a
          :href="telegramLink"
          target="_blank">
          <UIButton
            class="w-full"
            color="telegram"
            size="m">
            <template #left>
              <DynamicIcon name="telegram24" />
            </template>
            {{ $t("pst-private.contact-us") }}
          </UIButton>
        </a>
      </div>
      <div class="separator-line"></div>
      <div class="advantages">
        <div class="deposit-fee">
          <div class="percentage">
            <span class="percentage-value">2% </span>
            <span class="percentage-text">
              {{ $t("pst-private.enterprise-depositFee") }}
            </span>
          </div>
          <div class="enterprise-users-only">
            {{ $t("pst-private.enterprise-users-only") }}
          </div>
        </div>
        <div class="advantages-list">
          <div class="advantage-item">
            <div class="">
              <DynamicIcon
                class="advantage-icon"
                name="check-circle-plain" />
            </div>
            <span>
              {{ $t("pst-private.enterprise-custom-cards-per-month") }}
            </span>
          </div>
          <div class="advantage-item">
            <DynamicIcon
              class="advantage-icon"
              name="check-circle-plain" />
            <span>{{ $t("pst-private.enterprise-custom-cashback-adv") }}</span>
          </div>
          <div class="advantage-item">
            <DynamicIcon
              class="advantage-icon"
              name="check-circle-plain" />
            <span>{{
              $t("pst-private.enterprise-custom-cashback-limit")
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.subscriptions-enterprise {
  .container {
    @apply grid rounded text-fg-contrast p-10;
    background: linear-gradient(
        98.03deg,
        rgba(255, 255, 255, 0.2) -11.68%,
        rgba(255, 255, 255, 0) 67.2%
      ),
      linear-gradient(
        0deg,
        var(--sys-fg-fg-primary, #15181e),
        var(--sys-fg-fg-primary, #15181e)
      );

    @screen md {
      @apply grid-rows-[auto_auto_auto] grid-cols-[auto_auto];
      grid-template-areas:
        "description telegram-button"
        "separator-line separator-line"
        "advantages advantages";
    }
    @apply grid-cols-1;
    grid-template-areas:
      "description "
      "separator-line"
      "advantages"
      "telegram-button";
  }

  .description {
    grid-area: description;

    .title {
      @apply text-h2 leading-11 font-extrabold mb-2;
    }

    .text {
      @apply text-h5 leading-6 font-medium text-fg-secondary;
    }
  }

  .telegram-button {
    @apply flex flex-row justify-end;
    a {
      @apply w-full min-w-[220px];
      @screen md {
        @apply w-auto;
      }
    }

    grid-area: telegram-button;
  }

  .separator-line {
    grid-area: separator-line;
    @apply bg-[rgba(255,255,255,0.12)] h-px;
    @apply my-6;
  }

  .advantages {
    @apply flex flex-col;
    grid-area: advantages;
    @screen md {
      @apply flex-row;
    }
    .deposit-fee {
      @apply w-full;
      @screen md {
        @apply w-1/2;
      }
      .percentage {
        @apply text-13 leading-15 mb-2.5;
        .percentage-value {
          @apply text-fg-orange font-extrabold;
        }
      }

      .enterprise-users-only {
        @apply text-4.5 leading-6;
      }
    }

    .advantages-list {
      @apply w-full py-2 space-y-5 mt-4 mb-6;
      @screen md {
        @apply w-1/2 mt-0 mb-0;
      }
      .advantage-item {
        @apply flex flex-row;
        span {
          @apply font-normal text-4.5 leading-6;
        }

        .advantage-icon {
          @apply text-fg-green w-6 h-6 mr-3;
        }
      }
    }
  }
}
</style>
