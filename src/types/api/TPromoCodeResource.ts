// type: discount-card
export type TDiscountCardCodeFields = {
  card_buy_discount_percent: string;
  card_bonus_amount: string;
};

// type: discount-subscription-purchase
export type TDiscountSubscriptionCodeFields = {
  subscription_purchase_discount_percent: string;
};

// type: free-extension
export type TFreeExtensionCodeFields = {
  extension_tariff_id: string;
  additional_slots_number: string;
};

// union of all types
export type TPromoCodeFields =
  | TDiscountCardCodeFields
  | TDiscountSubscriptionCodeFields
  | TFreeExtensionCodeFields;

export type TPromoCodeResource = {
  id: number;
  code: string;
  status: number | null;
  fields: TPromoCodeFields;
  created_at: string;
  stop_at: string | null;
};
