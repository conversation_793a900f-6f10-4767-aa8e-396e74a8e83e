<template>
  <div class="ReferralBanners">
    <div class="col-span-3 text-h5 font-extrabold">
      {{ $t("What we provide you") }}:
    </div>
    <swiper
      :space-between="20"
      :breakpoints="breakpoints"
      class="ReferralBanners__swiper h-full min-h-[285px]">
      <swiper-slide>
        <referral-banners-item
          class="ReferralBanners__swiper--slide swiper-slide">
          <template #title>
            <span class="text-h5 text-white">$75</span> {{ $t("per review") }}
          </template>
          <template #description>
            {{
              $t(
                "Up to $75 per review if you have a reputation on various resources"
              )
            }}
          </template>
          <template #illustration>
            <div
              class="absolute right-0 bottom-0 w-[155px] -mr-[30px] -mb-[60px]">
              <img src="@/assets/img/referral-banner-4.png" />
            </div>
          </template>
        </referral-banners-item>
      </swiper-slide>

      <swiper-slide>
        <referral-banners-item
          class="ReferralBanners__swiper--slide swiper-slide">
          <template #title>
            <span class="text-h5 text-white">$250</span>
            {{ $t("per publication") }}
          </template>
          <template #description>
            {{ $t("Up to $250 for posting in a community or YouTube") }}
          </template>
          <template #illustration>
            <div class="absolute right-0 bottom-0 w-[140px] -mr-5 -mb-3">
              <img src="@/assets/img/referral-banner-5.png" />
            </div>
          </template>
        </referral-banners-item>
      </swiper-slide>

      <swiper-slide>
        <referral-banners-item
          class="ReferralBanners__swiper--slide swiper-slide">
          <template #title>
            {{ $t("Receive") }} <span class="text-h5 text-white">50%</span>
            {{ $t("cashback") }}
          </template>
          <template #description>
            {{ $t("Receive 50% cashback on the first issued card") }}
          </template>
          <template #illustration>
            <div
              class="absolute right-0 bottom-0 w-[227px] rotate-[6.7deg] -mr-[66px] -mb-8">
              <img src="@/assets/img/referral-banner-1.png" />
            </div>
          </template>
        </referral-banners-item>
      </swiper-slide>

      <!--      <swiper-slide>-->
      <!--        <referral-banners-item-->
      <!--            class="ReferralBanners__swiper&#45;&#45;slide swiper-slide"-->
      <!--        >-->
      <!--          <template #title>-->
      <!--            {{$t('From')}} <span class="text-h5 text-white">$5</span> {{$t('per issue')}}-->
      <!--          </template>-->
      <!--          <template #description>-->
      <!--            {{$t('We pay from $5 for the first issued card on the service')}}-->
      <!--          </template>-->
      <!--          <template #illustration>-->
      <!--            <div class="absolute right-0 bottom-0 w-[201px] -mr-24 -mb-14">-->
      <!--              <img src="@/assets/img/referral-banner-2.png"/>-->
      <!--            </div>-->
      <!--          </template>-->
      <!--        </referral-banners-item>-->
      <!--      </swiper-slide>-->

      <swiper-slide>
        <referral-banners-item
          class="ReferralBanners__swiper--slide swiper-slide">
          <template #title>
            <span class="text-h5 text-white">10%</span> {{ $t("per transfer") }}
          </template>
          <template #description>
            {{ $t("We pay 10% from each transaction fee") }}
          </template>
          <template #illustration>
            <div class="absolute right-0 bottom-0 w-[182px] -mr-16 -mb-12">
              <img src="@/assets/img/referral-banner-3.png" />
            </div>
          </template>
        </referral-banners-item>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
import "swiper/css/free-mode";
import ReferralBannersItem from "@/components/Referral/ReferralBannersItem";

export default {
  name: "ReferralBanners",
  components: { ReferralBannersItem, Swiper, SwiperSlide },
  data() {
    return {
      breakpoints: {
        1280: {
          slidesPerView: 4,
        },
        320: {
          freeMode: true,
          slidesPerView: "auto",
        },
      },
    };
  },
};
</script>

<style lang="scss" scoped>
.ReferralBanners {
  @apply flex flex-col gap-3 lg:w-full;

  &__swiper {
    @apply px-6 -ml-6;
    width: calc(100% + 3em);

    @screen lg {
      @apply px-10 -ml-10;
      width: calc(100% + 5em);
    }

    .swiper-slide {
      @apply w-auto;
    }

    &--slide {
      @apply max-w-[262px] xl:max-w-[400px];
    }
  }
}
</style>
