<script setup lang="ts">
import UiButton from "@/components/ui/Button/Button.vue";
import AddMember from "@/components/Teams/Modals/AddMemberNew/AddMemberNew.vue";
import { ref } from "vue";
import UiTransition from "@/components/ui/UITransition.vue";

const showModal = ref<boolean>(false);
const toggleModal = () => {
  showModal.value = !showModal.value;
};
</script>
<template>
  <div>
    <div :class="$style.root">
      <h4>{{ $t("team.notFoundTitle") }}</h4>
      <UiButton
        :title="$t('btn.addTeamMember')"
        icon="add"
        :class="$style.button"
        @click="toggleModal" />
    </div>
    <!--  Modals  -->
    <Teleport to="#modals">
      <UiTransition :name="'fade-slide-up'">
        <AddMember
          v-if="showModal"
          @close="toggleModal" />
      </UiTransition>
    </Teleport>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply flex flex-col items-center gap-5 w-fit;

  & h4 {
    @apply text-h4 font-semibold;
  }
}

.button {
  @apply w-fit;
  @apply pl-3 pr-4 py-[10px] text-[15px] font-normal;

  & svg {
    @apply w-6 h-6;
  }
}
</style>
