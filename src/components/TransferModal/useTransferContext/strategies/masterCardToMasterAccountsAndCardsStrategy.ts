import { ref } from "vue";
import {
  defaultState,
  type TransferFormState,
  type TransferStrategy,
  type TransferStrategyInitParams,
} from "@/components/TransferModal/types";
import { useStrategyFactory } from "@/components/TransferModal/useTransferContext/factory/useStrategyFactory";

/*
 * Strategy for transfer from master card to master accounts and cards.
 */
export const masterCardToMasterAccountsAndCardsStrategy: TransferStrategy = (
  params: TransferStrategyInitParams
) => {
  const state = ref<TransferFormState>({
    ...defaultState,
  });

  const { initialize, searchFrom, searchTo, loadMoreFrom, loadMoreTo } =
    useStrategyFactory(
      {
        fromWhom: "user",
        fromWhat: "card",
        toWhom: "user",
        toWhat: "both",
      },
      state,
      params
    );

  initialize();

  return {
    state,
    searchFrom,
    searchTo,
    loadMoreFrom,
    loadMoreTo,
  };
};
