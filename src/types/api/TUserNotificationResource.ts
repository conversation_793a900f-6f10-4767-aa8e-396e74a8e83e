export type TUserNotificationResource = {
  notification: {
    id: number;
    notify_type_id: number;
    text: string;
    priority: string;
    buttons: string[] | null;
    read_at: string;
    created_at: string;
  };
  user: {
    uuid: string;
  };
  meta: {
    card_id: string;
    account_id: string;
    amount: string;
    link: string;
  };
};

export type TUserNotificationPayload = {
  page?: number;
  priority?: string;
};
