<script setup lang="ts">
import { watch } from "vue";
import UITransition from "@/components/ui/UITransition.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useUserStore } from "@/stores/user";
import { storeToRefs } from "pinia";
import SupportTrigger from "@/components/SupportTrigger.vue";
import { usePersonalNotification } from "./usePersonalNotification";

const { user } = storeToRefs(useUserStore());
const {
  personalNotification,
  isModalShowed,
  isFetching,
  setNotificationRead,
  onNotificationLinkOpen,
  onClose,
} = usePersonalNotification();

watch(
  () => isFetching.value,
  (value) => {
    if (!value && isModalShowed.value) {
      // Set POST 'read' if fetching of personal notification ends
      // & modal showed for user
      setNotificationRead();
    }
  }
);
</script>

<template>
  <UITransition>
    <div
      v-if="isModalShowed"
      class="notification-modal">
      <div class="head">
        <SupportTrigger>
          <template #button>
            <UIButton
              icon-only
              shape="pill"
              class="button black">
              <DynamicIcon
                name="headphone"
                class="text-fg-contrast" />
            </UIButton>
          </template>
        </SupportTrigger>
        <UIButton
          icon-only
          shape="pill"
          class="button black"
          @click="onClose">
          <DynamicIcon
            name="close"
            class="text-fg-contrast" />
        </UIButton>
      </div>
      <div class="content">
        <div class="content__image">
          <img
            src="@/assets/img/personal-offer-image.png"
            alt="gift" />
        </div>
        <div class="content__tag-wrapper">
          <div class="content__tag">
            {{ $t("notifications.personal.tag") }} {{ user.email }}
          </div>
        </div>
        <div class="content__title">
          {{ personalNotification?.title }}
        </div>
        <div class="content__text">
          {{ personalNotification?.message }}
        </div>
        <a
          class="content__button"
          :href="personalNotification?.link"
          target="_blank"
          @click="onNotificationLinkOpen">
          <UIButton
            class="w-full"
            color="telegram">
            <template #left>
              <DynamicIcon name="telegram24" />
            </template>
            {{ $t("notifications.personal.tg-btn") }}
          </UIButton>
        </a>
      </div>
    </div>
  </UITransition>
</template>

<style lang="scss" scoped>
.notification-modal {
  @apply absolute z-6 top-0 right-0 w-screen h-screen flex flex-col justify-center items-center px-4 py-5 bg-black;

  // Add selector weight for overrite uiButton styles
  .head {
    .button.black {
      @apply bg-bg-contrast hover:bg-bg-contrast-hover active:bg-bg-contrast-clicked;
    }
  }
}

.head {
  @apply absolute top-6 right-6 flex items-center gap-4;
}

.content {
  // Use negative margin for correct content centered
  // 'cause image already has negative margin
  @apply flex flex-col items-center max-w-[440px] -mt-12;

  // Use negative margin for pixel-perfect without 'position: absolute'
  &__image {
    @apply -mb-5;
  }

  &__tag-wrapper {
    @apply p-0.5 rounded-[40px];
    background: linear-gradient(
      180deg,
      rgba(208, 208, 207, 1) 0%,
      rgba(44, 44, 44, 1) 100%
    );
  }

  &__tag {
    @apply px-4 py-2.5 rounded-[60px] bg-black text-4 leading-5 text-fg-contrast;
  }

  &__title {
    @apply mt-7 text-fg-contrast text-12 leading-12 font-black text-center;
  }

  &__text {
    @apply mt-3 text-fg-tertiary text-4.5 leading-6 text-center;
  }

  &__button {
    @apply mt-10;
    min-width: 200px;
  }
}
</style>
