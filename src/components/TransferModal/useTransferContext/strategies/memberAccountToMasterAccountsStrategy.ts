import { ref } from "vue";
import {
  defaultState,
  type TransferFormState,
  type TransferStrategy,
  type TransferStrategyInitParams,
} from "@/components/TransferModal/types";
import { useStrategyFactory } from "@/components/TransferModal/useTransferContext/factory/useStrategyFactory";

/*
 * Strategy for transfer from member account to master accounts.
 */
export const memberAccountToMasterAccountsStrategy: TransferStrategy = (
  params: TransferStrategyInitParams
) => {
  const state = ref<TransferFormState>({
    ...defaultState,
  });

  const { initialize, searchFrom, searchTo, loadMoreFrom, loadMoreTo } =
    useStrategyFactory(
      {
        fromWhom: "member",
        fromWhat: "account",
        toWhom: "user",
        toWhat: "accounts",
      },
      state,
      params
    );

  initialize();

  return {
    state,
    searchFrom,
    searchTo,
    loadMoreFrom,
    loadMoreTo,
  };
};
