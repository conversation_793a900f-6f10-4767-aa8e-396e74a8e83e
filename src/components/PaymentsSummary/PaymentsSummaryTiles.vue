<script setup lang="ts">
import { Skeletor } from "vue-skeletor";
import { computed } from "vue";
import { prepareAccountBalance } from "@/helpers/account";
import type { TTransactionSummary } from "@/types/api/TTransactionSummaryResource";
import { useUserStore } from "@/stores/user";
import { isMobile } from "@/helpers";
import { useSubscriptionsInfo } from "@/composable";

const props = defineProps<{
  isLoading?: boolean;
  summary: TTransactionSummary | null;
  isProMode?: boolean;
}>();

const userStore = useUserStore();
const { subscriptionsStatus } = useSubscriptionsInfo();

const gridClass = computed(() => {
  if (isMobile.value) return "grid-flow-row";

  if (isCashbackVisible.value) {
    return props.isProMode
      ? "grid-cols-2 md:grid-cols-3 xl:grid-cols-6"
      : "grid-cols-2 md:grid-cols-4";
  }

  return props.isProMode
    ? "grid-cols-2 md:grid-cols-3 xl:grid-cols-5"
    : "grid-cols-2 md:grid-cols-3";
});

const successSummary = computed<number>(() => {
  return props.isProMode
    ? props.summary?.approved_sum ?? 0
    : (props.summary?.approved_sum ?? 0) + (props.summary?.pending_sum ?? 0);
});

const skeletorCountBlocks = computed(() => {
  if (isCashbackVisible.value) {
    return props.isProMode ? 6 : 4;
  }

  return props.isProMode ? 5 : 3;
});

const isCashbackVisible = computed(() => {
  return subscriptionsStatus.value && !userStore.isTeamMember;
});
</script>

<template>
  <div
    class="payments-summary-tiles"
    :class="gridClass">
    <template v-if="isLoading">
      <Skeletor
        v-for="index in skeletorCountBlocks"
        :key="index"
        width="100%"
        height="80"
        class="rounded" />
    </template>

    <template v-else>
      <div class="payments-summary-item">
        <span class="text-fg-green"> Success </span>
        <span class="text-fg-primary font-semibold">
          {{ prepareAccountBalance(successSummary, "USD") }} $
        </span>
      </div>

      <div
        v-if="isProMode"
        class="payments-summary-item">
        <span class="text-fg-yellow"> Hold </span>
        <span class="text-fg-primary font-semibold">
          {{ prepareAccountBalance(summary?.pending_sum ?? 0, "USD") }} $
        </span>
      </div>

      <div class="payments-summary-item">
        <span class="text-fg-red"> Declined </span>
        <span class="text-fg-primary font-semibold">
          {{ prepareAccountBalance(summary?.declined_sum ?? 0, "USD") }} $
        </span>
      </div>

      <div
        v-if="isProMode"
        class="payments-summary-item">
        <span class="text-fg-secondary"> Canceled </span>
        <span class="text-fg-primary font-semibold">
          {{ prepareAccountBalance(summary?.cancelled_sum ?? 0, "USD") }} $
        </span>
      </div>

      <div class="payments-summary-item">
        <span class="text-fg-secondary"> Refund </span>
        <span class="text-fg-primary font-semibold">
          {{ prepareAccountBalance(summary?.refund_sum ?? 0, "USD") }} $
        </span>
      </div>

      <div
        v-if="isCashbackVisible"
        class="payments-summary-item">
        <span class="text-fg-purple"> Cashback </span>
        <span class="text-fg-primary font-semibold">
          {{ prepareAccountBalance(summary?.cashback_sum ?? 0, "USD") }} $
        </span>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.payments-summary-tiles {
  @apply w-full grid gap-3;
}
.payments-summary-item {
  @apply rounded p-4 flex flex-col bg-bg-level-1;
}
</style>
