<script setup lang="ts">
import UiTransition from "@/components/ui/UITransition.vue";
import type { TTabs } from "./types";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import AddMember from "@/components/Teams/Modals/AddMemberNew/AddMemberNew.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";

const { t } = useI18n();
const currentIdx = ref(0);

const emit = defineEmits(["changeTab"]);

interface ITabItem {
  id: TTabs;
  title: string;
}

const tabs: ITabItem[] = [
  {
    id: "MembersTable",
    title: t("membersV2.header.tabs.team"),
  },
  {
    id: "MembersTableApproved",
    title: t("membersV2.header.tabs.invitation"),
  },
];

function headerItemHandler(item: ITabItem, idx: number) {
  currentIdx.value = idx;
  emit("changeTab", item.id);
}

const isShowAddMember = ref(false);
</script>
<template>
  <div
    class="flex flex-col md:flex-row justify-between items-start md:items-center">
    <h2 class="font-medium text-h5">{{ $t("membersHeader.title") }}</h2>
    <div class="flex flex-col md:flex-row md:items-start gap-2">
      <div class="flex flex-col items-start">
        <div :class="$style.header">
          <div
            v-for="(item, index) in tabs"
            :class="[$style.item, index === currentIdx ? $style.active : null]"
            :key="index"
            @click="headerItemHandler(item, index)">
            {{ item.title }}
          </div>
        </div>
      </div>

      <UIButton
        size="s"
        color="black"
        class="w-full md:w-[170px]"
        @click="isShowAddMember = true">
        <template #left>
          <DynamicIcon name="user_outlined_24" />
        </template>
        {{ $t("membersV2.header.btnInvite") }}
      </UIButton>
    </div>
  </div>

  <Teleport to="body">
    <UiTransition :name="'ui-fade-up'">
      <AddMember
        v-if="isShowAddMember"
        @close="isShowAddMember = false" />
    </UiTransition>
  </Teleport>
</template>
<style module lang="scss">
.header {
  @apply border border-bg-level-1 rounded flex md:inline-flex text-fg-secondary;
}
.item {
  @apply cursor-pointer transition-colors flex items-center justify-center w-1/2 md:w-auto
  text-fg-secondary h-8 px-3;
}

.item.active {
  @apply bg-bg-level-1 transition-colors text-fg-primary;
}
</style>
