<script setup lang="ts">
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import apiIcon from "./assets/api.svg";
import cardsIcon from "./assets/cards.svg";
import cashbackIcon from "./assets/cashback.svg";
import freeIcon from "./assets/free.svg";
import teamIcon from "./assets/team.svg";
import flashIcon from "./assets/flash.svg";
import { useSupportLink } from "@/composable";

const icons: Record<string, unknown> = {
  api: apiIcon,
  cards: cardsIcon,
  cashback: cashbackIcon,
  free: freeIcon,
  team: teamIcon,
  flash: flashIcon,
};

const { t } = useI18n();

const { telegramComplianceLink } = useSupportLink();

const advantages = computed(() => [
  {
    icon: "api",
    text: t("kyc.individual-banner.advantages.api"),
  },
  {
    icon: "team",
    text: t("kyc.individual-banner.advantages.team"),
  },
  {
    icon: "cashback",
    text: t("kyc.individual-banner.advantages.cashback"),
  },
  {
    icon: "free",
    text: t("kyc.individual-banner.advantages.free"),
  },
  {
    icon: "cards",
    text: t("kyc.individual-banner.advantages.cards"),
  },
  {
    icon: "flash",
    text: t("kyc.individual-banner.advantages.flash"),
  },
]);
</script>

<template>
  <div class="individual-banner wrapper">
    <div class="digital-banner common-banner">
      <div class="badge-wrapper">
        <div class="business-badge">
          {{ $t("kyc.individual-banner.badge") }}
        </div>
      </div>
      <div>
        <h4 class="title">{{ $t("kyc.individual-banner.title") }}</h4>
        <div class="digital-text">
          {{ $t("kyc.individual-banner.text") }}
        </div>
        <div>
          <a
            :href="telegramComplianceLink"
            target="_blank">
            <UIButton
              color="telegram"
              class="digital-button">
              <template #left>
                <DynamicIcon name="telegram24" />
              </template>
              Telegram
            </UIButton>
          </a>
          <span class="support-time">
            <DynamicIcon
              name="clock"
              class="clock-icon" />
            {{ $t("verification.twoStepVerificationView.col-2.support.hours") }}
          </span>
        </div>
      </div>
    </div>
    <div class="advantages-banner common-banner">
      <div class="advantages">
        <div
          v-for="item in advantages"
          :key="item.icon"
          class="advantages-item">
          <div class="advantages-item-icon">
            <component :is="icons[item.icon]" />
          </div>
          <span class="advantages-item-text">{{ item.text }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.wrapper {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fill, minmax(23.75rem, 1fr));
}

.common-banner {
  @apply p-10 rounded-small bg-fg-primary;
  background-image: linear-gradient(
    -44deg,
    rgba(255, 255, 255, 0.08) 0,
    transparent 100%
  );
}

.digital-text {
  @apply text-4 leading-5 text-fg-tertiary font-normal pt-2.5 pb-10;
}

.digital-banner {
  @apply flex flex-col   relative overflow-hidden;
  &:after {
    @apply content-[''] block absolute top-[-8rem] right-[-8rem]
    bg-[url("./assets/snowflake.svg")] bg-no-repeat bg-contain bg-center w-[20rem] h-[20rem];
  }
}

.digital-button {
  @apply max-w-[15.8125rem] w-full;
}

.title {
  @apply text-white text-11 font-[720] leading-12 whitespace-pre-line;
}

.badge-wrapper {
  @apply w-full pb-[118px];
}

.business-badge {
  @apply py-2 px-3 bg-fg-purple inline-flex text-center text-white rounded text-4 leading-5;
}

.advantages {
  @apply space-y-9 flex flex-col;
}

.advantages-banner {
  @apply relative;
  &:after {
    @apply content-[''] block absolute bottom-0 right-0
    bg-[url("./assets/triangle.svg")] bg-no-repeat bg-contain bg-center w-[12.5rem] h-[12.5rem];
  }
}

.advantages-item {
  @apply flex items-center gap-2;
}

.advantages-item-icon {
  @apply w-9 h-9 mr-2.5 bg-white rounded-full flex items-center justify-center;
}

.advantages-item-text {
  @apply text-white text-4 flex-1 whitespace-pre-line leading-5;
}

.support-time {
  @apply text-fg-secondary text-3.5 leading-4 inline-flex items-center mt-2;
}

.clock-icon {
  @apply w-3 h-3 mr-1.5;
}
</style>
