import { useUserStore } from "@/stores/user";
import { RouteName } from "@/constants/route_name";
import { useCountries, useExperiments } from "@/composable";
import { useSessionStorage } from "@vueuse/core";
import { SessionStorageKey } from "@/constants/session_storage_key";

const { getGeoBlockValue } = useExperiments();
const excludedRouteNames = [
  RouteName.LOGIN,
  RouteName.AUTH,
  RouteName.CONFIRM_TELEGRAM,
  RouteName.OAUTH2,
  RouteName.NOT_FOUND,
  RouteName.REGISTER,
  RouteName.RESET_PASSWORD,
  RouteName.UNAVAILABLE_COUNTRIES,
  RouteName.PAY_DEMO,
  RouteName.CONFIRM_2FA_SOCIAL,
];
const wasGeoBlockShown = useSessionStorage(
  SessionStorageKey.WAS_GEO_BLOCK_SHOWN,
  false
);

export default async (to: any) => {
  if (!excludedRouteNames.includes(to.name)) {
    if (!wasGeoBlockShown.value) {
      const { getUserCountryCode } = useUserStore();
      const { blockedCountriesCodes } = useCountries();
      const countryCode: string | undefined = await getUserCountryCode();
      if (countryCode && blockedCountriesCodes.has(countryCode)) {
        const geoBlockValue = await getGeoBlockValue();
        if (geoBlockValue) {
          return { name: RouteName.UNAVAILABLE_COUNTRIES };
        }
      }
    }
  }
};
