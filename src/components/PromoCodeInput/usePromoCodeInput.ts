import { computed, ref } from "vue";
import type {
  TDiscountCardCodeFields,
  TDiscountSubscriptionCodeFields,
  TFreeExtensionCodeFields,
  TPromoCodeFields,
  TPromoCodeResource,
} from "@/types/api/TPromoCodeResource";
import { usePromoCodeCheckGet } from "@/composable/API/usePromoCodeCheckGet";
import { usePromoCodeAttachedListGet } from "@/composable/API/usePromoCodeAttachedListGet";

export type TPromoCodeMode = "card" | "subscription";

export type TPromoCodeType =
  | "no_action"
  | "card_discount"
  | "card_bonus"
  | "subscription_discount"
  | "extension";

export type TUIPromoCode = {
  mode: TPromoCodeMode;
  promocode_type: TPromoCodeType;
  promocode_value: number;
  enabled: boolean;
  code: string;
  stop_at: string | null;
  status: number | null;
  id: number;
  resource: TPromoCodeResource;
};

const getBenefit = (
  promoCode: TPromoCodeResource
): [TPromoCodeType, number] => {
  const { fields } = promoCode;

  if (isCardCodeResource(fields)) {
    const discountPercent = Number(fields.card_buy_discount_percent);
    const bonusAmount = Number(fields.card_bonus_amount);
    return discountPercent > 0
      ? ["card_discount", discountPercent]
      : ["card_bonus", bonusAmount];
  }

  if (isSubscriptionCodeResource(fields)) {
    const discountPercent = Number(
      fields.subscription_purchase_discount_percent
    );
    return ["subscription_discount", discountPercent];
  }

  if (isFreeExtensionCodeResource(fields)) {
    const slotsNumber = Number(fields.additional_slots_number) || 0;
    return ["extension", slotsNumber];
  }

  return ["no_action", 0];
};

const toUIPromoCode = (promoCode: TPromoCodeResource): TUIPromoCode => {
  const mode = isCardCodeResource(promoCode.fields) ? "card" : "subscription";
  const [promocode_type, promocode_value] = getBenefit(promoCode);

  const uiPromoCode: TUIPromoCode = {
    mode: mode,
    promocode_type: promocode_type,
    promocode_value: promocode_value,
    enabled: false,
    code: promoCode.code,
    stop_at: promoCode.stop_at,
    status: promoCode.status,
    id: promoCode.id,
    resource: promoCode,
  };
  return uiPromoCode;
};

export const isCardCodeResource = (
  fields: TPromoCodeFields
): fields is TDiscountCardCodeFields => {
  return "card_buy_discount_percent" in fields || "card_bonus_amount" in fields;
};

export const isSubscriptionCodeResource = (
  fields: TPromoCodeFields
): fields is TDiscountSubscriptionCodeFields => {
  return "subscription_purchase_discount_percent" in fields;
};

export const isFreeExtensionCodeResource = (
  fields: TPromoCodeFields
): fields is TFreeExtensionCodeFields => {
  return "extension_tariff_id" in fields && "additional_slots_number" in fields;
};

const isValidPromoCodeMode = (
  mode: TPromoCodeMode,
  fields: TPromoCodeFields
) => {
  if (mode === "card") {
    return isCardCodeResource(fields);
  }

  return (
    isSubscriptionCodeResource(fields) || isFreeExtensionCodeResource(fields)
  );
};

export const usePromoCodeInput = (mode: TPromoCodeMode = "card") => {
  const activePromoCode = ref("");
  const isFetching = ref(false);
  const hasError = ref<boolean>(false);
  const errorMsg = ref<string>("");
  const promoCodeLocalList = ref<TUIPromoCode[]>([]);
  const promoCodeAttachedList = ref<TPromoCodeResource[]>([]);

  const activePromoCodeData = computed(() => {
    return promoCodeLocalList.value.find(
      (item) => item.code === activePromoCode.value
    );
  });

  const clearErrors = () => {
    errorMsg.value = "";
    hasError.value = false;
  };

  const checkPromoCodeInLocalList = (code: string): boolean => {
    return !!promoCodeLocalList.value.find((item) => item.code === code);
  };

  const checkPromoCodeInLAttachList = (
    code: string
  ): TPromoCodeResource | undefined => {
    return promoCodeAttachedList.value.find((item) => item.code === code);
  };

  const addPromoCodeToLocalList = (promoCode: TPromoCodeResource) => {
    if (checkPromoCodeInLocalList(promoCode.code)) {
      return;
    }

    promoCodeLocalList.value.unshift(toUIPromoCode(promoCode));

    promoCodeLocalList.value.forEach(
      (item) => (item.enabled = item.code === promoCode.code)
    );

    activePromoCode.value = promoCode.code;
    clearErrors();
  };

  const getPromoCodeAttachedList = async () => {
    const { data } = await usePromoCodeAttachedListGet();

    if (!data.value?.data) {
      hasError.value = true;
      return;
    }

    promoCodeAttachedList.value = data.value.data;
  };

  const checkPromoCode = async (code: string) => {
    isFetching.value = true;

    await getPromoCodeAttachedList();

    const promoCodeInAttachedList = checkPromoCodeInLAttachList(code);

    if (promoCodeInAttachedList && promoCodeInAttachedList.status === 0) {
      addPromoCodeToLocalList(promoCodeInAttachedList);
      isFetching.value = false;
      return;
    }

    const { data } = await usePromoCodeCheckGet(code);

    if (!data.value?.data) {
      errorMsg.value = data.value?.message
        ? data.value?.message
        : "Unknown Error";
      hasError.value = true;
      isFetching.value = false;
      return;
    }

    if (!data.value?.success) {
      errorMsg.value = "Unknown Error";
      hasError.value = true;
      isFetching.value = false;
      return;
    }

    if (!isValidPromoCodeMode(mode, data.value.data.fields)) {
      errorMsg.value = "invalid_promo_code_type";
      hasError.value = true;
      isFetching.value = false;
      return;
    }

    addPromoCodeToLocalList(data.value.data);
    isFetching.value = false;
  };

  return {
    activePromoCode,
    activePromoCodeData,
    isFetching,
    promoCodeLocalList,
    hasError,
    errorMsg,
    checkPromoCode,
    clearErrors,
  };
};
