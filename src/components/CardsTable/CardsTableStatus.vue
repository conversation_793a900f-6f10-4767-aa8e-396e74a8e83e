<script lang="ts" setup>
import { computed } from "vue";
import { getCardSimpleStatus } from "@/components/CardsTable/cardsTableUtils";
import { isCardBinDeprecated } from "@/helpers/isCardBinDeprecated";
import type { TCardResource } from "@/types/api/TCardResource";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

const props = defineProps<{
  card: TCardResource;
}>();

const cardIsBlocked = computed(() => {
  return props.card.status !== 1;
});

const cardIsUnderMaintenance = computed(() => {
  return props.card.under_maintenance;
});
</script>

<template>
  <span
    v-if="card.card_message"
    :style="[
      card.card_message?.text_color
        ? {
            color: card.card_message?.text_color,
          }
        : {},
    ]"
    >{{ $t(`card-messages.${card.card_message.label}`) }}
  </span>
  <span
    v-else-if="cardIsBlocked"
    class="text-fg-red">
    {{ $t("Card blocked") }}
  </span>
  <span
    v-else-if="cardIsUnderMaintenance"
    class="text-fg-orange">
    <VTooltip
      class="flex items-center grow-0"
      placement="bottom">
      <span class="text-3.5 text-fg-orange">{{
        $t("Technical maintenance")
      }}</span>
      <template #popper>
        <div class="max-w-[260px]">
          <p class="text-lg">{{ $t("common.technical-work.title") }}</p>
          <p class="text-3.5 text-fg-tertiary font-normal leading-4 my-2">
            {{ $t("common.technical-work.descriptions") }}
          </p>
        </div>
      </template>
    </VTooltip>
  </span>
  <span
    v-else
    :class="{
      'text-fg-green': card.simple_status === 'active',
      'text-fg-red': card.simple_status === 'inactive',
    }">
    {{ getCardSimpleStatus(card.simple_status) }}
  </span>
</template>
