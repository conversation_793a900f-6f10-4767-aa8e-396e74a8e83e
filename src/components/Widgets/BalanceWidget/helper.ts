import { computed, ref } from "vue";
import { balanceWithSymbol } from "@/helpers/account";
import { useRemoteStorageStore } from "@/stores/remoteStorage";
import { useUserStore } from "@/stores/user";
import {
  TransactionsApiService,
  type ITransactionResponse,
} from "@modules/services/transactions";

export class BalanceWidgetHelper {
  summary = ref<ITransactionResponse["summary"] | null>(null);
  remoteStorageStore = useRemoteStorageStore();
  userStore = useUserStore();
  #transactionService = new TransactionsApiService();

  constructor() {
    this.#getSummary();
  }

  currentBalance(v: number) {
    return balanceWithSymbol(
      v,
      {
        symbol: "$",
        iso_code: "USD",
      },
      6,
      "right"
    );
  }

  get accountsBalance() {
    return this.currentBalance(
      Number(this.userStore.user.summary?.accounts_balance_active!)
    );
  }

  get cardsBalance() {
    return this.currentBalance(
      Number(this.userStore.user.summary?.cards_balance_active!)
    );
  }

  get pendingBalance() {
    return computed(() => {
      return this.currentBalance(Number(this.summary.value?.pending_sum));
    });
  }

  get totalBalance() {
    return computed(() => {
      return this.currentBalance(
        Number(this.userStore.user.summary?.cards_balance_active) +
          Number(this.userStore.user.summary?.accounts_balance_active)
      );
    });
  }

  async #getSummary() {
    let res;
    if (this.userStore.isTeamOwner) {
      res = await this.#transactionService.getMembersSummary();
    } else {
      res = await this.#transactionService.getSummary();
    }

    if (!res.status) {
      return;
    }
    this.summary.value = res.data!;
  }
}
