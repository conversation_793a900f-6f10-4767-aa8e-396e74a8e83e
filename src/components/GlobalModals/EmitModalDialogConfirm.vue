<template>
  <!-- EmitModals->DialogConfirm -->
  <UIModal
    :is-open="isOpen"
    :title="title"
    @close="close">
    <template
      v-if="isOpen"
      #content>
      <div class="flex flex-auto text-4 leading-5 text-fg-primary py-4 mb-2">
        {{ text }}
      </div>
      <div class="flex flex-none flex-col">
        <UIButton
          color="black"
          class="w-full"
          @click="success">
          {{
            confirmButtonText ||
            $t("subscriptions.page.dialogConfirm.confirmButtonTextDefault")
          }}
        </UIButton>
      </div>
    </template>
  </UIModal>
</template>

<script setup lang="ts">
import UIModal from "@/components/ui/UIModal/UIModal.vue";
import eventEmitter from "@/services/EventEmitter";
import { ref } from "vue";
import { EmitEventsNames } from "@/constants/emit_events_names";
import UIButton from "@/components/ui/UIButton/UIButton.vue";

const isOpen = ref<boolean>(false);

const text = ref("");
const title = ref("");
const confirmButtonText = ref("");
let onSuccess = () => {};
let onClose = () => {};

interface IDataEmit {
  title: string;
  text: string;
  confirmButtonText?: string;
  onClose: () => {};
  onSuccess: () => {};
}

const close = () => {
  isOpen.value = false;
  if (onClose) onClose();
  reset();
};

const success = () => {
  isOpen.value = false;
  if (onSuccess) onSuccess();
  reset();
};

const reset = () => {
  text.value = "";
  title.value = "";
  confirmButtonText.value = "";
  onSuccess = () => {};
  onClose = () => {};
};

eventEmitter.on(
  EmitEventsNames.MODALS_DIALOG_CONFIRM,
  async (data: IDataEmit) => {
    reset();

    if (data?.onClose) {
      onClose = data?.onClose;
    }

    if (data?.onSuccess) {
      onSuccess = data?.onSuccess;
    }

    if (data?.title) {
      title.value = data?.title;
    }

    if (data?.text) {
      text.value = data?.text;
    }

    if (data?.confirmButtonText) {
      confirmButtonText.value = data?.confirmButtonText;
    }

    isOpen.value = true;
  }
);
</script>
<style lang="scss" scoped></style>
