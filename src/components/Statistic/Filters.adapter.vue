<template>
  <Filters
    :full="full"
    :dates="filters.dates"
    :period="filters.period"
    :currency="filters.currency"
    :members="members"
    :member="filters.member"
    @update:period="updatePeriod"
    @update:dates="updateDates"
    @update:member="updateMember" />
</template>

<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import type { TFilters } from "@/components/Statistic/types";
import { reactive, watch, computed } from "vue";
import { useUserStore } from "@/stores/user";
import { useMembers } from "@/composable/Team/TeamBuilding";

interface Props {
  filters?: TFilters;
  full?: boolean;
}

// data
const { t } = useI18n();
const { isTeamOwner } = useUserStore();
const emit = defineEmits(["update:filters"]);
let membersQuery: any = null;
const props = defineProps<Props>();
const filters = reactive<TFilters>({
  dates: {
    start: undefined,
    end: undefined,
  },
  period: "month",
  member: -1,
});

if (isTeamOwner) {
  membersQuery = useMembers();
}

const members = computed(() => {
  const list = membersQuery?.data
    ? membersQuery.data.value?.data?.map((item: any) => ({
        id: item.id,
        name: item.name,
        email: item.email,
      }))
    : [];

  return [
    {
      id: -1,
      name: t("statistic.filter.allTeam"),
      email: t("statistic.filter.allTeam"),
    },
    ...list,
  ];
});

// methods
const updatePeriod = (value: TFilters["period"]) => {
  filters.period = value;

  setDates();
};

const updateDates = (value: TFilters["dates"]) => {
  filters.dates = value;

  resetPeriod();
};
const updateMember = (value: TFilters["member"]) => (filters.member = value);
const updateFilters = (value: TFilters) => emit("update:filters", value);
// reset to default
const resetPeriod = () => (filters.period = "custom");
const setDates = () => {
  // Step 1. Set initial date
  const now = new Date();

  if (filters.period === "month") {
    filters.dates.end = now;
    filters.dates.start = new Date(now.getFullYear(), now.getMonth(), 1);
  } else if (filters.period === "week") {
    filters.dates.end = now;
    filters.dates.start = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() - now.getDay()
    );
  }
};

// watchers
watch(
  () => props.filters,
  () => props.filters && Object.assign(filters, props.filters)
);
watch(
  () => JSON.stringify(filters),
  () => updateFilters(filters)
);

// setup
const setup = () => {
  // Step 1. Set initial date
  setDates();

  // Step 2. Update filters
  updateFilters(filters);
};
setup();
</script>

<style lang="sass" module></style>
