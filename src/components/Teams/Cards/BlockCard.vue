<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import UiSelect from "@/components/ui/Select/Select.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import BlockCardInput from "@/components/ui/InputCurrency/BlockCardInput.vue";
import CardActionResult from "@/components/Teams/Cards/CardActionResult.vue";
import ModalScreen from "@/components/ui/Modal/ModalScreen.vue";
import DepositWarning from "@/components/ui/Warning/DepositWarning.vue";
import { CardService } from "@/modules/services/card";
import { useUserAccounts } from "@/composable/useUserAccounts";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

interface Props {
  data: any;
}

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(["close"]);
const { accounts, activeAccountId, isFetching } = useUserAccounts({
  selectUsdAccount: true,
});

const resultData = ref<any>({
  icon: "card-blocked-circle",
  title: t("Success"),
  subtitle: t("team.blockCard.successBlock"),
});
const step = ref<number>(1);

// mutations
const setStep = (value: number) => (step.value = value);
const onAccountChange = (value: number) => (activeAccountId.value = value);

// actions
const deleteCard = async () => {
  const response = await CardService.block({
    urlParams: {
      id: props.data.id,
    },
    data: {
      account_id: activeAccountId.value,
    },
  });

  if (!response.status) {
    console.error("BlockCard->deleteCard error handled: ", response);
    resultData.value.icon = "cross-circle";
    resultData.value.title = t("Error");
    resultData.value.subtitle = t(response.message || "Something went srong");
  }

  setStep(2);
};
</script>

<template>
  <ModalScreen
    content-class="flex flex-col items-center gap-10"
    :loading="isFetching"
    boxed
    @close="emit('close', true)">
    <template #title>
      <span></span>
    </template>

    <template v-if="step === 1">
      <!--   Title   -->
      <div class="flex flex-col gap-3 items-center">
        <h4 class="text-center">
          {{ $t("team.blockCard.title") + " " + data.mask }}
        </h4>

        <p class="text-base font-medium text-gray-600 text-center w-10/12">
          {{
            $t("team.blockCard.subtitle1") +
            ` ${data.mask} ` +
            $t("team.blockCard.subtitle2")
          }}
        </p>
      </div>

      <!--   Accounts   -->
      <div class="flex flex-col gap-5 w-full">
        <UiSelect
          :options="accounts"
          :value="activeAccountId"
          :label="$t('team.blockCard.toAccount')"
          @change="onAccountChange">
          <template #withIcon="{ option }">
            <div class="flex items-center gap-3">
              <DynamicIcon
                class="w-5 h-5"
                :name="`${option._currency.iso_code.toLowerCase()}-circle`" />

              <span class="text-sm font-extrabold text-gray-900">
                {{
                  option._currency.iso_code + " " + $t("team.blockCard.wallet")
                }}
              </span>
            </div>
          </template>
        </UiSelect>

        <BlockCardInput
          :disabled="true"
          :max="data.account.balance - 1"
          :label="$t('team.blockCard.balanceAmount')"
          :value="'$' + (data.account.balance - (data.account.balance - 1))" />
      </div>

      <DepositWarning :text="$t('card.blockCard.warning.text')" />

      <!--   Manage row   -->
      <div class="grid grid-cols-2 gap-5 w-full">
        <UiButton
          class="col-span-1"
          type="secondary"
          :title="$t('buttons.cancel')"
          @click="emit('close')" />

        <UiButton
          class="col-span-1"
          :title="$t('team.blockCard.blockButton')"
          @click="deleteCard" />
      </div>
    </template>

    <CardActionResult
      v-if="step === 2"
      :icon="resultData.icon"
      :title="resultData.title"
      :subtitle="resultData.subtitle"
      @close="emit('close', true)" />
  </ModalScreen>
</template>

<style lang="sass" module></style>
