export type TLogger<T> = {
  disable(): void;
  enable(logLevel: T): void;
  log(
    cause: TLoggerArgs["cause"],
    reason: TLoggerArgs["reason"],
    data?: TLoggerArgs["data"]
  ): void;
  warn(
    cause: TLoggerArgs["cause"],
    reason: TLoggerArgs["reason"],
    data?: TLoggerArgs["data"]
  ): void;
  error(
    cause: TLoggerArgs["cause"],
    reason: TLoggerArgs["reason"],
    data?: TLoggerArgs["data"]
  ): void;
  debug(
    cause: TLoggerArgs["cause"],
    reason: TLoggerArgs["reason"],
    data?: TLoggerArgs["data"]
  ): void;
};

export enum LogLevel {
  None = 0,
  Error = 1,
  Warn = 2,
  Verbose = 3,
  Debug = 4,
}

export type TLoggerArgs = {
  cause: string;
  reason: string;
  data?: {
    [key: string]: string | number | boolean;
  };
};

export type TLoggerInjectable = TLogger<LogLevel>;
