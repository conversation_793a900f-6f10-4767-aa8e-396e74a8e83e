import { LAYOUT_CONTENT_SCROLLBOX_ID } from "@/constants/layout_content_scrollbox_id";

/**
 * Scrolls element recieved from DOM by id
 *
 * @category Helpers
 *
 * @param {number} yTarget - Position for scroll to. Using in 'top' option
 * @param {string} scrollboxId - ID of DOM element
 * @param {ScrollToOptions} options - WebAPI scrollTo method options
 * @returns {void} - Promise
 */
export function scrollToSmooth(
  yTarget: number,
  scrollboxId: string = LAYOUT_CONTENT_SCROLLBOX_ID,
  options: ScrollOptions = {}
) {
  const scrollBox = document.getElementById(scrollboxId);
  if (!scrollBox) {
    console.warn("Scrollbox doesn't exist");
    return;
  }

  return new Promise<void>((resolve) => {
    scrollBox.scrollTo({
      top: yTarget,
      behavior: "smooth",
      ...options,
    });

    let lastPosition = -1;
    let sameCount = 0;

    const checkIfDone = () => {
      const currentPosition = scrollBox.scrollTop;

      if (Math.abs(currentPosition - yTarget) < 2) {
        resolve();
        return;
      }

      if (currentPosition === lastPosition) {
        sameCount++;
        if (sameCount > 10) {
          resolve();
          return;
        }
      } else {
        sameCount = 0;
      }

      lastPosition = currentPosition;
      requestAnimationFrame(checkIfDone);
    };

    requestAnimationFrame(checkIfDone);
  });
}
