<script setup lang="ts">
import UiButton from "@/components/ui/Button/Button.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import ModalScreen from "@/components/ui/Modal/ModalScreen.vue";

import { computed } from "vue";
import { useI18n } from "vue-i18n";

interface Props {
  type?: string;
  error?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: "error",
});

const emit = defineEmits(["close"]);

const close = () => emit("close");

const { t } = useI18n();

const title = computed(() => {
  const alertTitle = (type: string) => {
    switch (type) {
      case "error":
        return t("Error");
      case "wait":
        return t("kyc.status.wait");
      default:
        return "";
    }
  };

  return alertTitle(props.type);
});
</script>

<template>
  <ModalScreen
    :closable="false"
    centered
    @close="close">
    <div :class="$style.root">
      <DynamicIcon
        path="alerts"
        :name="props.type" />

      <h4>{{ title }}</h4>

      <p :class="$style.root__error">
        {{ props.error }}
      </p>

      <UiButton
        type="secondary"
        :title="$t('Close')"
        :class="$style.root__button"
        data-cy="close"
        @click="close" />
    </div>
  </ModalScreen>
</template>

<style lang="scss" module>
.root {
  @apply font-granate max-w-[440px] mx-auto text-center flex flex-col justify-center items-center;

  & h4 {
    @apply mt-10 font-normal;
  }

  &__button {
    @apply w-fit font-normal text-[15px] py-3 px-12;
  }

  &__error {
    @apply pb-4 text-error-base;
  }
}
</style>
