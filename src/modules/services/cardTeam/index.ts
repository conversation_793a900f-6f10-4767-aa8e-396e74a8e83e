import { defineService } from "@modules/services/base";
import type { TPayloads, TResponses } from "@modules/services/cardTeam/types";
import { ServiceEndpoint } from "@modules/services/cardTeam/types";

const useCardService = defineService<TResponses, TPayloads>(
  "cardsTeamServiceConfig"
);

const list = (v: TPayloads[ServiceEndpoint.LIST]) =>
  useCardService<
    TResponses[ServiceEndpoint.LIST],
    TPayloads[ServiceEndpoint.LIST]
  >(ServiceEndpoint.LIST, v);

const update = (v: TPayloads[ServiceEndpoint.UPDATE]) =>
  useCardService<
    TResponses[ServiceEndpoint.UPDATE],
    TPayloads[ServiceEndpoint.UPDATE]
  >(ServiceEndpoint.UPDATE, v);

const info = (v: TPayloads[ServiceEndpoint.INFO]) =>
  useCardService<
    TResponses[ServiceEndpoint.INFO],
    TPayloads[ServiceEndpoint.INFO]
  >(ServiceEndpoint.INFO, v);

const block = (v: TPayloads[ServiceEndpoint.BLOCK_CARD]) =>
  useCardService<
    TResponses[ServiceEndpoint.BLOCK_CARD],
    TPayloads[ServiceEndpoint.BLOCK_CARD]
  >(ServiceEndpoint.BLOCK_CARD, v);

const bins = (v: TPayloads[ServiceEndpoint.GET_BINS]) =>
  useCardService<
    TResponses[ServiceEndpoint.GET_BINS],
    TPayloads[ServiceEndpoint.GET_BINS]
  >(ServiceEndpoint.GET_BINS, v);

const autoBuyShow = (v: TPayloads[ServiceEndpoint.AUTO_BUY_SHOW] = undefined) =>
  useCardService<
    TResponses[ServiceEndpoint.AUTO_BUY_SHOW],
    TPayloads[ServiceEndpoint.AUTO_BUY_SHOW]
  >(ServiceEndpoint.AUTO_BUY_SHOW, v);

const createRefill = (v: TPayloads[ServiceEndpoint.CREATE_REFILL]) =>
  useCardService<
    TResponses[ServiceEndpoint.CREATE_REFILL],
    TPayloads[ServiceEndpoint.CREATE_REFILL]
  >(ServiceEndpoint.CREATE_REFILL, v);

const multiBuyCheck = (v: TPayloads[ServiceEndpoint.MULTI_BUY_CHECK]) =>
  useCardService<
    TResponses[ServiceEndpoint.MULTI_BUY_CHECK],
    TPayloads[ServiceEndpoint.MULTI_BUY_CHECK]
  >(ServiceEndpoint.MULTI_BUY_CHECK, v);
const multiBuy = (v: TPayloads[ServiceEndpoint.MULTI_BUY]) =>
  useCardService<
    TResponses[ServiceEndpoint.MULTI_BUY],
    TPayloads[ServiceEndpoint.MULTI_BUY]
  >(ServiceEndpoint.MULTI_BUY, v);

const checkAutoBuyTransaction = (
  v: TPayloads[ServiceEndpoint.CHECK_AUTO_BUY_TRANSACTION]
) =>
  useCardService<
    TResponses[ServiceEndpoint.CHECK_AUTO_BUY_TRANSACTION],
    TPayloads[ServiceEndpoint.CHECK_AUTO_BUY_TRANSACTION]
  >(ServiceEndpoint.CHECK_AUTO_BUY_TRANSACTION, v);

const updateRefill = (v: TPayloads[ServiceEndpoint.UPDATE_REFILL]) =>
  useCardService<
    TResponses[ServiceEndpoint.UPDATE_REFILL],
    TPayloads[ServiceEndpoint.UPDATE_REFILL]
  >(ServiceEndpoint.UPDATE_REFILL, v);

export const CardService = {
  list,
  info,
  block,
  bins,
  autoBuyShow,
  createRefill,
  multiBuy,
  multiBuyCheck,
  checkAutoBuyTransaction,
  updateRefill,
  update,
};
