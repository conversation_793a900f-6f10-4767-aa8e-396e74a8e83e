<template>
  <div
    :class="[
      $style.divider,
      {
        [$style.divider__label]: props.label,
        [$style.divider__solid]: props.type === 'solid',
        [$style.divider__dashed]: props.type === 'dashed',
      },
    ]">
    <template v-if="label">
      {{ label }}
    </template>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  label?: string;
  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  label: "",
  type: "solid",
});
</script>

<style lang="scss" module>
.divider {
  @apply flex items-center text-fg-tertiary;

  &__solid {
    &:before,
    &:after {
      @apply bg-bg-level-1 flex-grow;
      content: "";
      height: 1px;
    }
  }

  &__dashed {
    &:before,
    &:after {
      @apply border-t border-fg-tertiary border-dashed flex-grow opacity-40;
      content: "";
    }
  }

  &__label {
    &:before {
      @apply mr-3;
    }

    &:after {
      @apply ml-3;
    }
  }
}
</style>
