<script setup lang="ts">
import UiButton from "@/components/ui/Button/Button.vue";
import { onMounted, onUnmounted, ref } from "vue";

interface Props {
  transition?: string;
  closable?: boolean;
  centered?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  transition: "fade-slide-up",
  closable: true,
  centered: false,
});
const emit = defineEmits<{ (event: "close"): void }>();

const isReady = ref(false);

const close = () => {
  if (props.closable) {
    isReady.value = false;
    setTimeout(() => {
      emit("close");
    }, 300);
  } else {
    emit("close");
  }
};

onMounted(() => {
  isReady.value = true;
  document.documentElement.classList.add("no-scrolled");
  document.documentElement.classList.add(props.transition);
  window.addEventListener("keydown", (e) => {
    if (e.key === "Escape") {
      close();
    }
  });
});

onUnmounted(() => {
  isReady.value = false;
  document.documentElement.classList.remove("no-scrolled");
  document.documentElement.classList.remove(props.transition);
});
</script>

<template>
  <transition
    :name="transition"
    appear>
    <div
      v-if="isReady"
      :class="$style.root"
      data-cy="modal-transaction">
      <div :class="$style.container">
        <div
          :class="{
            [$style.wrapper]: true,
            [$style.wrapper__centered]: props.centered,
          }">
          <div
            v-if="$slots.title || closable"
            :class="$style.header">
            <h4 v-if="$slots.title">
              <slot name="title" />
            </h4>
            <UiButton
              v-if="closable"
              type="secondary"
              size="small"
              :title="$t('Cancel')"
              @click="close"
              data-cy="btn-close" />
          </div>
          <div :class="$style.content">
            <template v-if="$slots.fromTitle">
              <h6>
                <slot name="fromTitle" />
              </h6>
            </template>

            <div
              v-if="$slots.from"
              :class="$style.info">
              <div :class="$style['info-wrapper']">
                <slot name="from" />
              </div>
              <slot name="fromAlert" />
            </div>

            <template v-if="$slots.toTitle">
              <h6>
                <slot name="toTitle" />
              </h6>
            </template>

            <div
              v-if="$slots.to"
              :class="$style.info">
              <div :class="$style['info-wrapper']">
                <slot name="to" />
              </div>
              <slot name="toAlert" />
            </div>

            <template v-if="$slots.amountTitle">
              <h6>
                <slot name="amountTitle" />
              </h6>
            </template>

            <div
              v-if="$slots.amount"
              :class="$style.info">
              <div :class="$style['info-wrapper']">
                <slot name="amount" />
              </div>
              <slot name="amountAlert" />
            </div>

            <div
              v-if="$slots.default"
              :class="$style.steps">
              <slot />
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<style lang="scss" module>
.root {
  @apply fixed top-0 bottom-0 left-0 right-0 w-full h-full bg-white z-6;
}

.container {
  @apply w-full h-full overflow-y-auto;
}

.wrapper {
  @apply w-full py-5 px-4 mx-auto max-w-[472px] flex flex-col gap-5;
  @apply md:py-10;

  &__centered {
    @apply min-h-full justify-center;
  }
}

.header {
  @apply flex flex-wrap gap-4 items-center justify-between;

  & h4 {
    @apply order-2;
    @apply md:order-1;
  }

  & button {
    @apply w-full order-1;
    @apply md:w-fit md:order-2;
  }
}

.content {
  @apply flex flex-col;

  & h4,
  h6 {
    @apply mb-5;
  }
}

.info {
  @apply flex flex-col gap-5 mb-10;

  &-wrapper {
    @apply bg-greyscale-100 p-5 rounded-lg;
  }
}

.steps {
}
</style>
