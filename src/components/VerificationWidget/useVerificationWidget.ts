import { useVerificationState } from "@/composable";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { upperFirst } from "lodash";
import { currencyFormatter } from "@/helpers/currencyFormatter";
import { useUserStore } from "@/stores/user";
import { EVerificationStatus } from "@/types/verification/verification";

export enum EVerificationWidgetState {
  NOT_VERIFIED = "NOT_VERIFIED",
  VERIFIED = "VERIFIED",
  IN_PROGRESS = "IN_PROGRESS",
  ERROR = "ERROR",
  REJECTED = "REJECTED",
  UNLIMITED = "UNLIMITED",
}

type TVerificationWidget = {
  text: string;
  subtext: string;
  icon: string;
  iconColor: string;
  color: string;
};

export const useVerificationWidget = () => {
  const {
    userVerificationTier: verificationStep,
    verificationActualData,
    verificationStatus,
    isVerified,
    isComplete,
  } = useVerificationState();
  const { isTeamMember } = useUserStore();

  const { t } = useI18n();

  const remainingDeposit = computed<number>(() => {
    return Number(verificationActualData.value?.data?.remaining_deposit ?? 0);
  });

  const verificationWidgetState = computed<EVerificationWidgetState>(() => {
    if (!isVerified.value) {
      return EVerificationWidgetState.NOT_VERIFIED;
    }

    if (verificationStep.value === "unlimited") {
      return EVerificationWidgetState.UNLIMITED;
    }

    if (verificationStatus.value === EVerificationStatus.PROCESSING) {
      return EVerificationWidgetState.IN_PROGRESS;
    }

    if (verificationStatus.value === EVerificationStatus.ERROR) {
      return EVerificationWidgetState.ERROR;
    }

    if (verificationStatus.value === EVerificationStatus.REJECTED) {
      return EVerificationWidgetState.REJECTED;
    }

    return EVerificationWidgetState.VERIFIED;
  });

  const getWidgetByState = (
    state: EVerificationWidgetState
  ): TVerificationWidget => {
    const defaultWidget = {
      text: "",
      subtext: "",
      icon: "verify_outlined",
      iconColor: "text-fg-green",
      color: "bg-bg-level-1",
    };

    switch (state) {
      case EVerificationWidgetState.VERIFIED:
        defaultWidget.text = t(
          "verification.verification-widget-state.verified-text",
          {
            s: upperFirst(verificationStep.value || ""),
          }
        );
        defaultWidget.subtext = t(
          "verification.verification-widget-state.remaining-deposit",
          {
            d: currencyFormatter(remainingDeposit.value, "USD", true, 0),
          }
        );
        defaultWidget.icon = "verify_outlined";
        defaultWidget.iconColor = "text-fg-green";
        defaultWidget.color = "bg-bg-level-1";
        break;
      case EVerificationWidgetState.ERROR:
        defaultWidget.text = t(
          "verification.verification-widget-state.error-text"
        );
        defaultWidget.subtext = t(
          "verification.verification-widget-state.error-subtext"
        );
        defaultWidget.icon = "verify_not_outlined";
        defaultWidget.iconColor = "text-fg-orange";
        defaultWidget.color = "bg-bg-orange-light";
        break;
      case EVerificationWidgetState.IN_PROGRESS:
        defaultWidget.text = t(
          "verification.verification-widget-state.in-progress-text"
        );
        defaultWidget.subtext = t(
          "verification.verification-widget-state.in-progress-subtext"
        );
        defaultWidget.icon = "clock-outlined";
        defaultWidget.iconColor = "text-fg-orange";
        defaultWidget.color = "bg-bg-level-1";
        break;
    }

    return defaultWidget;
  };

  const verificationWidget = computed<TVerificationWidget>(() => {
    return getWidgetByState(verificationWidgetState.value);
  });

  const isVerificationWidgetVisible = computed(() => {
    const conditions = [
      !isTeamMember,
      !isComplete.value,
      verificationWidgetState.value !== EVerificationWidgetState.REJECTED,
      (remainingDeposit.value > 0 && isVerified.value) || !isVerified.value,
    ];

    return conditions.every((condition) => condition);
  });

  return {
    verificationStep,
    verificationWidgetState,
    verificationWidget,
    isVerificationWidgetVisible,
  };
};
