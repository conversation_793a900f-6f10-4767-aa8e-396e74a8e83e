<template>
  <div :class="$style.ReferralStats">
    <div
      class="order-1 p-3 rounded-xl bg-white text-greyscale-600 w-fit border border-greyscale-200">
      <UserAlt class="w-8 h-8" />
    </div>
    <div
      class="order-2 md:order-3 xl:order-2 col-span-2 md:col-span-1 xl:col-span-2 flex flex-col">
      <span class="ext-greyscale-600 text-sm font-medium"
        >{{ $t("referral.stats.total") }}:</span
      >
      <span class="text-h5 font-extrabold">{{
        earnings.currencySymbol + earnings.total_earnings
      }}</span>
    </div>
    <!--    <div class="order-3 md:order-5 xl:order-3 col-span-2 md:col-span-1 xl:col-span-2 flex flex-col">-->
    <!--      <span class="ext-greyscale-600 text-sm font-medium"-->
    <!--      >{{ $t('Clicks on your link')}}:</span-->
    <!--      >-->
    <!--      <span class="text-h5 font-extrabold">0</span>-->
    <!--    </div>-->
    <div class="rder-6 md:order-2 xl:order-6 flex flex-col">
      <span class="ext-greyscale-600 text-sm font-medium">
        {{ $t("Issued Cards") }}:</span
      >
      <span class="text-h5 font-extrabold"
        >{{ earnings.total_referral_cards }}
      </span>
    </div>
    <div class="order-7 md:order-4 xl:order-7 flex flex-col">
      <span class="ext-greyscale-600 text-sm font-medium">
        {{ $t("Registrations") }}:</span
      >
      <span class="text-h5 font-extrabold">{{ referrals.length }}</span>
    </div>
    <div class="order-8 col-span-2 md:col-span-1 xl:col-span-2 flex flex-col">
      <span class="text-greyscale-600 text-sm font-medium">
        {{ $t("Transaction rewards") }}(Tether):</span
      >
      <span class="text-h5 font-extrabold">0</span>
    </div>
  </div>
</template>

<script>
import UserAlt from "@/assets/svg/icon/users-alt.svg";

export default {
  name: "ReferralStats",
  components: { UserAlt },
  props: {
    earnings: {
      type: Object,
      default: () => ({}),
    },
    referrals: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style lang="scss" module>
.ReferralStats {
  @apply grid grid-cols-2 gap-x-6 gap-y-2 w-auto bg-greyscale-100 p-5 rounded-2xl border border-greyscale-900 xl:flex xl:items-center justify-between;
}
</style>
