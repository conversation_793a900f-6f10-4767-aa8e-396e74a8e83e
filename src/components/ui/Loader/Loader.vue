<template>
  <div :class="[$style.root, padding && $style.withPadding, width]">
    <DynamicIcon
      name="loader"
      :class="$style.loader" />
  </div>
</template>

<script lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";

export default {
  name: "Ui<PERSON>oader",
  components: { DynamicIcon },
  props: {
    padding: {
      type: Boolean,
      default: true,
    },
    width: {
      type: String,
      default: "w-full",
    },
  },
};
</script>

<style lang="scss" module>
.root {
  @apply flex items-center justify-center;
}

.withPadding {
  @apply p-4;
}

.loader {
  @apply w-10;
}
</style>

<style>
@keyframes loader-animation-svg {
  0% {
    transform: translate(-108.3px, 0px);
    animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
  }

  50% {
    transform: translate(0px, 0px);
    animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
  }

  100% {
    transform: translate(-108.3px, 0px);
  }
}

@keyframes loader-animation-svg2 {
  0% {
    transform: translate(105.6px, 0.5px);
    animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
  }

  50% {
    transform: translate(0px, 0.5px);
    animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
  }

  100% {
    transform: translate(105.6px, 0.5px);
  }
}
</style>
