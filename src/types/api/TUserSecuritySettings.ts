const NotifyServiceIds = [
  "1", // tg
  "2", // email
  "3", // database
  "4", // web-push
  "5", // whatsapp
] as const;

export type TNotifyServiceId = (typeof NotifyServiceIds)[number];

export type TUserTwoFactorAuthProvider = {
  bot: string;
  code: string;
  command: string;
  activated: boolean;
};

export type TUserSecuritySettings = {
  "2fa_active": boolean;
  onesignal_hash: string;
  notify_services: Record<TNotifyServiceId, number[]>;
  telegram: TUserTwoFactorAuthProvider;
  whatsapp: TUserTwoFactorAuthProvider;
};
