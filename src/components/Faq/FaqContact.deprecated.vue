<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import { useSupportManager } from "@/composable";
import { openLink } from "@/helpers/events";

const { whatsAppLink, telegramLink, isSuccessLoadData } = useSupportManager();

const openSupport = (key: "telegram" | "skype" | "whatsapp") => {
  if (key === "telegram") {
    openLink(`${telegramLink.value}`, true);
  }

  if (key === "whatsapp") {
    openLink(`${whatsAppLink.value}`, true);
  }
};
</script>
<template>
  <div
    name="support"
    class="flex flex-col gap-3 w-fit">
    <h6>{{ $t("Connect with us") }}:</h6>

    <div
      v-if="isSuccessLoadData"
      class="grid grid-cols-2 gap-3">
      <UiButton
        type="social"
        title="Telegram"
        icon="telegram-color-circle"
        @click="openSupport('telegram')" />
      <UiButton
        type="social"
        title="WhatsApp"
        icon="whatsapp_colored"
        @click="openSupport('whatsapp')" />
    </div>
  </div>
</template>
